{"name": "client", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "vite"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@vitejs/plugin-vue": "^5.2.4", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.3", "axios": "^1.9.0", "element-plus": "^2.9.10", "pinia": "^3.0.2", "vite": "^6.3.5", "vue": "^3.5.14", "vue-router": "^4.5.1"}}