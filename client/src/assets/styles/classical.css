/* 古典风格全局样式 */

/* 导入中文字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;600;700&display=swap');

/* CSS变量定义 - 古典色彩方案 */
:root {
  /* 主色调 - 基于中国传统色彩 */
  --primary-gold: #daa520;        /* 金色 */
  --secondary-gold: #cd853f;      /* 秋香色 */
  --dark-brown: #8b4513;          /* 深棕色 */
  --light-brown: #a0522d;         /* 浅棕色 */
  --paper-white: #faf7f0;         /* 宣纸白 */
  --paper-cream: #f5deb3;         /* 米色 */
  --ink-black: #2c1810;           /* 墨黑 */
  --wood-brown: #4a2c1a;          /* 木色 */
  
  /* 背景渐变 */
  --bg-gradient: linear-gradient(135deg, #f5f1eb 0%, #ede4d3 50%, #e8dcc0 100%);
  --sidebar-gradient: linear-gradient(180deg, #2c1810 0%, #4a2c1a 50%, #2c1810 100%);
  --header-gradient: linear-gradient(90deg, #faf7f0 0%, #f0ead6 100%);
  
  /* 阴影效果 */
  --shadow-light: 0 2px 8px rgba(218, 165, 32, 0.15);
  --shadow-medium: 0 4px 16px rgba(218, 165, 32, 0.25);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);
  
  /* 边框 */
  --border-gold: 2px solid var(--primary-gold);
  --border-light: 1px solid #e6d7c3;
  
  /* 字体 */
  --font-classical: 'Noto Serif SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', serif;
  --font-modern: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

/* 全局字体设置 */
* {
  font-family: var(--font-classical);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--paper-cream);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary-gold), var(--secondary-gold));
  border-radius: 4px;
  box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--secondary-gold), var(--primary-gold));
}

/* 选中文本样式 */
::selection {
  background: var(--primary-gold);
  color: white;
}

::-moz-selection {
  background: var(--primary-gold);
  color: white;
}

/* 古典装饰元素 */
.classical-decoration {
  position: relative;
}

.classical-decoration::before,
.classical-decoration::after {
  content: '❦';
  position: absolute;
  color: var(--primary-gold);
  font-size: 1.2rem;
  opacity: 0.6;
}

.classical-decoration::before {
  left: -30px;
  top: 50%;
  transform: translateY(-50%);
}

.classical-decoration::after {
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
}

/* 古典分割线 */
.classical-divider {
  height: 2px;
  background: linear-gradient(to right, transparent, var(--primary-gold), transparent);
  margin: 30px 0;
  position: relative;
}

.classical-divider::before {
  content: '◆';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: var(--paper-white);
  color: var(--primary-gold);
  padding: 0 15px;
  font-size: 1rem;
}

/* 古典按钮样式 */
.classical-button {
  background: linear-gradient(145deg, var(--paper-white), var(--paper-cream));
  border: var(--border-gold);
  color: var(--dark-brown);
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.classical-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.2), transparent);
  transition: left 0.5s;
}

.classical-button:hover::before {
  left: 100%;
}

.classical-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--secondary-gold);
}

/* 古典卡片样式 */
.classical-card {
  background: linear-gradient(145deg, var(--paper-white), #f8f4e6);
  border: var(--border-light);
  border-radius: 16px;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.classical-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary-gold), var(--secondary-gold), var(--primary-gold));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.classical-card:hover::before {
  transform: scaleX(1);
}

.classical-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-gold);
}

/* 古典文本样式 */
.classical-title {
  color: var(--dark-brown);
  font-weight: 600;
  letter-spacing: 2px;
  position: relative;
}

.classical-subtitle {
  color: var(--light-brown);
  font-weight: 400;
  opacity: 0.8;
  letter-spacing: 1px;
}

.classical-text {
  color: var(--light-brown);
  line-height: 1.8;
  letter-spacing: 0.5px;
}

/* 古典输入框样式 */
.classical-input {
  background: var(--paper-white);
  border: var(--border-light);
  border-radius: 8px;
  padding: 12px 16px;
  color: var(--dark-brown);
  font-family: var(--font-classical);
  transition: all 0.3s ease;
}

.classical-input:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.1);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes inkWash {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

/* 响应式辅助类 */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.sparkle-animation {
  animation: sparkle 2s ease-in-out infinite alternate;
}

.ink-wash-animation {
  animation: inkWash 3s ease-in-out infinite;
}
