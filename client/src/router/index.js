import { createRouter, createWebHistory } from 'vue-router';
import WorldSettingsView from '../views/WorldSettings.vue';
import authService from '../services/auth';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    meta: { requiresAuth: true },
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
  },
  {
    meta: { requiresAuth: true },
    path: '/projects',
    name: 'Projects',
    component: () => import('../views/Projects.vue'),
  },
  {
    meta: { requiresAuth: true },
    path: '/world-settings/:projectId',
    name: 'WorldSettings',
    component: WorldSettingsView,
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/characters/:projectId',
    name: 'Characters',
    component: () => import('../views/Characters.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/outlines/:projectId',
    name: 'Outlines',
    component: () => import('../views/Outlines.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/chapters/:projectId',
    name: 'Chapters',
    component: () => import('../views/Chapters.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/volumes/:projectId',
    name: 'Volumes',
    component: () => import('../views/Volumes.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/volume-chapter-groups/:projectId/:volumeId',
    name: 'VolumeChapterGroups',
    component: () => import('../views/VolumeChapterGroups.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/clues/:projectId',
    name: 'Clues',
    component: () => import('../views/Clues.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/prompt-generator',
    name: 'PromptGenerator',
    component: () => import('../views/PromptGenerator.vue'),
  },
  {
    meta: { requiresAuth: true },
    path: '/novel-reader/:projectId',
    name: 'NovelReader',
    component: () => import('../views/NovelReader.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true, requiresAdmin: true },
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('../views/UserManagement.vue'),
  },
  {
    meta: { requiresAuth: true },
    path: '/ai-config',
    name: 'AIConfig',
    component: () => import('../views/AIConfig.vue'),
  },
  // 工作流相关路由
  {
    meta: { requiresAuth: true },
    path: '/workflow-templates',
    name: 'WorkflowTemplates',
    component: () => import('../views/WorkflowTemplates.vue'),
  },
  {
    meta: { requiresAuth: true },
    path: '/workflow-designer/:id?',
    name: 'WorkflowDesigner',
    component: () => import('../views/WorkflowDesigner.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/workflow-execution/:executionId?',
    name: 'WorkflowExecution',
    component: () => import('../views/WorkflowExecution.vue'),
    props: true,
  },
  {
    meta: { requiresAuth: true },
    path: '/workflow-execution-history',
    name: 'WorkflowExecutionHistory',
    component: () => import('../views/WorkflowExecutionHistory.vue'),
  },
  {
    meta: { requiresAuth: true },
    path: '/workflow-statistics',
    name: 'WorkflowStatistics',
    component: () => import('../views/WorkflowStatistics.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin);
  const isAuthenticated = authService.isAuthenticated();

  if (requiresAuth && !isAuthenticated) {
    // 如果需要认证但用户未登录，重定向到登录页面
    next('/login');
  } else if (to.path === '/login' && isAuthenticated) {
    // 如果用户已登录但尝试访问登录页面，重定向到首页
    next('/');
  } else if (requiresAdmin && isAuthenticated) {
    // 检查是否需要admin权限
    const user = authService.getUser();
    if (!user || user.role !== 'admin') {
      // 如果不是admin用户，显示错误并重定向到首页
      next('/');
      // 可以在这里显示权限不足的提示
      setTimeout(() => {
        import('element-plus').then(({ ElMessage }) => {
          ElMessage.error('您没有权限访问该页面');
        });
      }, 100);
      return;
    }
    next();
  } else {
    // 其他情况正常导航
    next();
  }
});

export default router;
