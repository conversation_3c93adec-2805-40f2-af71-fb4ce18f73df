<template>
  <div class="novel-reader" :class="[`theme-${currentTheme}`, { 'fullscreen': isFullscreen }]">
    <!-- 顶部工具栏 -->
    <div class="reader-toolbar"
         v-show="!isFullscreen || showToolbar"
         :class="{ 'hidden': isFullscreen && !showToolbar }">
      <div class="toolbar-left">
        <el-button icon="ArrowLeft" @click="goBack" size="small">返回</el-button>
        <span class="novel-title">{{ novelData?.project?.name }}</span>
      </div>
      <div class="toolbar-center">
        <span class="chapter-info" v-if="chapters.length > 0">
          第{{ currentChapterIndex + 1 }}章 / 共{{ chapters.length }}章
        </span>
        <span class="chapter-info" v-else>
          暂无章节
        </span>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="showSidebar = !showSidebar" icon="List">目录</el-button>
          <el-button @click="showSettings = true" icon="Setting">设置</el-button>
          <el-button @click="toggleFullscreen" :icon="isFullscreen ? 'FullScreen' : 'Aim'">
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要阅读区域 -->
    <div class="reader-main" @click="handleMainClick">
      <!-- 移动端遮罩层 -->
      <div
        v-if="isMobile && showSidebar"
        class="sidebar-overlay"
        @click="showSidebar = false"
      ></div>
      <!-- 左侧导航区域 -->
      <div class="reader-sidebar" v-show="showSidebar" :class="{ show: showSidebar }">
        <div class="sidebar-header">
          <h3>章节目录</h3>
          <el-button @click="showSidebar = false" icon="Close" size="small" text></el-button>
        </div>
        <div class="chapter-list">
          <div v-if="chapters.length === 0" class="no-chapters">
            暂无章节内容
          </div>
          <div
            v-for="(chapter, index) in chapters"
            :key="chapter.id"
            class="chapter-item"
            :class="{ active: index === currentChapterIndex }"
            @click="goToChapter(index)"
          >
            <span class="chapter-number">第{{ index + 1 }}章</span>
            <span class="chapter-title">{{ chapter.title }}</span>
            <span v-if="chapter.versions && chapter.versions.length > 1" class="version-badge">
              {{ chapter.versions.length }}版本
            </span>
          </div>
        </div>
      </div>

      <!-- 阅读内容区域 -->
      <div class="reader-content">
        <div class="book-page" v-if="currentChapter" :style="pageStyles">
          <div class="chapter-header">
            <h1 class="chapter-title">{{ currentChapter.title }}</h1>
            <div class="chapter-meta">
              <span>第{{ currentChapterIndex + 1 }}章</span>
              <!-- 版本选择器 -->
              <div class="version-selector" v-if="currentChapterVersions.length > 1">
                <el-select
                  v-model="currentVersionId"
                  @change="handleVersionChange"
                  size="small"
                  style="width: 150px;"
                >
                  <el-option
                    v-for="version in currentChapterVersions"
                    :key="version.id"
                    :label="`版本 ${version.versionNumber}`"
                    :value="version.id"
                  >
                    <span>版本 {{ version.versionNumber }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px;">
                      {{ formatVersionDate(version.createdAt) }}
                    </span>
                  </el-option>
                </el-select>
              </div>
            </div>
          </div>
          <div class="chapter-content" :style="contentStyles" v-html="formattedContent"></div>

          <!-- 章节导航按钮 -->
          <div class="chapter-navigation" v-if="chapters.length > 1">
            <div class="nav-buttons">
              <el-button
                :disabled="currentChapterIndex <= 0"
                @click="prevChapter"
                icon="ArrowLeft"
                size="large"
              >
                上一章
              </el-button>
              <div class="chapter-info">
                {{ currentChapterIndex + 1 }} / {{ chapters.length }}
              </div>
              <el-button
                :disabled="currentChapterIndex >= chapters.length - 1"
                @click="nextChapter"
                icon="ArrowRight"
                size="large"
              >
                下一章
              </el-button>
            </div>
          </div>
        </div>
        <div class="no-content" v-else>
          <el-empty description="暂无章节内容" />
        </div>
      </div>

      <!-- 翻页按钮 -->
      <div class="page-navigation" v-if="chapters.length > 0">
        <el-button
          class="nav-btn nav-prev"
          @click="prevChapter"
          :disabled="currentChapterIndex <= 0"
          icon="ArrowLeft"
          size="large"
          circle
        ></el-button>
        <el-button
          class="nav-btn nav-next"
          @click="nextChapter"
          :disabled="currentChapterIndex >= chapters.length - 1"
          icon="ArrowRight"
          size="large"
          circle
        ></el-button>
      </div>
    </div>

    <!-- 底部进度条 -->
    <div class="reader-progress"
         v-show="!isFullscreen || showToolbar"
         v-if="chapters.length > 0"
         :class="{ 'hidden': isFullscreen && !showToolbar }">
      <el-slider
        v-model="currentChapterIndex"
        :max="Math.max(0, chapters.length - 1)"
        :show-tooltip="false"
        @change="goToChapter"
      />
      <div class="progress-info">
        阅读进度: {{ Math.round(((currentChapterIndex + 1) / chapters.length) * 100) }}%
      </div>
    </div>

    <!-- 设置面板 -->
    <el-drawer v-model="showSettings" title="阅读设置" direction="rtl" size="300px">
      <div class="settings-panel">
        <!-- 主题设置 -->
        <div class="setting-group">
          <h4>阅读主题</h4>
          <div class="theme-options">
            <div
              v-for="theme in themes"
              :key="theme.value"
              class="theme-option"
              :class="{ active: currentTheme === theme.value }"
              @click="currentTheme = theme.value"
            >
              <div class="theme-preview" :style="{ backgroundColor: theme.bg, color: theme.color }">
                {{ theme.name }}
              </div>
            </div>
          </div>
        </div>

        <!-- 字体设置 -->
        <div class="setting-group">
          <h4>字体大小</h4>
          <el-slider v-model="fontSize" :min="12" :max="24" :step="1" show-input />
        </div>

        <!-- 行间距设置 -->
        <div class="setting-group">
          <h4>行间距</h4>
          <el-slider v-model="lineHeight" :min="1.2" :max="2.5" :step="0.1" show-input />
        </div>

        <!-- 页面宽度设置 -->
        <div class="setting-group">
          <h4>页面宽度</h4>
          <el-slider v-model="pageWidth" :min="600" :max="1200" :step="50" show-input />
        </div>

        <!-- 其他设置 -->
        <div class="setting-group">
          <h4>其他设置</h4>
          <div class="setting-item">
            <el-switch v-model="showSidebar" active-text="显示目录" />
            <span v-if="isMobile" class="setting-tip">（移动端点击目录按钮切换）</span>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading-spinner size="large" />
      <p>正在加载小说内容...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { projectAPI } from '../services/api'

const route = useRoute()
const router = useRouter()
const projectId = route.params.projectId

// 响应式数据
const loading = ref(true)
const novelData = ref(null)
const chapters = ref([])
const currentChapterIndex = ref(0)
const currentVersionId = ref(null)
const showSettings = ref(false)
const showSidebar = ref(true)
const showToolbar = ref(true)
const isFullscreen = ref(false)
const isMobile = ref(window.innerWidth <= 768)

// 阅读设置
const currentTheme = ref('light')
const fontSize = ref(16)
const lineHeight = ref(1.6)
const pageWidth = ref(800)

// 主题配置
const themes = [
  { name: '明亮', value: 'light', bg: '#ffffff', color: '#333333' },
  { name: '护眼', value: 'green', bg: '#f0f8e8', color: '#2d5016' },
  { name: '夜间', value: 'dark', bg: '#1a1a1a', color: '#e0e0e0' },
  { name: '羊皮纸', value: 'paper', bg: '#f4f1e8', color: '#5d4e37' }
]

// 计算属性
const currentChapter = computed(() => {
  return chapters.value[currentChapterIndex.value] || null
})

const currentChapterVersions = computed(() => {
  if (!currentChapter.value || !currentChapter.value.versions) {
    return []
  }
  return currentChapter.value.versions.sort((a, b) => b.versionNumber - a.versionNumber)
})

const currentVersionContent = computed(() => {
  if (!currentChapter.value) return ''

  // 如果选择了特定版本，使用版本内容
  if (currentVersionId.value && currentChapterVersions.value.length > 0) {
    const selectedVersion = currentChapterVersions.value.find(v => v.id === currentVersionId.value)
    if (selectedVersion) {
      return selectedVersion.content || ''
    }
  }

  // 否则使用章节的当前内容
  return currentChapter.value.content || ''
})

const formattedContent = computed(() => {
  const content = currentVersionContent.value
  if (!content) return '<p>暂无内容</p>'

  // 将换行符转换为段落标签
  return content
    .split('\n')
    .filter(line => line.trim())
    .map(line => `<p>${line.trim()}</p>`)
    .join('')
})

const contentStyles = computed(() => ({
  fontSize: `${fontSize.value}px`,
  lineHeight: lineHeight.value
}))

const pageStyles = computed(() => ({
  maxWidth: `${pageWidth.value}px`
}))

// 方法
const goBack = () => {
  router.go(-1)
}

const fetchNovelData = async () => {
  try {
    loading.value = true
    const data = await projectAPI.getProjectPreview(projectId)
    novelData.value = data
    chapters.value = data.chapters || []

    if (chapters.value.length === 0) {
      ElMessage.warning('该项目暂无章节内容')
    } else {
      // 初始化第一章的版本选择
      resetToLatestVersion()
    }
  } catch (error) {
    console.error('获取小说数据失败:', error)
    ElMessage.error('获取小说数据失败')
  } finally {
    loading.value = false
  }
}

const goToChapter = (index) => {
  if (chapters.value.length === 0) return

  // 确保索引在有效范围内
  const validIndex = Math.max(0, Math.min(index, chapters.value.length - 1))

  if (validIndex >= 0 && validIndex < chapters.value.length) {
    currentChapterIndex.value = validIndex

    // 重置版本选择为最新版本
    resetToLatestVersion()

    // 只在移动端隐藏侧边栏
    if (isMobile.value) {
      showSidebar.value = false
    }

    // 滚动到顶部
    nextTick(() => {
      const contentEl = document.querySelector('.reader-content')
      if (contentEl) {
        contentEl.scrollTop = 0
      }
    })
  }
}

const resetToLatestVersion = () => {
  const chapter = chapters.value[currentChapterIndex.value]
  if (chapter && chapter.versions && chapter.versions.length > 0) {
    // 选择最新版本（版本号最大的）
    const latestVersion = chapter.versions.reduce((latest, current) =>
      current.versionNumber > latest.versionNumber ? current : latest
    )
    currentVersionId.value = latestVersion.id
  } else {
    currentVersionId.value = null
  }
}

const handleVersionChange = (versionId) => {
  currentVersionId.value = versionId

  // 滚动到顶部以便查看新版本内容
  nextTick(() => {
    const contentEl = document.querySelector('.reader-content')
    if (contentEl) {
      contentEl.scrollTop = 0
    }
  })
}

const formatVersionDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const prevChapter = () => {
  if (chapters.value.length === 0) return
  if (currentChapterIndex.value > 0) {
    goToChapter(currentChapterIndex.value - 1)
  }
}

const nextChapter = () => {
  if (chapters.value.length === 0) return
  if (currentChapterIndex.value < chapters.value.length - 1) {
    goToChapter(currentChapterIndex.value + 1)
  }
}

const toggleFullscreen = async () => {
  try {
    if (!isFullscreen.value) {
      // 进入全屏
      await document.documentElement.requestFullscreen?.()
      isFullscreen.value = true
      showToolbar.value = true // 进入全屏时先显示工具栏

      // 3秒后自动隐藏工具栏
      setTimeout(() => {
        if (isFullscreen.value) {
          showToolbar.value = false
        }
      }, 3000)
    } else {
      // 退出全屏
      await document.exitFullscreen?.()
      isFullscreen.value = false
      showToolbar.value = true // 退出全屏时恢复工具栏显示
    }
  } catch (error) {
    console.warn('全屏操作失败:', error)
    // 如果浏览器API失败，至少切换内部状态
    isFullscreen.value = !isFullscreen.value
  }
}

const handleMainClick = (event) => {
  // 点击主区域时隐藏/显示工具栏（全屏模式下）
  if (isFullscreen.value) {
    // 检查点击的是否是阅读内容区域
    const target = event.target
    const isContentArea = target.closest('.reader-content') &&
                          !target.closest('.reader-sidebar') &&
                          !target.closest('.page-navigation')

    if (isContentArea) {
      showToolbar.value = !showToolbar.value

      // 如果显示了工具栏，3秒后自动隐藏
      if (showToolbar.value) {
        setTimeout(() => {
          if (isFullscreen.value) {
            showToolbar.value = false
          }
        }, 3000)
      }
    }
  }
}

// 键盘快捷键
const handleKeydown = (event) => {
  switch (event.key) {
    case 'ArrowLeft':
      prevChapter()
      break
    case 'ArrowRight':
      nextChapter()
      break
    case 'Escape':
      if (isFullscreen.value) {
        toggleFullscreen()
      }
      break
    case 'F11':
      event.preventDefault()
      toggleFullscreen()
      break
  }
}

// 窗口大小变化监听
const handleResize = () => {
  isMobile.value = window.innerWidth <= 768
  // 在桌面端默认显示侧边栏，移动端默认隐藏
  if (!isMobile.value && chapters.value.length > 0) {
    showSidebar.value = true
  }
}

// 监听浏览器全屏状态变化
const handleFullscreenChange = () => {
  const isCurrentlyFullscreen = !!document.fullscreenElement
  if (isCurrentlyFullscreen !== isFullscreen.value) {
    isFullscreen.value = isCurrentlyFullscreen
    if (!isCurrentlyFullscreen) {
      showToolbar.value = true // 退出全屏时恢复工具栏
    }
  }
}

// 生命周期
onMounted(() => {
  fetchNovelData()
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('resize', handleResize)
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 初始化侧边栏状态
  handleResize()
})

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})

// 监听主题变化
watch(currentTheme, (newTheme) => {
  document.body.className = `theme-${newTheme}`
})
</script>

<style scoped>
.novel-reader {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 主题样式 */
.theme-light {
  --bg-color: #ffffff;
  --bg-color-rgb: 255, 255, 255;
  --text-color: #333333;
  --text-color-rgb: 51, 51, 51;
  --border-color: #e0e0e0;
  --sidebar-bg: #f8f9fa;
  --hover-bg: #f0f0f0;
}

.theme-green {
  --bg-color: #f0f8e8;
  --bg-color-rgb: 240, 248, 232;
  --text-color: #2d5016;
  --text-color-rgb: 45, 80, 22;
  --border-color: #d4e6c7;
  --sidebar-bg: #e8f5e0;
  --hover-bg: #ddefd2;
}

.theme-dark {
  --bg-color: #1a1a1a;
  --bg-color-rgb: 26, 26, 26;
  --text-color: #e0e0e0;
  --text-color-rgb: 224, 224, 224;
  --border-color: #404040;
  --sidebar-bg: #2d2d2d;
  --hover-bg: #404040;
}

.theme-paper {
  --bg-color: #f4f1e8;
  --bg-color-rgb: 244, 241, 232;
  --text-color: #5d4e37;
  --text-color-rgb: 93, 78, 55;
  --border-color: #d4c4a8;
  --sidebar-bg: #ede8dc;
  --hover-bg: #e6dfd0;
}

/* 工具栏样式 */
.reader-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.novel-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-color);
}

.toolbar-center {
  flex: 1;
  text-align: center;
}

.chapter-info {
  color: var(--text-color);
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 主要区域样式 */
.reader-main {
  flex: 1;
  display: flex;
  background: var(--bg-color);
  position: relative;
  overflow: hidden;
}

/* 侧边栏样式 */
.reader-sidebar {
  width: 300px;
  background: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
  margin: 0;
  color: var(--text-color);
}

.chapter-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.chapter-item {
  display: flex;
  flex-direction: column;
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.chapter-item .chapter-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chapter-item:hover {
  background: var(--hover-bg);
}

.chapter-item.active {
  background: #409eff;
  color: white;
}

.chapter-number {
  font-size: 12px;
  opacity: 0.7;
  margin-bottom: 4px;
}

.chapter-title {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.version-badge {
  font-size: 10px;
  background: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  white-space: nowrap;
}

.no-chapters {
  padding: 20px;
  text-align: center;
  color: var(--text-color);
  opacity: 0.6;
  font-size: 14px;
}

/* 内容区域样式 */
.reader-content {
  flex: 1;
  overflow-y: auto;
  padding: 40px;
  background: var(--bg-color);
  color: var(--text-color);
  display: flex;
  justify-content: center;
}

.book-page {
  width: 100%;
  margin: 0 auto;
  background: var(--bg-color);
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 200px); /* 确保有足够的高度 */
  display: flex;
  flex-direction: column;
}

.chapter-header {
  text-align: center;
  margin-bottom: 40px;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 20px;
}

.chapter-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--text-color);
  margin: 0 0 10px 0;
}

.chapter-meta {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.version-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-selector .el-select {
  opacity: 0.9;
}

.version-selector .el-select:hover {
  opacity: 1;
}

.chapter-content {
  color: var(--text-color);
  flex: 1; /* 占据剩余空间 */
  /* 移除固定的 line-height 和 font-size，让动态样式生效 */
}

.chapter-content :deep(p) {
  margin: 0 0 1em 0;
  text-indent: 2em;
  text-align: justify;
  /* 继承父元素的字体大小和行高 */
  line-height: inherit;
  font-size: inherit;
}

/* 章节导航样式 */
.chapter-navigation {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 2px solid var(--border-color);
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.nav-buttons .chapter-info {
  color: var(--text-color);
  font-size: 14px;
  opacity: 0.7;
  white-space: nowrap;
  padding: 0 20px;
  text-align: center;
  min-width: 80px;
}

.nav-buttons .el-button {
  min-width: 120px;
  height: 40px;
}

.nav-buttons .el-button:disabled {
  opacity: 0.4;
}

.no-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 翻页按钮样式 */
.page-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 50;
}

.nav-btn {
  position: absolute;
  pointer-events: auto;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.nav-btn:hover {
  opacity: 1;
}

.nav-prev {
  left: 20px;
}

.nav-next {
  right: 20px;
}

/* 进度条样式 */
.reader-progress {
  padding: 15px 20px;
  background: var(--bg-color);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-info {
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.7;
  white-space: nowrap;
}

/* 设置面板样式 */
.settings-panel {
  padding: 20px;
}

.setting-group {
  margin-bottom: 30px;
}

.setting-group h4 {
  margin: 0 0 15px 0;
  color: var(--text-color);
  font-size: 16px;
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.setting-tip {
  font-size: 12px;
  color: var(--text-color);
  opacity: 0.6;
}

.theme-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.theme-option {
  cursor: pointer;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}

.theme-option.active {
  border-color: #409eff;
}

.theme-preview {
  padding: 15px;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
}

/* 全屏模式样式 */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: var(--bg-color);
}

.fullscreen .reader-main {
  height: 100vh;
}

.fullscreen .reader-sidebar {
  position: absolute;
  height: 100%;
  transform: translateX(-100%);
  transition: transform 0.3s;
  z-index: 1000;
}

.fullscreen .reader-sidebar.show {
  transform: translateX(0);
}

.fullscreen .reader-content {
  height: 100vh;
  padding: 60px 40px 80px 40px; /* 为工具栏和进度条留出空间 */
  overflow-y: auto;
}

.fullscreen .book-page {
  min-height: calc(100vh - 200px); /* 全屏模式下调整高度 */
}

.fullscreen .reader-content::-webkit-scrollbar {
  width: 8px;
}

.fullscreen .reader-content::-webkit-scrollbar-track {
  background: transparent;
}

.fullscreen .reader-content::-webkit-scrollbar-thumb {
  background: rgba(var(--text-color-rgb, 128, 128, 128), 0.3);
  border-radius: 4px;
}

.fullscreen .reader-content::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--text-color-rgb, 128, 128, 128), 0.5);
}

.fullscreen .reader-toolbar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background: rgba(var(--bg-color-rgb), 0.95);
  backdrop-filter: blur(10px);
  transition: opacity 0.3s, transform 0.3s;
}

.fullscreen .reader-toolbar.hidden {
  opacity: 0;
  transform: translateY(-100%);
  pointer-events: none;
}

.fullscreen .reader-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background: rgba(var(--bg-color-rgb), 0.95);
  backdrop-filter: blur(10px);
  transition: opacity 0.3s, transform 0.3s;
}

.fullscreen .reader-progress.hidden {
  opacity: 0;
  transform: translateY(100%);
  pointer-events: none;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay p {
  margin-top: 20px;
  color: var(--text-color);
}

/* 移动端遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-sidebar {
    width: 250px;
    position: absolute;
    height: 100%;
    transform: translateX(-100%);
    transition: transform 0.3s;
    z-index: 200;
  }

  .reader-sidebar.show {
    transform: translateX(0);
  }

  .reader-content {
    padding: 20px;
  }

  .book-page {
    padding: 20px;
    min-height: calc(100vh - 120px); /* 移动端调整高度 */
  }

  .chapter-title {
    font-size: 24px;
  }

  .nav-btn {
    display: none;
  }

  .chapter-navigation {
    margin-top: 30px;
    padding-top: 20px;
  }

  .nav-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .nav-buttons .chapter-info {
    order: -1; /* 在移动端将章节信息放在按钮上方 */
  }

  .chapter-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .version-selector {
    width: 100%;
  }

  .version-selector .el-select {
    width: 100%;
  }
}
</style>
