<template>
  <PageLoading page-key="world-settings" @retry="fetchWorldSettings">
    <div class="world-settings-page">
    <el-container>
      <el-header>
        <div class="header-content">
          <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
          <h1>世界观设定管理</h1>
        </div>
      </el-header>
      <el-main>
        <el-row :gutter="20">
          <el-col :span="16">
            <h2>设定列表</h2>
            <el-input
              v-model="searchTerm"
              placeholder="搜索设定..."
              clearable
              style="margin-bottom: 20px;"
              @input="filterSettings"
            />
            <el-tabs v-model="activeCategory" @tab-click="handleTabClick">
              <el-tab-pane
                v-for="category in categories"
                :key="category.value"
                :label="category.label"
                :name="category.value"
              >
                <el-table :data="filteredSettings" style="width: 100%" v-loading="loading">
                  <el-table-column prop="title" label="标题" width="180"></el-table-column>
                  <el-table-column prop="category" label="类别" width="120"></el-table-column>
                  <el-table-column prop="content" label="内容" show-overflow-tooltip></el-table-column>
                  <el-table-column label="操作" width="180">
                    <template #default="scope">
                      <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
                      <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col :span="8">
            <h2>{{ formTitle }}</h2>
            <el-form ref="settingFormRef" :model="settingForm" :rules="rules" label-width="80px">
              <el-form-item label="标题" prop="title">
                <el-input v-model="settingForm.title"></el-input>
              </el-form-item>
              <el-form-item label="类别" prop="category">
                <el-select v-model="settingForm.category" placeholder="请选择类别">
                  <el-option
                    v-for="category in categories.filter(c => c.value !== 'all')"
                    :key="category.value"
                    :label="category.label"
                    :value="category.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="内容" prop="content">
                <el-input type="textarea" v-model="settingForm.content" :rows="5"></el-input>
              </el-form-item>

              <!-- AI生成功能 -->
              <el-form-item label="AI生成">
                <div class="ai-generation-section">
                  <el-button
                    type="success"
                    @click="showAIGenerationDialog"
                    :disabled="!settingForm.title || !settingForm.category"
                  >
                    <el-icon><Star /></el-icon>
                    AI生成内容
                  </el-button>
                  <el-text type="info" size="small" style="margin-left: 10px;">
                    需要先填写标题和类别，支持流式生成和打字机效果
                  </el-text>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="submitForm">{{ formMode === 'add' ? '创建' : '更新' }}</el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-main>
    </el-container>

    <!-- 通用AI生成对话框 -->
    <AIGenerationDialog
      :visible="aiGenerationDialogVisible"
      @update:visible="aiGenerationDialogVisible = $event"
      :content-type="'世界观'"
      :existing-content="settingForm.content"
      :project-id="props.projectId"
      :role-filter="'world_builder'"
      :context-data="{
        title: settingForm.title,
        category: settingForm.category,
        categoryLabel: categories.find(c => c.value === settingForm.category)?.label || '',
        projectInfo: projectInfo
      }"
      :enable-streaming="true"
      :use-typewriter-effect="true"
      :typewriter-speed="60"
      @content-generated="handleContentGenerated"
    />




    </div>
  </PageLoading>
</template>

<script setup>
import { ref, reactive, onMounted, computed, defineProps, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { Star } from '@element-plus/icons-vue';
import api from '../services/api';
import PageLoading from '../components/PageLoading.vue';
import AIGenerationDialog from '../components/AIGenerationDialog.vue';
import { usePageLoading, useOperationLoading } from '../composables/useLoading';

const router = useRouter();
const props = defineProps({
  projectId: {
    type: String,
    required: true,
  },
});

// 返回上一页方法
const goBack = () => {
  router.go(-1);
};

// 加载状态管理
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('world-settings');
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading();

// 移除 mockApiService
// const mockApiService = { ... };
const worldSettings = ref([]);
const loading = ref(false);
const searchTerm = ref('');
const activeCategory = ref('all');

const categories = ref([
  { label: '全部', value: 'all' },
  { label: '世界背景与历史', value: 'history_background' },
  { label: '地理环境与文化', value: 'geo_culture' },
  { label: '核心规则', value: 'rules' },
  { label: '独特元素', value: 'unique_elements' },
  { label: '游戏机制设定', value: 'game_mechanics' },
]);

const settingFormRef = ref(null);
const settingForm = reactive({
  id: null,
  title: '',
  category: '',
  content: '',
});
const formMode = ref('add');
const formTitle = computed(() => formMode.value === 'add' ? '添加新设定' : '编辑设定');

const rules = reactive({
  title: [{ required: true, message: '请输入设定标题', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入设定内容', trigger: 'blur' }],
});

// AI生成相关的响应式数据
const aiGenerationDialogVisible = ref(false);

const filteredSettings = computed(() => {
  let settings = worldSettings.value;
  if (activeCategory.value !== 'all') {
    settings = settings.filter(s => s.category === activeCategory.value);
  }
  if (searchTerm.value) {
    settings = settings.filter(s =>
      s.title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      s.content.toLowerCase().includes(searchTerm.value.toLowerCase())
    );
  }
  return settings;
});

// 项目信息（用于AI生成的上下文）
const projectInfo = ref(null);

const fetchProjectInfo = async () => {
  if (!props.projectId) return;
  try {
    // 获取项目信息
    const response = await api.get(`/projects/${props.projectId}`);
    projectInfo.value = response;
  } catch (error) {
    console.error('Failed to fetch project info:', error);
    ElMessage.error('获取项目信息失败');
  }
};

const fetchWorldSettings = async () => {
  if (!props.projectId) return;

  try {
    showPageLoading('正在加载世界观设定...');
    loading.value = true;

    const response = await api.get(`/projects/${props.projectId}/world-settings`);
    // 将后端的字段名称映射到前端的字段名称
    worldSettings.value = response.map(setting => ({
      id: setting.id,
      title: setting.title, // 后端已经在控制器中将name映射为title
      category: setting.category,
      content: setting.content, // 后端已经在控制器中将description映射为content
      projectId: setting.projectId,
      createdAt: setting.createdAt,
      updatedAt: setting.updatedAt
    }));

    hidePageLoading();
  } catch (error) {
    console.error('Failed to fetch world settings:', error);
    setPageError(error);
    ElMessage.error('获取世界设定失败');
    worldSettings.value = []; // 出错时清空或设置为默认
  } finally {
    loading.value = false;
  }
};

const handleEdit = (setting) => {
  formMode.value = 'edit';
  settingForm.id = setting.id;
  settingForm.title = setting.title;
  settingForm.category = setting.category;
  settingForm.content = setting.content;
  if (settingFormRef.value) {
    settingFormRef.value.clearValidate();
  }
};

const handleDelete = (setting) => {
  handleDeleteSetting(setting.id);
};

const submitForm = async () => {
  if (!settingFormRef.value) return;
  settingFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        if (formMode.value === 'add') {
          const newSetting = {
            name: settingForm.title, // 前端用title，后端用name
            category: settingForm.category,
            description: settingForm.content, // 前端用content，后端用description
            projectId: props.projectId // 确保传递 projectId
          };
          const response = await api.post('/world-settings', newSetting);
          worldSettings.value.push(response);
          ElMessage.success('添加成功');
        } else {
          const updatedSetting = {
            name: settingForm.title, // 前端用title，后端用name
            category: settingForm.category,
            description: settingForm.content // 前端用content，后端用description
          };
          // 注意：PUT 请求通常将 projectId 放在 URL 中或由后端通过 setting.id 关联
          // 如果后端 /api/world-settings/:id 的更新操作不需要 projectId 在 body 中，则可以不传
          // 此处假设后端能通过 settingForm.id 找到并更新，且已关联 projectId
          const response = await api.put(`/world-settings/${settingForm.id}`, updatedSetting);
          const index = worldSettings.value.findIndex(s => s.id === settingForm.id);
          if (index !== -1) {
            worldSettings.value[index] = response;
          }
          ElMessage.success('更新成功');
        }
        // showDialog.value = false; // 关闭对话框
        // 模拟关闭对话框
        console.log('Form submitted, close dialog');
        fetchWorldSettings(); // 重新获取列表，确保数据同步
      } catch (error) {
        console.error('Failed to save world setting:', error);
        ElMessage.error(formMode.value === 'add' ? '添加失败' : '更新失败');
      } finally {
        loading.value = false;
      }
    } else {
      console.log('error submit!!');
      return false;
    }
  });
};

const handleDeleteSetting = async (settingId) => {
  try {
    await ElMessageBox.confirm('确定要删除此设定吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    loading.value = true;
    await api.delete(`/world-settings/${settingId}`);
    worldSettings.value = worldSettings.value.filter(s => s.id !== settingId);
    ElMessage.success('删除成功');
  } catch (error) {
    if (error !== 'cancel') { // 用户点击取消时，ElMessageBox会reject 'cancel'
      console.error('Failed to delete world setting:', error);
      ElMessage.error('删除失败');
    }
  } finally {
    loading.value = false;
  }
};

watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    fetchProjectInfo();
    fetchWorldSettings();
  }
}, { immediate: true });

onMounted(() => {
  // projectId 现在通过 props 传递，并在 watch 中初始化加载
  // 如果 projectId 初始就有值，watch 的 immediate: true 会触发首次加载
  // 如果希望在 onMounted 中也确保加载（例如 props 可能异步更新的情况，虽然不常见），可以保留调用
  // 但通常 watch immediate: true 已经足够
  // if (props.projectId) {
  //   fetchProjectInfo();
  //   fetchWorldSettings();
  // }
});

const handleTabClick = (tab) => {
  activeCategory.value = tab.props.name;
};

const filterSettings = () => {
  // 计算属性会自动更新，这里可以留空或用于更复杂的即时过滤逻辑
};

const changeCategory = (category) => {
  activeCategory.value = category;
};

const resetForm = () => {
  if (settingFormRef.value) {
    settingFormRef.value.resetFields();
  }
  settingForm.id = null;
  settingForm.title = '';
  settingForm.category = '';
  settingForm.content = '';
  formMode.value = 'add';
};

// AI生成相关方法
const showAIGenerationDialog = () => {
  aiGenerationDialogVisible.value = true;
};

// 处理AI生成的内容
const handleContentGenerated = (data) => {
  if (data.action === 'replace') {
    settingForm.content = data.content;
    ElMessage.success('已使用AI生成的内容');
  } else if (data.action === 'append') {
    if (settingForm.content) {
      settingForm.content += '\n\n' + data.content;
    } else {
      settingForm.content = data.content;
    }
    ElMessage.success('已追加AI生成的内容');
  }
};

</script>

<style scoped>
.world-settings-page {
  padding: 20px;
}
.header-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-header h1 {
  font-size: 24px;
  margin-bottom: 20px;
  flex-grow: 1;
  text-align: center;
}

.ai-generation-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ai-result-content {
  max-height: 600px;
  overflow-y: auto;
}

.ai-result-content .el-textarea__inner {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

/* 可以添加更多响应式布局样式 */
</style>