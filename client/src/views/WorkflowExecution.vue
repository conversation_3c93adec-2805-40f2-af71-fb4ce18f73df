<template>
  <div class="workflow-execution">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="execution-info">
          <h1>{{ workflowName || '流程执行' }}</h1>
          <div class="execution-meta">
            <el-tag :type="getStatusType(execution?.status)" size="large">
              {{ getStatusText(execution?.status) }}
            </el-tag>
            <span class="execution-id">执行ID: {{ executionId }}</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button 
          v-if="execution?.status === 'running'" 
          @click="pauseExecution"
          :loading="pausing"
        >
          <el-icon><VideoPause /></el-icon>
          暂停
        </el-button>
        <el-button 
          v-if="execution?.status === 'paused'" 
          @click="resumeExecution"
          type="primary"
          :loading="resuming"
        >
          <el-icon><VideoPlay /></el-icon>
          继续
        </el-button>
        <el-button 
          v-if="['running', 'paused'].includes(execution?.status)" 
          @click="cancelExecution"
          type="danger"
          :loading="cancelling"
        >
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button @click="refreshStatus">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 执行进度 -->
    <div class="execution-progress" v-if="execution">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>执行进度</span>
            <span class="progress-text">{{ execution.progress || 0 }}%</span>
          </div>
        </template>
        
        <el-progress 
          :percentage="execution.progress || 0" 
          :status="getProgressStatus(execution.status)"
          :stroke-width="8"
        />
        
        <div class="progress-details">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="detail-item">
                <div class="detail-label">当前节点</div>
                <div class="detail-value">{{ getCurrentNodeName() }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <div class="detail-label">已用时间</div>
                <div class="detail-value">{{ formatDuration(getExecutionDuration()) }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <div class="detail-label">预计完成</div>
                <div class="detail-value">
                  {{ execution.estimatedCompletionAt ? formatTime(execution.estimatedCompletionAt) : '计算中...' }}
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="detail-item">
                <div class="detail-label">关联项目</div>
                <div class="detail-value">{{ execution.Project?.name || '无' }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 流程图显示 -->
    <div class="execution-flow" v-if="workflow">
      <el-card>
        <template #header>
          <span>流程图</span>
        </template>
        
        <div class="flow-container" ref="flowContainer">
          <VueFlow
            v-model="elements"
            :readonly="true"
            :fit-view-on-init="true"
            class="execution-flow-canvas"
          >
            <!-- 背景 -->
            <Background pattern-color="#aaa" :gap="16" />
            
            <!-- 控制器 -->
            <Controls />
            
            <!-- 小地图 -->
            <MiniMap />
            
            <!-- 自定义节点 -->
            <template #node-start="{ data }">
              <StartNode 
                :data="data" 
                :execution-status="getNodeExecutionStatus(data.id)"
              />
            </template>
            
            <template #node-ai_generation="{ data }">
              <AIGenerationNode 
                :data="data" 
                :execution-status="getNodeExecutionStatus(data.id)"
              />
            </template>
            
            <template #node-user_input="{ data }">
              <UserInputNode 
                :data="data" 
                :execution-status="getNodeExecutionStatus(data.id)"
              />
            </template>
            
            <template #node-condition="{ data }">
              <ConditionNode 
                :data="data" 
                :execution-status="getNodeExecutionStatus(data.id)"
              />
            </template>
            
            <template #node-end="{ data }">
              <EndNode 
                :data="data" 
                :execution-status="getNodeExecutionStatus(data.id)"
              />
            </template>
          </VueFlow>
        </div>
      </el-card>
    </div>

    <!-- 执行日志 -->
    <div class="execution-log">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>执行日志</span>
            <el-switch 
              v-model="autoRefresh" 
              active-text="自动刷新"
              @change="toggleAutoRefresh"
            />
          </div>
        </template>
        
        <div class="log-container">
          <div 
            v-for="(log, index) in executionLogs"
            :key="index"
            class="log-item"
            :class="log.status"
          >
            <div class="log-time">{{ formatTime(log.timestamp) }}</div>
            <div class="log-node">{{ log.nodeId }}</div>
            <div class="log-status">
              <el-tag :type="getStatusType(log.status)" size="small">
                {{ getStatusText(log.status) }}
              </el-tag>
            </div>
            <div class="log-message">{{ log.message }}</div>
          </div>
          
          <div v-if="executionLogs.length === 0" class="empty-logs">
            <el-empty description="暂无执行日志" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 用户输入对话框 -->
    <el-dialog
      v-model="showUserInputDialog"
      title="需要用户输入"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <UserInputForm
        v-if="currentUserInputNode"
        :node="currentUserInputNode"
        :execution-data="execution?.outputData || {}"
        @submit="handleUserInput"
        @cancel="cancelExecution"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  VideoPause,
  VideoPlay,
  Close,
  Refresh
} from '@element-plus/icons-vue'

// Vue Flow 相关导入
import { VueFlow, Background, Controls, MiniMap } from '@vue-flow/core'

// 自定义组件导入
import StartNode from '@/components/workflow/nodes/StartNode.vue'
import AIGenerationNode from '@/components/workflow/nodes/AIGenerationNode.vue'
import UserInputNode from '@/components/workflow/nodes/UserInputNode.vue'
import ConditionNode from '@/components/workflow/nodes/ConditionNode.vue'
import EndNode from '@/components/workflow/nodes/EndNode.vue'
import UserInputForm from '@/components/workflow/UserInputForm.vue'

import workflowAPI from '@/services/workflowAPI'
import websocketService from '@/services/websocketService'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const pausing = ref(false)
const resuming = ref(false)
const cancelling = ref(false)
const execution = ref(null)
const workflow = ref(null)
const elements = ref([])
const nodeExecutions = ref([])
const autoRefresh = ref(true)
const refreshTimer = ref(null)
const showUserInputDialog = ref(false)
const currentUserInputNode = ref(null)
const flowContainer = ref()

// 计算属性
const executionId = computed(() => route.params.executionId || route.query.executionId)
const workflowName = computed(() => workflow.value?.name || execution.value?.WorkflowTemplate?.name)

const executionLogs = computed(() => {
  return execution.value?.executionLog || []
})

// 方法
const goBack = () => {
  router.push('/workflow-templates')
}

const loadExecutionStatus = async () => {
  if (!executionId.value) return
  
  try {
    const response = await workflowAPI.getExecutionStatus(executionId.value)
    execution.value = response
    nodeExecutions.value = response.nodeExecutions || []
    
    // 如果是第一次加载，也加载工作流数据
    if (!workflow.value && response.WorkflowTemplate) {
      await loadWorkflowData(response.WorkflowTemplate.id)
    }
    
    // 检查是否需要用户输入
    if (response.status === 'paused' && response.currentNodeId) {
      const currentNode = workflow.value?.nodes?.find(node => node.nodeId === response.currentNodeId)
      if (currentNode && currentNode.nodeType === 'user_input') {
        currentUserInputNode.value = currentNode
        showUserInputDialog.value = true
      }
    }
    
  } catch (error) {
    ElMessage.error('获取执行状态失败')
    console.error(error)
  }
}

const loadWorkflowData = async (workflowId) => {
  try {
    const response = await workflowAPI.getTemplateById(workflowId)
    workflow.value = response
    
    // 转换数据格式为Vue Flow格式
    const nodes = response.nodes?.map(node => ({
      id: node.nodeId,
      type: node.nodeType,
      position: { x: node.positionX || 0, y: node.positionY || 0 },
      data: {
        id: node.nodeId,
        name: node.name,
        description: node.description,
        config: node.config || {}
      }
    })) || []
    
    const edges = response.connections?.map(conn => ({
      id: `${conn.sourceNodeId}-${conn.targetNodeId}`,
      source: conn.sourceNodeId,
      target: conn.targetNodeId,
      sourceHandle: conn.sourceHandle,
      targetHandle: conn.targetHandle
    })) || []
    
    elements.value = [...nodes, ...edges]
    
  } catch (error) {
    console.error('加载工作流数据失败:', error)
  }
}

const getNodeExecutionStatus = (nodeId) => {
  const nodeExecution = nodeExecutions.value.find(ne => ne.nodeId === nodeId)
  return nodeExecution?.status || null
}

const getCurrentNodeName = () => {
  if (!execution.value?.currentNodeId || !workflow.value?.nodes) return '无'
  
  const currentNode = workflow.value.nodes.find(node => node.nodeId === execution.value.currentNodeId)
  return currentNode?.name || execution.value.currentNodeId
}

const getExecutionDuration = () => {
  if (!execution.value?.startedAt) return 0
  
  const startTime = new Date(execution.value.startedAt)
  const endTime = execution.value.completedAt ? new Date(execution.value.completedAt) : new Date()
  return endTime - startTime
}

const pauseExecution = async () => {
  try {
    pausing.value = true
    await workflowAPI.pauseExecution(executionId.value)
    ElMessage.success('流程已暂停')
    await loadExecutionStatus()
  } catch (error) {
    ElMessage.error('暂停流程失败')
    console.error(error)
  } finally {
    pausing.value = false
  }
}

const resumeExecution = async () => {
  try {
    resuming.value = true
    await workflowAPI.continueExecution(executionId.value, {})
    ElMessage.success('流程已继续')
    await loadExecutionStatus()
  } catch (error) {
    ElMessage.error('继续流程失败')
    console.error(error)
  } finally {
    resuming.value = false
  }
}

const cancelExecution = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消流程执行吗？此操作不可恢复。',
      '确认取消',
      {
        confirmButtonText: '取消执行',
        cancelButtonText: '继续执行',
        type: 'warning'
      }
    )
    
    cancelling.value = true
    await workflowAPI.cancelExecution(executionId.value)
    ElMessage.success('流程已取消')
    await loadExecutionStatus()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('取消流程失败')
    console.error(error)
  } finally {
    cancelling.value = false
  }
}

const handleUserInput = async (inputData) => {
  try {
    await workflowAPI.continueExecution(executionId.value, {
      nodeId: currentUserInputNode.value.nodeId,
      inputData
    })
    
    showUserInputDialog.value = false
    currentUserInputNode.value = null
    ElMessage.success('输入已提交，流程继续执行')
    await loadExecutionStatus()
  } catch (error) {
    ElMessage.error('提交输入失败')
    console.error(error)
  }
}

const refreshStatus = () => {
  loadExecutionStatus()
}

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer.value) return
  
  refreshTimer.value = setInterval(() => {
    if (execution.value && ['running', 'paused'].includes(execution.value.status)) {
      loadExecutionStatus()
    } else {
      stopAutoRefresh()
    }
  }, 3000) // 每3秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 工具方法
const getStatusType = (status) => {
  const types = {
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning',
    paused: 'warning',
    pending: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    paused: '已暂停',
    pending: '等待中'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return null
}

const formatDuration = (ms) => {
  if (!ms) return '0秒'
  
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// WebSocket相关方法
const connectWebSocket = async () => {
  try {
    await websocketService.connect()

    // 订阅执行状态更新
    if (executionId.value) {
      websocketService.subscribeExecution(executionId.value)

      // 监听执行更新
      websocketService.onExecutionUpdate(executionId.value, handleWebSocketUpdate)
    }

    console.log('WebSocket连接成功')
  } catch (error) {
    console.error('WebSocket连接失败:', error)
    // WebSocket连接失败时，继续使用轮询
    if (autoRefresh.value) {
      startAutoRefresh()
    }
  }
}

const handleWebSocketUpdate = (updateData) => {
  console.log('收到执行状态更新:', updateData)

  // 更新本地状态
  if (execution.value) {
    Object.assign(execution.value, updateData)
  }

  // 如果状态变为完成或失败，停止自动刷新
  if (['completed', 'failed', 'cancelled'].includes(updateData.status)) {
    stopAutoRefresh()
  }
}

const disconnectWebSocket = () => {
  if (executionId.value) {
    websocketService.unsubscribeExecution(executionId.value)
    websocketService.offExecutionUpdate(executionId.value, handleWebSocketUpdate)
  }
}

// 生命周期
onMounted(async () => {
  if (executionId.value) {
    // 首先加载执行状态
    await loadExecutionStatus()

    // 尝试连接WebSocket
    await connectWebSocket()

    // 如果WebSocket连接失败，启用轮询
    if (!websocketService.getStatus().isConnected && autoRefresh.value) {
      startAutoRefresh()
    }
  } else {
    ElMessage.error('缺少执行ID参数')
    goBack()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
  disconnectWebSocket()
})
</script>

<style scoped>
.workflow-execution {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.execution-info h1 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.execution-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.execution-id {
  font-size: 12px;
  color: #666;
}

.header-right {
  display: flex;
  gap: 8px;
}

.execution-progress {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-weight: 600;
  color: #409eff;
}

.progress-details {
  margin-top: 16px;
}

.detail-item {
  text-align: center;
}

.detail-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.execution-flow {
  margin-bottom: 24px;
}

.flow-container {
  height: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.execution-flow-canvas {
  width: 100%;
  height: 100%;
}

.execution-log {
  margin-bottom: 24px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  display: grid;
  grid-template-columns: 150px 120px 80px 1fr;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #666;
}

.log-node {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.log-message {
  font-size: 13px;
  color: #333;
}

.empty-logs {
  padding: 40px 0;
}

/* Vue Flow 样式覆盖 */
:deep(.vue-flow__node) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__edge) {
  pointer-events: none;
}
</style>
