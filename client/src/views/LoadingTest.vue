<template>
  <PageLoading page-key="loading-test" @retry="handleRetry">
    <div class="loading-test-page">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>加载状态测试页面</span>
          </div>
        </template>
        
        <div class="test-section">
          <h3>全局加载测试</h3>
          <el-space>
            <el-button @click="testGlobalLoading">测试全局加载</el-button>
            <el-button @click="testGlobalLoadingWithText">测试全局加载（自定义文本）</el-button>
          </el-space>
        </div>
        
        <el-divider />
        
        <div class="test-section">
          <h3>页面加载测试</h3>
          <el-space>
            <el-button @click="testPageLoading">测试页面加载</el-button>
            <el-button @click="testPageError">测试页面错误</el-button>
          </el-space>
        </div>
        
        <el-divider />
        
        <div class="test-section">
          <h3>操作加载测试</h3>
          <el-space>
            <el-button 
              @click="testOperationLoading('save')"
              :loading="getOperationState('save').loading"
            >
              测试保存操作
            </el-button>
            <el-button 
              @click="testOperationLoading('delete')"
              :loading="getOperationState('delete').loading"
            >
              测试删除操作
            </el-button>
            <el-button 
              @click="testOperationLoading('create')"
              :loading="getOperationState('create').loading"
            >
              测试创建操作
            </el-button>
          </el-space>
        </div>
        
        <el-divider />
        
        <div class="test-section">
          <h3>API调用包装器测试</h3>
          <el-space>
            <el-button @click="testApiWithGlobalLoading">API + 全局加载</el-button>
            <el-button @click="testApiWithPageLoading">API + 页面加载</el-button>
            <el-button @click="testApiWithOperationLoading">API + 操作加载</el-button>
          </el-space>
        </div>
        
        <el-divider />
        
        <div class="test-section">
          <h3>批量操作测试</h3>
          <el-space>
            <el-button @click="testBatchOperation">测试批量操作</el-button>
          </el-space>
          
          <div v-if="batchStates.batchTest" class="batch-progress">
            <el-progress 
              :percentage="batchStates.batchTest.progress"
              :status="batchStates.batchTest.loading ? 'active' : 'success'"
            />
            <p>{{ batchStates.batchTest.text }} ({{ batchStates.batchTest.current }}/{{ batchStates.batchTest.total }})</p>
          </div>
        </div>
      </el-card>
    </div>
  </PageLoading>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import PageLoading from '../components/PageLoading.vue'
import { 
  useGlobalLoading, 
  usePageLoading, 
  useOperationLoading, 
  useApiCall,
  useBatchLoading 
} from '../composables/useLoading'

// 全局加载
const { showGlobalLoading, hideGlobalLoading } = useGlobalLoading()

// 页面加载
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('loading-test')

// 操作加载
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

// API调用包装器
const { withGlobalLoading, withPageLoading, withOperationLoading } = useApiCall()

// 批量操作
const { batchStates, startBatch, updateBatchProgress, finishBatch } = useBatchLoading()

// 模拟API调用
const mockApiCall = (delay = 2000, shouldFail = false) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (shouldFail) {
        reject(new Error('模拟API调用失败'))
      } else {
        resolve('API调用成功')
      }
    }, delay)
  })
}

// 测试全局加载
const testGlobalLoading = async () => {
  showGlobalLoading()
  await mockApiCall(3000)
  hideGlobalLoading()
  ElMessage.success('全局加载测试完成')
}

// 测试全局加载（自定义文本）
const testGlobalLoadingWithText = async () => {
  showGlobalLoading('正在处理复杂任务...')
  await mockApiCall(3000)
  hideGlobalLoading()
  ElMessage.success('自定义文本加载测试完成')
}

// 测试页面加载
const testPageLoading = async () => {
  try {
    showPageLoading('正在加载页面数据...')
    await mockApiCall(2000)
    hidePageLoading()
    ElMessage.success('页面加载测试完成')
  } catch (error) {
    setPageError(error)
  }
}

// 测试页面错误
const testPageError = async () => {
  try {
    showPageLoading('正在加载数据...')
    await mockApiCall(2000, true) // 模拟失败
  } catch (error) {
    setPageError(error)
  }
}

// 测试操作加载
const testOperationLoading = async (operationKey) => {
  try {
    const operationTexts = {
      save: '正在保存...',
      delete: '正在删除...',
      create: '正在创建...'
    }
    
    showOperationLoading(operationKey, operationTexts[operationKey])
    await mockApiCall(2000)
    ElMessage.success(`${operationKey} 操作完成`)
  } catch (error) {
    ElMessage.error(`${operationKey} 操作失败`)
  } finally {
    hideOperationLoading(operationKey)
  }
}

// 测试API包装器
const testApiWithGlobalLoading = async () => {
  try {
    await withGlobalLoading(() => mockApiCall(2000), '正在调用API...')
    ElMessage.success('API + 全局加载测试完成')
  } catch (error) {
    ElMessage.error('API调用失败')
  }
}

const testApiWithPageLoading = async () => {
  try {
    await withPageLoading('loading-test', () => mockApiCall(2000), '正在调用API...')
    ElMessage.success('API + 页面加载测试完成')
  } catch (error) {
    ElMessage.error('API调用失败')
  }
}

const testApiWithOperationLoading = async () => {
  try {
    await withOperationLoading('apiTest', () => mockApiCall(2000), '正在调用API...')
    ElMessage.success('API + 操作加载测试完成')
  } catch (error) {
    ElMessage.error('API调用失败')
  }
}

// 测试批量操作
const testBatchOperation = async () => {
  const total = 5
  startBatch('batchTest', total, '正在处理批量任务...')
  
  for (let i = 1; i <= total; i++) {
    updateBatchProgress('batchTest', i, `正在处理第 ${i} 项任务...`)
    await mockApiCall(1000)
  }
  
  finishBatch('batchTest')
  ElMessage.success('批量操作完成')
}

// 重试处理
const handleRetry = () => {
  ElMessage.info('重试加载页面数据')
  testPageLoading()
}
</script>

<style scoped>
.loading-test-page {
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
}

.test-section h3 {
  margin-bottom: 12px;
  color: #303133;
}

.batch-progress {
  margin-top: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.batch-progress p {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #606266;
}
</style>
