<template>
  <div class="login-container">
    <div class="login-background">
<!--      <div class="scroll-decoration left-scroll"></div>-->
<!--      <div class="scroll-decoration right-scroll"></div>-->
    </div>

    <el-card class="login-card">
      <template #header>
        <div class="card-header">
          <div class="login-title">
            <span class="title-decoration">📚</span>
            <h2>墨韵登录</h2>
            <span class="title-decoration">📚</span>
          </div>
          <p class="login-subtitle">进入您的创作世界</p>
        </div>
      </template>

      <el-form
        ref="loginForm"
        :model="loginData"
        :rules="rules"
        label-width="80px"
        @submit.prevent="handleLogin"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginData.username" placeholder="请输入用户名"></el-input>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="loginData.password"
            type="password"
            placeholder="请输入密码"
            show-password
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="loading">登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import authService from '../services/auth';

const router = useRouter();
const loginForm = ref(null);
const loading = ref(false);

const loginData = reactive({
  username: 'admin',
  password: ''
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
};

const handleLogin = async () => {
  if (!loginForm.value) return;

  await loginForm.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        await authService.login(loginData);
        ElMessage.success('登录成功');
        router.push('/');
      } catch (error) {
        console.error('登录失败:', error);
        ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码');
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<style scoped>
/* 登录页面古典风格 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--bg-gradient);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 背景装饰 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10%;
  pointer-events: none;
}

.scroll-decoration {
  width: 60px;
  height: 300px;
  background: linear-gradient(to bottom, var(--wood-brown), var(--light-brown), var(--wood-brown));
  border-radius: 30px;
  position: relative;
  box-shadow: var(--shadow-medium);
  opacity: 0.3;
}

.scroll-decoration::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  bottom: 15px;
  background: var(--paper-cream);
  border-radius: 20px;
  box-shadow: inset 0 0 10px rgba(139, 69, 19, 0.3);
}

/* 登录卡片样式 */
.login-card {
  width: 450px;
  max-width: 90%;
  border-radius: 20px;
  border: var(--border-gold);
  background: linear-gradient(145deg, var(--paper-white), #f8f4e6);
  box-shadow: var(--shadow-heavy);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, var(--primary-gold), var(--secondary-gold), var(--primary-gold));
}

.login-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #faf7f0, #f0ead6);
  border-bottom: 2px solid var(--primary-gold);
  padding: 30px;
  text-align: center;
}

.login-card :deep(.el-card__body) {
  padding: 40px;
}

/* 标题样式 */
.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.login-title {
  display: flex;
  align-items: center;
  gap: 15px;
}

.login-title h2 {
  color: var(--dark-brown);
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: 2px;
  font-family: var(--font-classical);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.title-decoration {
  font-size: 1.5rem;
  animation: sparkle 2s ease-in-out infinite alternate;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.login-subtitle {
  color: var(--light-brown);
  font-size: 1rem;
  margin: 0;
  font-style: italic;
  letter-spacing: 1px;
  opacity: 0.8;
}

/* 表单样式 */
.login-card :deep(.el-form-item__label) {
  color: var(--dark-brown) !important;
  font-weight: 600;
  font-family: var(--font-classical);
  letter-spacing: 1px;
}

.login-card :deep(.el-input__wrapper) {
  background: var(--paper-white);
  border: 2px solid #e6d7c3;
  border-radius: 10px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.login-card :deep(.el-input__wrapper:hover) {
  border-color: var(--secondary-gold);
}

.login-card :deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.1);
}

.login-card :deep(.el-input__inner) {
  color: var(--dark-brown);
  font-family: var(--font-classical);
}

.login-card :deep(.el-button--primary) {
  width: 100%;
  height: 45px;
  background: linear-gradient(145deg, var(--primary-gold), var(--secondary-gold)) !important;
  border: none !important;
  border-radius: 22px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 2px;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-card :deep(.el-button--primary)::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-card :deep(.el-button--primary):hover::before {
  left: 100%;
}

.login-card :deep(.el-button--primary):hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.login-tips {
  margin-top: 20px;
  color: var(--light-brown);
  font-size: 0.9rem;
  text-align: center;
  font-family: var(--font-classical);
  letter-spacing: 0.5px;
}

.login-tips p {
  margin: 8px 0;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card {
    width: 95%;
    margin: 20px;
  }

  .login-card :deep(.el-card__header) {
    padding: 25px 20px;
  }

  .login-card :deep(.el-card__body) {
    padding: 30px 20px;
  }

  .login-title h2 {
    font-size: 1.5rem;
  }

  .scroll-decoration {
    display: none;
  }
}
</style>
