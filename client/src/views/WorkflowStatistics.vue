<template>
  <div class="workflow-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>统计分析</h1>
      </div>
      <div class="header-right">
        <el-select v-model="selectedPeriod" @change="loadStatistics" style="width: 120px">
          <el-option label="今日" value="day" />
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
          <el-option label="本年" value="year" />
        </el-select>
        <el-button @click="loadStatistics">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon total">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.totalExecutions || 0 }}</div>
                <div class="stat-label">总执行次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon success">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.successRate || 0 }}%</div>
                <div class="stat-label">成功率</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon time">
                <el-icon><Timer /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatDuration(statistics.averageExecutionTime) }}</div>
                <div class="stat-label">平均执行时间</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon workflows">
                <el-icon><Operation /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ statistics.popularWorkflows?.length || 0 }}</div>
                <div class="stat-label">活跃流程数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 执行趋势图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>执行趋势</span>
            </template>
            <div ref="trendChart" class="chart-container"></div>
          </el-card>
        </el-col>
        
        <!-- 成功率图 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>成功率分析</span>
            </template>
            <div ref="successChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px">
        <!-- 热门流程 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>热门流程</span>
            </template>
            <div class="popular-workflows">
              <div 
                v-for="(workflow, index) in statistics.popularWorkflows?.slice(0, 10)"
                :key="workflow.id"
                class="workflow-item"
              >
                <div class="workflow-rank">{{ index + 1 }}</div>
                <div class="workflow-info">
                  <div class="workflow-name">{{ workflow.name }}</div>
                  <div class="workflow-category">{{ workflow.category }}</div>
                </div>
                <div class="workflow-stats">
                  <div class="recent-executions">{{ workflow.recentExecutions }}次</div>
                  <div class="total-usage">总计{{ workflow.usageCount }}次</div>
                </div>
              </div>
              
              <div v-if="!statistics.popularWorkflows?.length" class="empty-state">
                <el-empty description="暂无数据" />
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 节点性能 -->
        <el-col :span="12">
          <el-card>
            <template #header>
              <span>节点性能分析</span>
            </template>
            <div class="node-performance">
              <el-table 
                :data="statistics.nodePerformance?.slice(0, 10)" 
                size="small"
                max-height="400"
              >
                <el-table-column prop="nodeId" label="节点ID" width="120" />
                <el-table-column prop="executionCount" label="执行次数" width="80" />
                <el-table-column label="平均时间" width="100">
                  <template #default="{ row }">
                    {{ formatDuration(row.averageTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="最短时间" width="100">
                  <template #default="{ row }">
                    {{ formatDuration(row.minTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="最长时间">
                  <template #default="{ row }">
                    {{ formatDuration(row.maxTime) }}
                  </template>
                </el-table-column>
              </el-table>
              
              <div v-if="!statistics.nodePerformance?.length" class="empty-state">
                <el-empty description="暂无数据" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  DataAnalysis,
  CircleCheck,
  Timer,
  Operation
} from '@element-plus/icons-vue'

import workflowAPI from '@/services/workflowAPI'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const selectedPeriod = ref('month')
const statistics = reactive({
  totalExecutions: 0,
  successRate: 0,
  averageExecutionTime: 0,
  popularWorkflows: [],
  executionTrends: [],
  nodePerformance: []
})

// 图表引用
const trendChart = ref()
const successChart = ref()

// 方法
const goBack = () => {
  router.push('/workflow-templates')
}

const loadStatistics = async () => {
  loading.value = true
  try {
    const response = await workflowAPI.getStatistics({
      period: selectedPeriod.value
    })
    
    Object.assign(statistics, response)
    
    // 更新图表
    await nextTick()
    renderCharts()
  } catch (error) {
    ElMessage.error('获取统计数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const renderCharts = () => {
  renderTrendChart()
  renderSuccessChart()
}

const renderTrendChart = () => {
  if (!trendChart.value || !statistics.executionTrends?.length) return
  
  // 这里可以集成 ECharts 或其他图表库
  // 简化版本：显示文本数据
  const chartData = statistics.executionTrends.map(item => ({
    date: new Date(item.date).toLocaleDateString('zh-CN'),
    count: item.count,
    successful: item.successful
  }))
  
  trendChart.value.innerHTML = `
    <div style="padding: 20px; text-align: center;">
      <p>执行趋势数据</p>
      <div style="font-size: 12px; color: #666;">
        ${chartData.map(item => 
          `${item.date}: ${item.count}次执行 (${item.successful}次成功)`
        ).join('<br>')}
      </div>
    </div>
  `
}

const renderSuccessChart = () => {
  if (!successChart.value) return
  
  const successCount = Math.round(statistics.totalExecutions * statistics.successRate / 100)
  const failureCount = statistics.totalExecutions - successCount
  
  successChart.value.innerHTML = `
    <div style="padding: 20px; text-align: center;">
      <div style="margin-bottom: 20px;">
        <div style="display: inline-block; width: 60px; height: 60px; border-radius: 50%; background: conic-gradient(#67c23a 0deg ${statistics.successRate * 3.6}deg, #f56c6c ${statistics.successRate * 3.6}deg 360deg); position: relative;">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-weight: bold;">
            ${statistics.successRate}%
          </div>
        </div>
      </div>
      <div style="font-size: 12px; color: #666;">
        <div style="color: #67c23a;">成功: ${successCount}次</div>
        <div style="color: #f56c6c;">失败: ${failureCount}次</div>
      </div>
    </div>
  `
}

// 工具方法
const formatDuration = (ms) => {
  if (!ms) return '0秒'
  
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 生命周期
onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.workflow-statistics {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.statistics-overview {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.time {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.workflows {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popular-workflows {
  max-height: 400px;
  overflow-y: auto;
}

.workflow-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.workflow-item:last-child {
  border-bottom: none;
}

.workflow-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
}

.workflow-info {
  flex: 1;
}

.workflow-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.workflow-category {
  font-size: 12px;
  color: #666;
}

.workflow-stats {
  text-align: right;
}

.recent-executions {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.total-usage {
  font-size: 12px;
  color: #666;
}

.node-performance {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>
