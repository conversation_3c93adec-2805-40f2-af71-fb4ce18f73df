<template>
  <div class="workflow-templates">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>创作流程配置</h1>
        <p>设计和管理您的AI小说创作流程</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          创建流程
        </el-button>

        <el-button @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入流程
        </el-button>

        <el-button @click="$router.push('/workflow-execution-history')">
          <el-icon><Clock /></el-icon>
          执行历史
        </el-button>

        <el-button @click="$router.push('/workflow-statistics')">
          <el-icon><TrendCharts /></el-icon>
          统计分析
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="filters.category" placeholder="选择分类" clearable @change="fetchTemplates">
            <el-option label="全部分类" value="" />
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.name"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.isPublic" placeholder="模板类型" clearable @change="fetchTemplates">
            <el-option label="全部模板" value="" />
            <el-option label="我的模板" value="false" />
            <el-option label="公开模板" value="true" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-input
            v-model="filters.search"
            placeholder="搜索流程名称或描述"
            @keyup.enter="fetchTemplates"
            @clear="fetchTemplates"
            clearable
          >
            <template #append>
              <el-button @click="fetchTemplates">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 模板列表 -->
    <div class="templates-grid" v-loading="loading">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        @click="viewTemplate(template)"
      >
        <!-- 模板缩略图 -->
        <div class="template-thumbnail">
          <img v-if="template.thumbnail" :src="template.thumbnail" alt="流程缩略图" />
          <div v-else class="placeholder-thumbnail">
            <el-icon size="48"><Setting /></el-icon>
          </div>
        </div>

        <!-- 模板信息 -->
        <div class="template-info">
          <div class="template-header">
            <h3 class="template-name">{{ template.name }}</h3>
            <div class="template-badges">
              <el-tag v-if="template.isPublic" type="success" size="small">公开</el-tag>
              <el-tag v-if="template.category" size="small">{{ template.category }}</el-tag>
            </div>
          </div>
          
          <p class="template-description">{{ template.description || '暂无描述' }}</p>
          
          <div class="template-tags" v-if="template.tags && template.tags.length">
            <el-tag
              v-for="tag in template.tags.slice(0, 3)"
              :key="tag"
              size="small"
              effect="plain"
            >
              {{ tag }}
            </el-tag>
            <span v-if="template.tags.length > 3" class="more-tags">+{{ template.tags.length - 3 }}</span>
          </div>

          <div class="template-meta">
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>{{ template.User?.username || '未知' }}</span>
            </div>
            <div class="meta-item">
              <el-icon><Clock /></el-icon>
              <span>{{ formatDate(template.updatedAt) }}</span>
            </div>
            <div class="meta-item">
              <el-icon><View /></el-icon>
              <span>{{ template.usageCount || 0 }} 次使用</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="template-actions" @click.stop>
          <el-dropdown @command="handleAction">
            <el-button link>
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="{action: 'view', template}">查看详情</el-dropdown-item>
                <el-dropdown-item :command="{action: 'execute', template}">执行流程</el-dropdown-item>
                <el-dropdown-item :command="{action: 'copy', template}">复制流程</el-dropdown-item>
                <el-dropdown-item :command="{action: 'export', template}">导出流程</el-dropdown-item>
                <el-dropdown-item
                  v-if="canEdit(template)"
                  :command="{action: 'edit', template}"
                >
                  编辑流程
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="canDelete(template)"
                  :command="{action: 'delete', template}"
                  divided
                >
                  删除流程
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="total > 0">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="total"
        :page-sizes="[12, 24, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchTemplates"
        @current-change="fetchTemplates"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && templates.length === 0" class="empty-state">
      <el-empty description="暂无流程模板">
        <el-button type="primary" @click="showCreateDialog">创建第一个流程</el-button>
      </el-empty>
    </div>

    <!-- 创建流程对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建流程模板"
      width="600px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入流程名称" />
        </el-form-item>
        <el-form-item label="流程描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入流程描述"
          />
        </el-form-item>
        <el-form-item label="流程分类" prop="category">
          <el-select v-model="createForm.category" placeholder="选择分类">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-input
            v-model="tagInput"
            placeholder="输入标签后按回车添加"
            @keyup.enter="addTag"
          />
          <div class="tags-display" v-if="createForm.tags.length">
            <el-tag
              v-for="tag in createForm.tags"
              :key="tag"
              closable
              @close="removeTag(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="公开模板" v-if="userRole === 'admin'">
          <el-switch v-model="createForm.isPublic" />
          <span class="form-tip">公开模板所有用户都可以查看和使用</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createTemplate" :loading="creating">创建</el-button>
      </template>
    </el-dialog>

    <!-- 导入流程对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入流程模板"
      width="600px"
      @close="resetImportForm"
    >
      <el-form :model="importForm" :rules="importRules" ref="importFormRef" label-width="100px">
        <el-form-item label="流程文件" prop="file">
          <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            accept=".json"
            :on-change="handleFileChange"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
          </el-upload>
          <div v-if="importForm.fileName" class="file-info">
            已选择文件: {{ importForm.fileName }}
          </div>
        </el-form-item>

        <el-form-item label="流程名称" prop="name">
          <el-input
            v-model="importForm.name"
            placeholder="留空则使用原始名称"
          />
        </el-form-item>

        <el-form-item label="公开设置">
          <el-switch
            v-model="importForm.makePublic"
            active-text="公开"
            inactive-text="私有"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="importTemplate" :loading="importing">导入</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Setting, User, Clock, View, MoreFilled, Upload, TrendCharts } from '@element-plus/icons-vue'
import api from '@/services/api'
import workflowAPI from '@/services/workflowAPI'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const importing = ref(false)
const templates = ref([])
const categories = ref([])
const total = ref(0)

// 筛选条件
const filters = reactive({
  category: '',
  isPublic: '',
  search: ''
})

// 分页
const pagination = reactive({
  page: 1,
  limit: 12
})

// 创建对话框
const createDialogVisible = ref(false)
const createFormRef = ref()
const tagInput = ref('')

const createForm = reactive({
  name: '',
  description: '',
  category: '',
  tags: [],
  isPublic: false
})

const createRules = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择流程分类', trigger: 'change' }
  ]
}

// 导入对话框
const importDialogVisible = ref(false)
const importFormRef = ref()
const uploadRef = ref()

const importForm = reactive({
  file: null,
  fileName: '',
  name: '',
  makePublic: false
})

const importRules = {
  file: [
    { required: true, message: '请选择要导入的文件', trigger: 'change' }
  ]
}

// 计算属性
const userRole = computed(() => authStore.user?.role)

// 权限检查
const canEdit = (template) => {
  return userRole.value === 'admin' || template.userId === authStore.user?.id
}

const canDelete = (template) => {
  return userRole.value === 'admin' || template.userId === authStore.user?.id
}

// 方法
const fetchTemplates = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...filters
    }

    const response = await workflowAPI.getTemplates(params)
    templates.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取流程模板失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const fetchCategories = async () => {
  try {
    const response = await workflowAPI.getCategories()
    categories.value = response.data
  } catch (error) {
    console.error('获取分类失败:', error)
  }
}

const resetFilters = () => {
  Object.assign(filters, {
    category: '',
    isPublic: '',
    search: ''
  })
  pagination.page = 1
  fetchTemplates()
}

const showCreateDialog = () => {
  createDialogVisible.value = true
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    description: '',
    category: '',
    tags: [],
    isPublic: false
  })
  tagInput.value = ''
  createFormRef.value?.resetFields()
}

const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !createForm.tags.includes(tag)) {
    createForm.tags.push(tag)
    tagInput.value = ''
  }
}

const removeTag = (tag) => {
  const index = createForm.tags.indexOf(tag)
  if (index > -1) {
    createForm.tags.splice(index, 1)
  }
}

const createTemplate = async () => {
  try {
    await createFormRef.value.validate()
    creating.value = true

    await workflowAPI.createTemplate(createForm)
    ElMessage.success('流程模板创建成功')
    createDialogVisible.value = false
    resetCreateForm()
    fetchTemplates()
  } catch (error) {
    if (error.errors) return // 表单验证错误
    ElMessage.error('创建流程模板失败')
    console.error(error)
  } finally {
    creating.value = false
  }
}

const viewTemplate = (template) => {
  router.push(`/workflow-designer/${template.id}`)
}

const handleAction = async ({ action, template }) => {
  switch (action) {
    case 'view':
      viewTemplate(template)
      break
    case 'execute':
      router.push(`/workflow-execution?templateId=${template.id}`)
      break
    case 'copy':
      await copyTemplate(template)
      break
    case 'export':
      await exportTemplate(template)
      break
    case 'edit':
      router.push(`/workflow-designer/${template.id}?mode=edit`)
      break
    case 'delete':
      await deleteTemplate(template)
      break
  }
}

const copyTemplate = async (template) => {
  try {
    const newTemplate = {
      name: `${template.name} - 副本`,
      description: template.description,
      category: template.category,
      config: template.config,
      tags: template.tags
    }

    await workflowAPI.createTemplate(newTemplate)
    ElMessage.success('流程模板复制成功')
    fetchTemplates()
  } catch (error) {
    ElMessage.error('复制流程模板失败')
    console.error(error)
  }
}

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除流程模板"${template.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await workflowAPI.deleteTemplate(template.id)
    ElMessage.success('流程模板删除成功')
    fetchTemplates()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('删除流程模板失败')
    console.error(error)
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 导入导出方法
const showImportDialog = () => {
  importDialogVisible.value = true
}

const resetImportForm = () => {
  importForm.file = null
  importForm.fileName = ''
  importForm.name = ''
  importForm.makePublic = false
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const handleFileChange = (file) => {
  importForm.file = file.raw
  importForm.fileName = file.name
}

const importTemplate = async () => {
  try {
    await importFormRef.value.validate()

    if (!importForm.file) {
      ElMessage.error('请选择要导入的文件')
      return
    }

    importing.value = true

    // 读取文件内容
    const fileContent = await readFileAsText(importForm.file)
    const workflowData = JSON.parse(fileContent)

    // 验证文件格式
    if (!workflowData.workflow) {
      throw new Error('文件格式不正确，缺少workflow字段')
    }

    // 调用导入API
    await workflowAPI.importTemplate({
      workflow: workflowData.workflow,
      name: importForm.name,
      makePublic: importForm.makePublic
    })

    ElMessage.success('流程导入成功')
    importDialogVisible.value = false
    resetImportForm()
    fetchTemplates()
  } catch (error) {
    if (error.errors) return // 表单验证错误
    ElMessage.error('导入流程失败: ' + (error.message || '未知错误'))
    console.error(error)
  } finally {
    importing.value = false
  }
}

const exportTemplate = async (template) => {
  try {
    const response = await workflowAPI.exportTemplate(template.id)

    // 创建下载链接
    const blob = new Blob([JSON.stringify(response, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${template.name}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('流程导出成功')
  } catch (error) {
    ElMessage.error('导出流程失败')
    console.error(error)
  }
}

const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsText(file)
  })
}

// 生命周期
onMounted(() => {
  fetchCategories()
  fetchTemplates()
})
</script>

<style scoped>
.workflow-templates {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #666;
}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.template-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.template-thumbnail {
  height: 120px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-thumbnail {
  color: #ccc;
}

.template-info {
  padding: 16px;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.template-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.template-badges {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.template-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-tags {
  margin-bottom: 12px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.more-tags {
  font-size: 12px;
  color: #999;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.template-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover .template-actions {
  opacity: 1;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.tags-display {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

.file-info {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  font-size: 12px;
  color: #1e40af;
}
</style>
