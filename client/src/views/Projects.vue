<template>
  <PageLoading page-key="projects" @retry="fetchProjects">
    <div class="projects-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <span class="title-decoration">📚</span>
            项目管理
            <span class="title-decoration">📚</span>
          </h1>
          <p class="page-subtitle">管理您的小说创作项目，如藏书楼般井然有序</p>
        </div>
        <div class="header-right">
          <el-button type="primary" size="large" @click="showCreateDialog" class="create-btn">
            <el-icon><Plus /></el-icon>
            创建新项目
          </el-button>
        </div>
      </div>
    </div>

    <!-- 项目统计卡片 -->
    <div class="stats-cards" v-if="projects.length > 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ projects.length }}</div>
              <div class="stat-label">总项目数</div>
            </div>
            <el-icon class="stat-icon"><Collection /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ completedProjects }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon success"><CircleCheck /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ inProgressProjects }}</div>
              <div class="stat-label">进行中</div>
            </div>
            <el-icon class="stat-icon warning"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ totalWordCount.toLocaleString() }}</div>
              <div class="stat-label">总字数</div>
            </div>
            <el-icon class="stat-icon info"><EditPen /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 项目列表 -->
    <el-card class="projects-card" v-if="projects.length > 0">
      <template #header>
        <div class="card-header">
          <span class="card-title">项目列表</span>
          <div class="header-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索项目..."
              style="width: 200px; margin-right: 10px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="filterType" placeholder="筛选类型" style="width: 120px;" clearable>
              <el-option label="全部" value="" />
              <el-option label="奇幻" value="fantasy" />
              <el-option label="科幻" value="sci-fi" />
              <el-option label="武侠" value="wuxia" />
              <el-option label="都市" value="urban" />
              <el-option label="历史" value="historical" />
              <el-option label="悬疑" value="mystery" />
              <el-option label="其他" value="other" />
            </el-select>
          </div>
        </div>
      </template>

      <el-table
        :data="filteredProjects"
        style="width: 100%"
        class="projects-table"
        stripe
        @row-click="handleRowClick"
      >
        <el-table-column width="60">
          <template #default="scope">
            <div class="project-avatar">
              <el-icon class="avatar-icon" :style="{ color: getTypeColor(scope.row.type) }">
                <Document />
              </el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="项目信息" min-width="200">
          <template #default="scope">
            <div class="project-info">
              <div class="project-name">{{ scope.row.name }}</div>
              <div class="project-meta">
                <el-tag :type="getTypeTagType(scope.row.type)" size="small">
                  {{ getTypeLabel(scope.row.type) }}
                </el-tag>
                <span class="meta-divider">·</span>
                <span class="target-audience">{{ scope.row.targetAudience }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="进度状态" width="150">
          <template #default="scope">
            <div class="progress-info">
              <template v-if="scope.row.statsLoading">
                <el-skeleton :rows="1" animated />
              </template>
              <template v-else>
                <el-progress
                  :percentage="getProjectProgress(scope.row)"
                  :color="getProgressColor(scope.row)"
                  :stroke-width="8"
                />
                <div class="progress-text">
                  {{ getProjectProgress(scope.row) }}% 完成
                </div>
              </template>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="统计信息" width="120">
          <template #default="scope">
            <div class="stats-info">
              <template v-if="scope.row.statsLoading">
                <el-skeleton :rows="2" animated />
              </template>
              <template v-else>
                <div class="stat-item">
                  <el-icon><EditPen /></el-icon>
                  <span>{{ getProjectWordCount(scope.row).toLocaleString() }}字</span>
                </div>
                <div class="stat-item">
                  <el-icon><Document /></el-icon>
                  <span>{{ getProjectChapterCount(scope.row) }}章</span>
                </div>
              </template>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="120">
          <template #default="scope">
            <div class="date-info">
              {{ formatDate(scope.row.createdAt) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-dropdown @command="(command) => handleQuickAction(command, scope.row)">
                <el-button type="primary" size="small">
                  快速操作
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="worldSettings">
                      <el-icon><Compass /></el-icon>
                      世界观设定
                    </el-dropdown-item>
                    <el-dropdown-item command="characters">
                      <el-icon><User /></el-icon>
                      角色管理
                    </el-dropdown-item>
                    <el-dropdown-item command="outlines">
                      <el-icon><List /></el-icon>
                      大纲管理
                    </el-dropdown-item>
                    <el-dropdown-item command="chapters">
                      <el-icon><Document /></el-icon>
                      章节管理
                    </el-dropdown-item>
                    <el-dropdown-item divided command="volumes">
                      <el-icon><Collection /></el-icon>
                      分卷剧情管理
                    </el-dropdown-item>
                    <el-dropdown-item command="clues">
                      <el-icon><Key /></el-icon>
                      线索管理
                    </el-dropdown-item>
                    <el-dropdown-item divided command="preview">
                      <el-icon><View /></el-icon>
                      预览小说
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <el-button
                type="danger"
                size="small"
                plain
                @click.stop="confirmDelete(scope.row)"
                class="delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 空状态 -->
    <el-card v-else class="empty-card">
      <el-empty description="暂无项目">
        <template #image>
          <el-icon class="empty-icon"><Document /></el-icon>
        </template>
        <template #description>
          <p>您还没有创建任何项目</p>
          <p class="empty-subtitle">开始您的第一个小说创作项目吧！</p>
        </template>
        <el-button type="primary" @click="showCreateDialog" size="large">
          <el-icon><Plus /></el-icon>
          创建新项目
        </el-button>
      </el-empty>
    </el-card>

    <!-- 创建项目对话框 -->
    <el-dialog v-model="createDialogVisible" title="创建新项目" width="50%">
      <el-form :model="newProject" label-width="120px" :rules="rules" ref="projectForm">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="newProject.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="小说类型" prop="type">
          <el-select v-model="newProject.type" placeholder="请选择小说类型" style="width: 100%">
            <el-option label="奇幻" value="fantasy" />
            <el-option label="科幻" value="sci-fi" />
            <el-option label="武侠" value="wuxia" />
            <el-option label="都市" value="urban" />
            <el-option label="历史" value="historical" />
            <el-option label="悬疑" value="mystery" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="预计字数" prop="wordCount">
          <el-input-number v-model="newProject.wordCount" :min="1" :step="10000" :step-strictly="true" style="width: 100%" />
        </el-form-item>
        <el-form-item label="目标读者" prop="targetAudience">
          <el-input v-model="newProject.targetAudience" placeholder="请输入目标读者群体" />
        </el-form-item>
        <el-form-item label="写作风格" prop="style">
          <el-input v-model="newProject.style" placeholder="请输入核心写作风格" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="createProject"
            :loading="getOperationState('createProject').loading"
          >
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>
    </div>
  </PageLoading>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document, Plus, Collection, CircleCheck, Clock, EditPen,
  Search, Compass, User, List, Key, View, Delete, ArrowDown
} from '@element-plus/icons-vue';
import { projectAPI } from '../services/api';
import PageLoading from '../components/PageLoading.vue';
import { usePageLoading, useOperationLoading } from '../composables/useLoading';

const router = useRouter();
const projects = ref([]);
const createDialogVisible = ref(false);
const projectForm = ref(null);
const searchKeyword = ref('');
const filterType = ref('');

// 加载状态管理
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('projects');
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading();

const newProject = ref({
  name: '',
  type: '',
  wordCount: 100000,
  targetAudience: '',
  style: ''
});

const rules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择小说类型', trigger: 'change' }],
  wordCount: [{ required: true, message: '请输入预计字数', trigger: 'blur' }],
  targetAudience: [{ required: true, message: '请输入目标读者群体', trigger: 'blur' }],
  style: [{ required: true, message: '请输入核心写作风格', trigger: 'blur' }]
};

// 计算属性
const filteredProjects = computed(() => {
  let filtered = projects.value;

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(project =>
      project.name.toLowerCase().includes(keyword) ||
      project.targetAudience.toLowerCase().includes(keyword)
    );
  }

  // 按类型筛选
  if (filterType.value) {
    filtered = filtered.filter(project => project.type === filterType.value);
  }

  return filtered;
});

const completedProjects = computed(() => {
  return projects.value.filter(project => {
    const progress = project.stats?.progress || 0;
    return progress === 100;
  }).length;
});

const inProgressProjects = computed(() => {
  return projects.value.filter(project => {
    const progress = project.stats?.progress || 0;
    return progress > 0 && progress < 100;
  }).length;
});

const totalWordCount = computed(() => {
  return projects.value.reduce((total, project) => {
    // 使用实际字数，如果没有则使用预计字数
    return total + (project.stats?.actualWordCount || project.wordCount || 0);
  }, 0);
});

// 从后端获取项目列表
onMounted(async () => {
  await fetchProjects();
});

// 获取项目列表（优化版本）
const fetchProjects = async () => {
  try {
    showPageLoading('正在加载项目列表...');

    // 首先快速加载项目基本信息
    const basicData = await projectAPI.getProjectsBasic();
    projects.value = basicData.map(project => ({
      ...project,
      stats: null, // 初始化统计信息为null
      statsLoading: true // 标记统计信息正在加载
    }));

    hidePageLoading();

    // 异步加载统计信息
    if (basicData.length > 0) {
      const projectIds = basicData.map(p => p.id);
      try {
        const statsData = await projectAPI.getBatchProjectStats(projectIds);

        // 更新项目的统计信息
        projects.value = projects.value.map(project => ({
          ...project,
          stats: statsData[project.id] || {},
          statsLoading: false
        }));
      } catch (error) {
        console.error('获取统计信息失败:', error);
        // 统计信息加载失败时，设置默认值
        projects.value = projects.value.map(project => ({
          ...project,
          stats: {
            totalChapters: 0,
            completedChapters: 0,
            draftChapters: 0,
            actualWordCount: 0,
            progress: 0,
            totalVolumes: 0,
            characterCount: 0,
            outlineCount: 0,
            clueCount: 0
          },
          statsLoading: false
        }));
      }
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);
    setPageError(error);
    ElMessage.error('获取项目列表失败');
  }
};

const showCreateDialog = () => {
  createDialogVisible.value = true;
};

const createProject = () => {
  projectForm.value.validate(async (valid) => {
    if (valid) {
      try {
        showOperationLoading('createProject', '正在创建项目...');

        // 调用API创建项目
        const createdProject = await projectAPI.createProject(newProject.value);

        // 刷新项目列表
        await fetchProjects();

        ElMessage.success('项目创建成功');
        createDialogVisible.value = false;

        // 重置表单
        newProject.value = {
          name: '',
          type: '',
          wordCount: 100000,
          targetAudience: '',
          style: ''
        };
      } catch (error) {
        console.error('创建项目失败:', error);
        ElMessage.error('创建项目失败');
      } finally {
        hideOperationLoading('createProject');
      }
    } else {
      return false;
    }
  });
};

const navigateTo = (routeName, projectId) => {
  router.push({ name: routeName, params: { projectId } });
};

const confirmDelete = (project) => {
  ElMessageBox.confirm(
    `确定要删除项目 "${project.name}" 吗？此操作不可逆。`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // 调用API删除项目
      await projectAPI.deleteProject(project.id);

      // 刷新项目列表
      await fetchProjects();

      ElMessage.success('项目删除成功');
    } catch (error) {
      console.error('删除项目失败:', error);
      ElMessage.error('删除项目失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

// 预览小说功能
const previewNovel = (projectId) => {
  router.push({ name: 'NovelReader', params: { projectId } });
};

// 获取项目类型颜色
const getTypeColor = (type) => {
  const colors = {
    fantasy: '#722ed1',
    'sci-fi': '#1890ff',
    wuxia: '#fa8c16',
    urban: '#52c41a',
    historical: '#faad14',
    mystery: '#eb2f96',
    other: '#8c8c8c'
  };
  return colors[type] || colors.other;
};

// 获取项目类型标签类型
const getTypeTagType = (type) => {
  const types = {
    fantasy: '',
    'sci-fi': 'primary',
    wuxia: 'warning',
    urban: 'success',
    historical: 'warning',
    mystery: 'danger',
    other: 'info'
  };
  return types[type] || types.other;
};

// 获取项目类型标签
const getTypeLabel = (type) => {
  const labels = {
    fantasy: '奇幻',
    'sci-fi': '科幻',
    wuxia: '武侠',
    urban: '都市',
    historical: '历史',
    mystery: '悬疑',
    other: '其他'
  };
  return labels[type] || '其他';
};

// 获取项目进度（基于真实数据）
const getProjectProgress = (project) => {
  // 使用后端计算的真实进度
  return project.stats?.progress || 0;
};

// 获取进度条颜色
const getProgressColor = (project) => {
  const progress = getProjectProgress(project);
  if (progress >= 80) return '#67c23a';
  if (progress >= 50) return '#e6a23c';
  return '#f56c6c';
};

// 获取项目章节数（基于真实数据）
const getProjectChapterCount = (project) => {
  // 使用后端统计的真实章节数
  return project.stats?.totalChapters || 0;
};

// 获取项目字数（优先使用实际字数）
const getProjectWordCount = (project) => {
  // 优先使用实际字数，如果没有则使用预计字数
  return project.stats?.actualWordCount || project.wordCount || 0;
};

// 预览相关状态
const previewDialogVisible = ref(false);
const previewProject = ref(null);

// 处理行点击 - 打开预览弹窗
const handleRowClick = (row) => {
  previewProject.value = row;
  previewDialogVisible.value = true;
};

// 处理快速操作
const handleQuickAction = (command, project) => {
  const actions = {
    worldSettings: () => navigateTo('WorldSettings', project.id),
    characters: () => navigateTo('Characters', project.id),
    outlines: () => navigateTo('Outlines', project.id),
    chapters: () => navigateTo('Chapters', project.id),
    volumes: () => navigateTo('Volumes', project.id),
    clues: () => navigateTo('Clues', project.id),
    preview: () => previewNovel(project.id)
  };

  if (actions[command]) {
    actions[command]();
  }
};
</script>

<style scoped>
.projects-container {
  padding: 24px;
  background: var(--bg-gradient);
  min-height: calc(100vh - 60px);
  position: relative;
}

.projects-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(218, 165, 32, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(139, 69, 19, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* 页面头部样式 - 古典风格 */
.page-header {
  background: linear-gradient(45deg, var(--ink-black) 0%, var(--wood-brown) 50%, var(--ink-black) 100%);
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 30px;
  color: var(--paper-cream);
  box-shadow: var(--shadow-heavy);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2.2rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  color: var(--primary-gold);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 3px;
  font-family: var(--font-classical);
}

.title-decoration {
  font-size: 1.8rem;
  animation: sparkle 2s ease-in-out infinite alternate;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  text-align: center;
  color: var(--paper-cream);
  font-style: italic;
  letter-spacing: 1px;
}

.create-btn {
  height: 50px;
  padding: 0 30px;
  font-size: 1rem;
  border-radius: 25px;
  background: linear-gradient(145deg, var(--primary-gold), var(--secondary-gold)) !important;
  border: none !important;
  color: white !important;
  font-weight: 600;
  letter-spacing: 1px;
  box-shadow: 0 6px 20px rgba(218, 165, 32, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.create-btn:hover::before {
  left: 100%;
}

.create-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(218, 165, 32, 0.5);
}

/* 统计卡片样式 - 古典风格 */
.stats-cards {
  margin-bottom: 30px;
  position: relative;
  z-index: 2;
}

.stat-card {
  border-radius: 16px;
  border: var(--border-light);
  background: linear-gradient(145deg, var(--paper-white), #f8f4e6);
  box-shadow: var(--shadow-light);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-gold), var(--secondary-gold), var(--primary-gold));
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--primary-gold);
}

.stat-card :deep(.el-card__body) {
  padding: 30px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.stat-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-brown);
  line-height: 1;
  margin-bottom: 8px;
  font-family: var(--font-classical);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--light-brown);
  font-weight: 500;
  letter-spacing: 1px;
}

.stat-icon {
  font-size: 3.5rem;
  opacity: 0.15;
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
  opacity: 0.25;
  transform: translateY(-50%) scale(1.1);
}

.stat-icon.success {
  color: #52c41a;
}

.stat-icon.warning {
  color: var(--primary-gold);
}

.stat-icon.info {
  color: var(--secondary-gold);
}

/* 项目卡片样式 - 古典风格 */
.projects-card {
  border-radius: 16px;
  border: var(--border-light);
  background: linear-gradient(145deg, var(--paper-white), #f8f4e6);
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.projects-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary-gold), var(--secondary-gold), var(--primary-gold));
}

.projects-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #faf7f0, #f0ead6);
  border-bottom: 2px solid var(--primary-gold);
  padding: 25px 30px;
  position: relative;
}

.projects-card :deep(.el-card__header)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 0%, rgba(218, 165, 32, 0.05) 50%, transparent 100%);
  pointer-events: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.card-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--dark-brown);
  letter-spacing: 1px;
  font-family: var(--font-classical);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 表格样式 - 古典风格 */
.projects-table {
  border-radius: 12px;
  overflow: hidden;
  background: var(--paper-white);
}

.projects-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #f5deb3, #daa520);
}

.projects-table :deep(.el-table__header th) {
  background: linear-gradient(135deg, #f5deb3, #daa520);
  color: var(--ink-black);
  font-weight: 600;
  border-bottom: 2px solid var(--secondary-gold);
  font-family: var(--font-classical);
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.projects-table :deep(.el-table__row) {
  transition: all 0.3s ease;
  background: var(--paper-white);
}

.projects-table :deep(.el-table__row:hover) {
  background: linear-gradient(135deg, #faf7f0, #f0ead6);
  cursor: pointer;
  transform: scale(1.01);
}

.projects-table :deep(.el-table__row:nth-child(even)) {
  background: linear-gradient(135deg, #fefcf7, #f8f4e6);
}

.projects-table :deep(.el-table__row:nth-child(even):hover) {
  background: linear-gradient(135deg, #f5f1eb, #ede4d3);
}

/* 项目信息样式 - 古典风格 */
.project-avatar {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.project-avatar:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 16px rgba(218, 165, 32, 0.4);
}

.avatar-icon {
  font-size: 22px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.project-info {
  padding: 10px 0;
}

.project-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-brown);
  margin-bottom: 6px;
  line-height: 1.4;
  font-family: var(--font-classical);
  letter-spacing: 0.5px;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.85rem;
  color: var(--light-brown);
}

.meta-divider {
  color: var(--primary-gold);
  font-weight: bold;
}

.target-audience {
  color: var(--light-brown);
  font-style: italic;
}

/* 进度信息样式 - 古典风格 */
.progress-info {
  padding: 10px 0;
}

.progress-info :deep(.el-progress) {
  margin-bottom: 6px;
}

.progress-info :deep(.el-progress__text) {
  color: var(--dark-brown) !important;
  font-weight: 600;
}

.progress-text {
  font-size: 0.75rem;
  color: var(--light-brown);
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 统计信息样式 - 古典风格 */
.stats-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 5px 0;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--dark-brown);
  font-weight: 500;
  transition: all 0.3s ease;
}

.stat-item:hover {
  color: var(--primary-gold);
  transform: translateX(3px);
}

.stat-item .el-icon {
  font-size: 16px;
  color: var(--secondary-gold);
}

/* 日期信息样式 - 古典风格 */
.date-info {
  font-size: 0.8rem;
  color: var(--light-brown);
  padding: 10px 0;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.delete-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态样式 - 古典风格 */
.empty-card {
  border-radius: 16px;
  border: var(--border-light);
  background: linear-gradient(145deg, var(--paper-white), #f8f4e6);
  box-shadow: var(--shadow-light);
  text-align: center;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.empty-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-gold), var(--secondary-gold), var(--primary-gold));
}

.empty-card :deep(.el-empty) {
  padding: 80px 50px;
}

.empty-icon {
  font-size: 5rem;
  color: var(--primary-gold);
  margin-bottom: 25px;
  opacity: 0.6;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
  animation: sparkle 3s ease-in-out infinite alternate;
}

.empty-card :deep(.el-empty__description) {
  margin: 25px 0;
}

.empty-card :deep(.el-empty__description p) {
  margin: 12px 0;
  font-size: 1.1rem;
  color: var(--dark-brown);
  font-family: var(--font-classical);
  letter-spacing: 0.5px;
}

.empty-subtitle {
  font-size: 1rem;
  color: var(--light-brown);
  font-style: italic;
  letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-cards .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .projects-container {
    padding: 16px;
  }

  .page-header {
    padding: 24px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .page-title {
    font-size: 24px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .header-actions .el-input,
  .header-actions .el-select {
    width: 100% !important;
  }
}
</style>
