<template>
  <PageLoading page-key="characters" @retry="fetchCharacters">
    <div class="characters-container">
    <div class="header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>角色管理</h2>
      </div>
      <el-button type="primary" @click="showCreateDialog">创建角色</el-button>
    </div>

    <!-- 角色列表 -->
    <el-table :data="characters" style="width: 100%">
      <el-table-column prop="name" label="角色名称" width="120" show-overflow-tooltip />
      <el-table-column prop="role" label="角色定位" width="120" show-overflow-tooltip />
      <el-table-column prop="gender" label="性别" width="80">
        <template #default="scope">
          <el-tag :type="getGenderType(scope.row.gender)">
            {{ scope.row.gender || '未设定' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="personality" label="性格特征" show-overflow-tooltip />
      <el-table-column label="是否主角团" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isMainCharacter ? 'success' : 'info'">
            {{ scope.row.isMainCharacter ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="重要程度" width="100">
        <template #default="scope">
          <el-progress :percentage="scope.row.importance" :format="format" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建角色' : '编辑角色'"
      v-model="dialogVisible"
      width="50%"
    >
      <el-form :model="characterForm" label-width="100px">
        <el-form-item label="角色名称">
          <el-input v-model="characterForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色定位">
          <el-input v-model="characterForm.role" placeholder="请输入角色定位" />
        </el-form-item>
        <el-form-item label="性别">
          <el-select v-model="characterForm.gender" placeholder="请选择性别">
            <el-option label="男" value="男" />
            <el-option label="女" value="女" />
            <el-option label="其他" value="其他" />
            <el-option label="未设定" value="未设定" />
          </el-select>
        </el-form-item>
        <el-form-item label="性格特征">
          <el-input
            type="textarea"
            v-model="characterForm.personality"
            placeholder="请描述角色的性格特征"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="背景故事">
          <el-input
            type="textarea"
            v-model="characterForm.background"
            placeholder="请描述角色的背景故事"
            :rows="5"
          />
        </el-form-item>
        <el-form-item label="能力设定">
          <el-input
            type="textarea"
            v-model="characterForm.abilities_detail"
            placeholder="请描述角色的能力设定"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="人物弧光">
          <el-input
            type="textarea"
            v-model="characterForm.characterArc"
            placeholder="请描述角色在故事中的成长轨迹和变化过程"
            :rows="4"
          />
        </el-form-item>
        <el-form-item label="是否主角团">
          <el-switch v-model="characterForm.isMainCharacter" />
        </el-form-item>
        <el-form-item label="重要程度">
          <el-slider v-model="characterForm.importance" :min="1" :max="100" :step="1" show-input />
        </el-form-item>

        <!-- AI生成功能 -->
        <el-form-item label="AI生成">
          <div class="ai-generation-section">
            <el-button
              type="success"
              @click="showAIGenerationDialog"
              :disabled="!characterForm.name"
            >
              <el-icon><Star /></el-icon>
              AI生成角色内容
            </el-button>
            <el-text type="info" size="small" style="margin-left: 10px;">
              需要先填写角色名称
            </el-text>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI生成对话框 -->
    <AIGenerationDialog
      v-model:visible="aiGenerationDialogVisible"
      content-type="角色"
      :project-id="projectId"
      :existing-content="getExistingCharacterContent()"
      :context-data="getCharacterContextData()"
      role-filter="character_developer"
      @content-generated="handleAIContentGenerated"
    />
    </div>
  </PageLoading>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star } from '@element-plus/icons-vue'
import { characterAPI } from '@/services/api'
import { useRoute, useRouter } from 'vue-router'
import AIGenerationDialog from '@/components/AIGenerationDialog.vue'
import PageLoading from '@/components/PageLoading.vue'
import { usePageLoading, useOperationLoading } from '@/composables/useLoading'

export default {
  name: 'Characters',
  components: {
    AIGenerationDialog
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectId = route.params.projectId

    // 返回上一页方法
    const goBack = () => {
      router.go(-1)
    }

    // 角色列表数据
    const characters = ref([])

    // 对话框相关数据
    const dialogVisible = ref(false)
    const dialogType = ref('create')
    const characterForm = ref({
      name: '',
      role: '',
      gender: '未设定',
      personality: '',
      background: '',
      abilities: '',
      abilities_detail: '',
      characterArc: '',
      relationships: '',
      isMainCharacter: false,
      importance: 50,
      projectId: projectId
    })

    // AI生成相关数据
    const aiGenerationDialogVisible = ref(false)

    // 加载状态管理
    const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('characters')
    const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

    // 获取角色列表
    const fetchCharacters = async () => {
      try {
        showPageLoading('正在加载角色列表...')
        const response = await characterAPI.getAllCharacters(projectId)
        // 检查response是否是数组或者包含data属性
        if (Array.isArray(response)) {
          characters.value = response
        } else if (response && response.data) {
          characters.value = response.data
        } else {
          console.error('获取角色列表返回的数据格式不正确:', response)
          ElMessage.error('获取角色列表返回的数据格式不正确')
          characters.value = []
        }
        hidePageLoading()
      } catch (error) {
        setPageError(error)
        ElMessage.error('获取角色列表失败')
        console.error(error)
      }
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      dialogType.value = 'create'
      characterForm.value = {
        name: '',
        role: '',
        gender: '未设定',
        personality: '',
        background: '',
        abilities: '',
        abilities_detail: '',
        characterArc: '',
        relationships: '',
        isMainCharacter: false,
        importance: 50,
        projectId: projectId
      }
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (character) => {
      dialogType.value = 'edit'
      console.log('🔧 编辑角色 - 原始数据:', character)
      characterForm.value = {
        ...character,
        // 确保新字段有默认值
        gender: character.gender || '未设定',
        characterArc: character.characterArc || ''
      }
      console.log('🔧 编辑角色 - 表单数据:', characterForm.value)
      dialogVisible.value = true
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        console.log('🔧 提交表单 - 类型:', dialogType.value)
        console.log('🔧 提交表单 - 数据:', JSON.stringify(characterForm.value, null, 2))
        console.log('🔧 提交表单 - 性别:', characterForm.value.gender)
        console.log('🔧 提交表单 - 人物弧光:', characterForm.value.characterArc)

        if (dialogType.value === 'create') {
          await characterAPI.createCharacter(characterForm.value)
          ElMessage.success('创建角色成功')
        } else {
          await characterAPI.updateCharacter(characterForm.value.id, characterForm.value)
          ElMessage.success('更新角色成功')
        }
        dialogVisible.value = false
        fetchCharacters()
      } catch (error) {
        ElMessage.error(dialogType.value === 'create' ? '创建角色失败' : '更新角色失败')
        console.error(error)
      }
    }

    // 删除角色
    const handleDelete = async (character) => {
      try {
        await ElMessageBox.confirm('确定要删除这个角色吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await characterAPI.deleteCharacter(character.id)
        ElMessage.success('删除角色成功')
        fetchCharacters()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除角色失败')
          console.error(error)
        }
      }
    }

    // 组件挂载时获取角色列表
    onMounted(() => {
      if (projectId) {
        fetchCharacters()
      } else {
        ElMessage.warning('未指定项目ID')
      }
    })

    // 格式化重要程度显示
    const format = (percentage) => {
      return `${percentage}%`
    }

    // 获取性别标签颜色
    const getGenderType = (gender) => {
      switch (gender) {
        case '男':
          return 'primary'
        case '女':
          return 'danger'
        case '其他':
          return 'warning'
        default:
          return 'info'
      }
    }

    // AI生成相关方法
    const showAIGenerationDialog = () => {
      if (!characterForm.value.name) {
        ElMessage.warning('请先填写角色名称')
        return
      }
      aiGenerationDialogVisible.value = true
    }

    const getExistingCharacterContent = () => {
      const parts = []
      if (characterForm.value.personality) parts.push(`性格特征: ${characterForm.value.personality}`)
      if (characterForm.value.background) parts.push(`背景故事: ${characterForm.value.background}`)
      if (characterForm.value.abilities_detail) parts.push(`能力设定: ${characterForm.value.abilities_detail}`)
      if (characterForm.value.characterArc) parts.push(`人物弧光: ${characterForm.value.characterArc}`)
      return parts.join('\n\n')
    }

    const getCharacterContextData = () => {
      return {
        characterName: characterForm.value.name,
        characterRole: characterForm.value.role,
        characterGender: characterForm.value.gender,
        isMainCharacter: characterForm.value.isMainCharacter,
        importance: characterForm.value.importance
      }
    }

    const handleAIContentGenerated = ({ content, action }) => {
      if (action === 'replace') {
        // 尝试解析生成的内容并分配到不同字段
        parseAndAssignCharacterContent(content)
      } else if (action === 'append') {
        // 追加到背景故事字段
        if (characterForm.value.background) {
          characterForm.value.background += '\n\n' + content
        } else {
          characterForm.value.background = content
        }
      }
      ElMessage.success('AI生成内容已应用')
    }

    const parseAndAssignCharacterContent = (content) => {
      // 尝试解析结构化的角色内容
      const lines = content.split('\n')
      let currentField = 'background'
      let currentContent = ''

      for (const line of lines) {
        const trimmedLine = line.trim()
        if (trimmedLine.includes('性格特征') || trimmedLine.includes('性格：')) {
          if (currentContent) {
            assignContentToField(currentField, currentContent.trim())
          }
          currentField = 'personality'
          currentContent = ''
        } else if (trimmedLine.includes('背景故事') || trimmedLine.includes('背景：')) {
          if (currentContent) {
            assignContentToField(currentField, currentContent.trim())
          }
          currentField = 'background'
          currentContent = ''
        } else if (trimmedLine.includes('能力设定') || trimmedLine.includes('能力：')) {
          if (currentContent) {
            assignContentToField(currentField, currentContent.trim())
          }
          currentField = 'abilities_detail'
          currentContent = ''
        } else if (trimmedLine.includes('人物弧光') || trimmedLine.includes('成长轨迹')) {
          if (currentContent) {
            assignContentToField(currentField, currentContent.trim())
          }
          currentField = 'characterArc'
          currentContent = ''
        } else if (trimmedLine && !trimmedLine.includes('：') && !trimmedLine.includes(':')) {
          currentContent += (currentContent ? '\n' : '') + trimmedLine
        }
      }

      // 处理最后一个字段
      if (currentContent) {
        assignContentToField(currentField, currentContent.trim())
      }

      // 如果没有识别到结构化内容，则全部放到背景故事中
      if (!characterForm.value.personality && !characterForm.value.background &&
          !characterForm.value.abilities_detail && !characterForm.value.characterArc) {
        characterForm.value.background = content
      }
    }

    const assignContentToField = (field, content) => {
      if (content) {
        characterForm.value[field] = content
      }
    }

    return {
      characters,
      dialogVisible,
      dialogType,
      characterForm,
      aiGenerationDialogVisible,
      showCreateDialog,
      showEditDialog,
      handleSubmit,
      handleDelete,
      goBack,
      format,
      getGenderType,
      showAIGenerationDialog,
      getExistingCharacterContent,
      getCharacterContextData,
      handleAIContentGenerated,
      projectId
    }
  }
}
</script>

<style scoped>
.characters-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.ai-generation-section {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
