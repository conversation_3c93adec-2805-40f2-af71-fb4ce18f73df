<template>
  <div class="workflow-execution-history">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" link>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>执行历史</h1>
      </div>
      <div class="header-right">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filters">
      <el-card>
        <el-form :model="filters" inline>
          <el-form-item label="流程模板">
            <el-select 
              v-model="filters.workflowId" 
              placeholder="选择流程模板"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="template in templates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="执行状态">
            <el-select 
              v-model="filters.status" 
              placeholder="选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="已取消" value="cancelled" />
              <el-option label="已暂停" value="paused" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="关联项目">
            <el-select 
              v-model="filters.projectId" 
              placeholder="选择项目"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 执行历史列表 -->
    <div class="execution-list">
      <el-card>
        <el-table 
          :data="executions" 
          v-loading="loading"
          stripe
          @row-click="viewExecution"
          style="cursor: pointer"
        >
          <el-table-column prop="executionId" label="执行ID" width="200">
            <template #default="{ row }">
              <el-link type="primary" @click.stop="viewExecution(row)">
                {{ row.executionId }}
              </el-link>
            </template>
          </el-table-column>
          
          <el-table-column prop="WorkflowTemplate.name" label="流程模板" width="200" />
          
          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.progress || 0" 
                :status="getProgressStatus(row.status)"
                :stroke-width="6"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="Project.name" label="关联项目" width="150">
            <template #default="{ row }">
              {{ row.Project?.name || '无' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="startedAt" label="开始时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.startedAt) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="completedAt" label="完成时间" width="180">
            <template #default="{ row }">
              {{ row.completedAt ? formatTime(row.completedAt) : '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="执行时长" width="120">
            <template #default="{ row }">
              {{ formatDuration(getExecutionDuration(row)) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                size="small" 
                @click.stop="viewExecution(row)"
              >
                查看详情
              </el-button>
              
              <el-button 
                v-if="row.status === 'completed'"
                type="success" 
                size="small" 
                @click.stop="reExecute(row)"
              >
                重新执行
              </el-button>
              
              <el-button 
                v-if="['running', 'paused'].includes(row.status)"
                type="warning" 
                size="small" 
                @click.stop="cancelExecution(row)"
              >
                取消
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  Search
} from '@element-plus/icons-vue'

import workflowAPI from '@/services/workflowAPI'
import api from '@/services/api'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const executions = ref([])
const templates = ref([])
const projects = ref([])
const total = ref(0)

const filters = reactive({
  workflowId: null,
  status: null,
  projectId: null
})

const pagination = reactive({
  page: 1,
  limit: 20
})

// 方法
const goBack = () => {
  router.push('/workflow-templates')
}

const loadExecutions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...filters
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === '') {
        delete params[key]
      }
    })
    
    const response = await workflowAPI.getExecutionHistory(params)
    executions.value = response.data
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取执行历史失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const loadTemplates = async () => {
  try {
    const response = await workflowAPI.getTemplates()
    templates.value = response.data
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

const loadProjects = async () => {
  try {
    const response = await api.get('/projects')
    projects.value = response
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadExecutions()
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = null
  })
  pagination.page = 1
  loadExecutions()
}

const handleSizeChange = (size) => {
  pagination.limit = size
  pagination.page = 1
  loadExecutions()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadExecutions()
}

const refreshData = () => {
  loadExecutions()
}

const viewExecution = (execution) => {
  router.push(`/workflow-execution/${execution.executionId}`)
}

const reExecute = async (execution) => {
  try {
    await ElMessageBox.confirm(
      '确定要重新执行此流程吗？',
      '确认重新执行',
      {
        confirmButtonText: '重新执行',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    const response = await workflowAPI.executeWorkflow(execution.workflowId, {
      inputData: execution.inputData,
      projectId: execution.projectId
    })
    
    ElMessage.success('流程重新执行已启动')
    
    // 跳转到新的执行页面
    router.push(`/workflow-execution/${response.executionId}`)
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('重新执行失败')
    console.error(error)
  }
}

const cancelExecution = async (execution) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消此流程执行吗？此操作不可恢复。',
      '确认取消',
      {
        confirmButtonText: '取消执行',
        cancelButtonText: '继续执行',
        type: 'warning'
      }
    )
    
    await workflowAPI.cancelExecution(execution.executionId)
    ElMessage.success('流程执行已取消')
    loadExecutions()
  } catch (error) {
    if (error === 'cancel') return
    ElMessage.error('取消执行失败')
    console.error(error)
  }
}

// 工具方法
const getStatusType = (status) => {
  const types = {
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'warning',
    paused: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    running: '运行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    paused: '已暂停'
  }
  return texts[status] || status
}

const getProgressStatus = (status) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  return null
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatDuration = (ms) => {
  if (!ms) return '-'
  
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

const getExecutionDuration = (execution) => {
  if (!execution.startedAt) return 0
  
  const startTime = new Date(execution.startedAt)
  const endTime = execution.completedAt ? new Date(execution.completedAt) : new Date()
  return endTime - startTime
}

// 生命周期
onMounted(() => {
  loadExecutions()
  loadTemplates()
  loadProjects()
})
</script>

<style scoped>
.workflow-execution-history {
  padding: 20px;
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-right {
  display: flex;
  gap: 8px;
}

.filters {
  margin-bottom: 24px;
}

.execution-list {
  margin-bottom: 24px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 表格样式 */
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-progress-bar__outer) {
  background-color: #e4e7ed;
}

:deep(.el-progress-bar__inner) {
  transition: width 0.3s ease;
}
</style>
