<template>
  <div class="chapter-groups-container">
    <div class="header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>章节分组管理</h2>
      </div>
      <div class="header-info">
        <span>分卷：{{ volumeTitle }}</span>
        <el-button type="primary" @click="showCreateDialog">创建章节分组</el-button>
      </div>
    </div>

    <!-- 章节分组列表 -->
    <el-table :data="chapterGroups" style="width: 100%">
      <el-table-column prop="title" label="分组标题" />
      <el-table-column prop="chapterRange" label="章节范围" />
      <el-table-column prop="progressRate" label="推动度">
        <template #default="scope">
          <el-progress :percentage="scope.row.progressRate" />
        </template>
      </el-table-column>
      <el-table-column label="剧情梗概">
        <template #default="scope">
          <div class="summary-preview">{{ getSummaryPreview(scope.row.summary) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280">
        <template #default="scope">
          <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          <el-button size="small" type="primary" @click="showVersionsDialog(scope.row)">历史版本</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑章节分组对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建章节分组' : '编辑章节分组'"
      v-model="dialogVisible"
      width="80%"
    >
      <el-form :model="chapterGroupForm" label-width="100px" :rules="rules" ref="chapterGroupFormRef">
        <el-form-item label="分组标题" prop="title">
          <el-input v-model="chapterGroupForm.title" placeholder="请输入分组标题" />
        </el-form-item>
        <el-form-item label="章节范围" prop="chapterRange">
          <el-input v-model="chapterGroupForm.chapterRange" placeholder="例如：1-20章" />
        </el-form-item>
        <el-form-item label="剧情梗概" prop="summary">
          <div class="summary-input-container">
            <el-input
              type="textarea"
              v-model="chapterGroupForm.summary"
              placeholder="请输入分组剧情梗概"
              :rows="10"
            />
            <div class="ai-generate-section">
              <el-button
                type="primary"
                size="small"
                @click="showAIGenerationDialog"
                :disabled="!chapterGroupForm.title"
              >
                AI生成梗概
              </el-button>
              <span class="ai-tip">需要先填写分组标题</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="推动度" prop="progressRate">
          <el-slider v-model="chapterGroupForm.progressRate" :min="0" :max="100" :step="5" show-stops />
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="chapterGroupForm.order" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史版本对话框 -->
    <el-dialog
      title="历史版本"
      v-model="versionsDialogVisible"
      width="80%"
    >
      <div v-if="isLoadingVersions" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="chapterGroupVersions.length === 0" class="empty-versions">
        暂无历史版本
      </div>
      <div v-else>
        <el-select v-model="selectedVersionId" placeholder="选择版本查看内容" @change="handleVersionSelect" style="width: 100%; margin-bottom: 15px;">
          <el-option
            v-for="version in chapterGroupVersions"
            :key="version.id"
            :label="`版本 ${version.versionNumber} (${new Date(version.createdAt).toLocaleString()})`"
            :value="version.id"
          />
        </el-select>
        
        <div v-if="isLoadingVersionContent" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>
        <div v-else-if="selectedVersionContent" class="version-content">
          <h3>{{ selectedVersionContent.title }}</h3>
          <div class="version-info">
            <p><strong>章节范围:</strong> {{ selectedVersionContent.chapterRange }}</p>
            <p><strong>推动度:</strong> {{ selectedVersionContent.progressRate }}%</p>
          </div>
          <div class="version-summary">
            <h4>剧情梗概:</h4>
            <p style="white-space: pre-line">{{ selectedVersionContent.summary }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- AI生成对话框 -->
    <AIGenerationDialog
      v-model:visible="aiGenerationDialogVisible"
      content-type="章节分组"
      :project-id="projectId"
      :existing-content="getExistingChapterGroupContent()"
      :context-data="getChapterGroupContextData()"
      role-filter="writer"
      @content-generated="handleAIContentGenerated"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { chapterGroupAPI, volumeAPI } from '@/services/api'
import { useRoute, useRouter } from 'vue-router'
import AIGenerationDialog from '@/components/AIGenerationDialog.vue'

export default {
  name: 'VolumeChapterGroups',
  components: {
    AIGenerationDialog
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectId = route.params.projectId
    const volumeId = route.params.volumeId

    // 返回上一页方法
    const goBack = () => {
      router.go(-1)
    }

    // 分卷信息
    const volumeTitle = ref('')

    // 章节分组列表数据
    const chapterGroups = ref([])

    // 对话框相关数据
    const dialogVisible = ref(false)
    const dialogType = ref('create')
    const chapterGroupFormRef = ref(null)
    const chapterGroupForm = ref({
      title: '',
      chapterRange: '',
      summary: '',
      progressRate: 0,
      order: 0,
      volumeId: volumeId
    })

    // 表单验证规则
    const rules = {
      title: [{ required: true, message: '请输入分组标题', trigger: 'blur' }],
      chapterRange: [{ required: true, message: '请输入章节范围', trigger: 'blur' }],
      summary: [{ required: true, message: '请输入剧情梗概', trigger: 'blur' }]
    }

    // 获取分卷信息
    const fetchVolumeInfo = async () => {
      try {
        const response = await volumeAPI.getVolume(volumeId)
        volumeTitle.value = response.title
      } catch (error) {
        ElMessage.error('获取分卷信息失败')
        console.error(error)
      }
    }

    // 获取章节分组列表
    const fetchChapterGroups = async () => {
      try {
        const response = await chapterGroupAPI.getAllChapterGroups(volumeId)
        chapterGroups.value = response
      } catch (error) {
        ElMessage.error('获取章节分组列表失败')
        console.error(error)
      }
    }

    // 获取梗概预览
    const getSummaryPreview = (summary) => {
      if (!summary) return '无梗概'
      return summary.length > 50 ? summary.substring(0, 50) + '...' : summary
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      dialogType.value = 'create'
      chapterGroupForm.value = {
        title: '',
        chapterRange: '',
        summary: '',
        progressRate: 0,
        order: chapterGroups.value.length,
        volumeId: volumeId
      }
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (chapterGroup) => {
      dialogType.value = 'edit'
      chapterGroupForm.value = { ...chapterGroup }
      dialogVisible.value = true
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!chapterGroupFormRef.value) return

      chapterGroupFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            if (dialogType.value === 'create') {
              await chapterGroupAPI.createChapterGroup(chapterGroupForm.value)
              ElMessage.success('创建章节分组成功')
            } else {
              await chapterGroupAPI.updateChapterGroup(chapterGroupForm.value.id, chapterGroupForm.value)
              
              // 创建新版本
              try {
                await chapterGroupAPI.createChapterGroupVersion(chapterGroupForm.value.id, {
                  title: chapterGroupForm.value.title,
                  chapterRange: chapterGroupForm.value.chapterRange,
                  summary: chapterGroupForm.value.summary,
                  progressRate: chapterGroupForm.value.progressRate
                })
                ElMessage.success('更新章节分组并创建新版本成功')
              } catch (versionError) {
                console.error('创建章节分组版本失败:', versionError)
                ElMessage.warning('章节分组更新成功，但创建新版本失败')
              }
            }
            dialogVisible.value = false
            fetchChapterGroups()
          } catch (error) {
            ElMessage.error(dialogType.value === 'create' ? '创建章节分组失败' : '更新章节分组失败')
            console.error(error)
          }
        }
      })
    }

    // 删除章节分组
    const handleDelete = async (chapterGroup) => {
      try {
        await ElMessageBox.confirm('确定要删除这个章节分组吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await chapterGroupAPI.deleteChapterGroup(chapterGroup.id)
        ElMessage.success('删除章节分组成功')
        fetchChapterGroups()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除章节分组失败')
          console.error(error)
        }
      }
    }

    // 添加版本管理相关的状态
    const versionsDialogVisible = ref(false)
    const chapterGroupVersions = ref([])
    const selectedVersionId = ref(null)
    const selectedVersionContent = ref(null)
    const isLoadingVersions = ref(false)
    const isLoadingVersionContent = ref(false)
    const currentChapterGroupId = ref(null)

    // AI生成相关数据
    const aiGenerationDialogVisible = ref(false)

    // 显示版本历史对话框
    const showVersionsDialog = async (chapterGroup) => {
      versionsDialogVisible.value = true
      currentChapterGroupId.value = chapterGroup.id
      selectedVersionId.value = null
      selectedVersionContent.value = null
      await fetchChapterGroupVersions(chapterGroup.id)
    }

    // 获取章节分组版本列表
    const fetchChapterGroupVersions = async (chapterGroupId) => {
      if (!chapterGroupId) return
      isLoadingVersions.value = true
      try {
        const response = await chapterGroupAPI.getChapterGroupVersions(chapterGroupId)
        chapterGroupVersions.value = Array.isArray(response) ? response : (response.data || [])
      } catch (error) {
        ElMessage.error('获取章节分组版本列表失败')
        console.error('获取章节分组版本列表失败:', error)
        chapterGroupVersions.value = []
      } finally {
        isLoadingVersions.value = false
      }
    }

    // 获取选中版本的内容
    const handleVersionSelect = async (versionId) => {
      if (!versionId || !currentChapterGroupId.value) return
      isLoadingVersionContent.value = true
      selectedVersionContent.value = null
      try {
        const response = await chapterGroupAPI.getChapterGroupVersion(currentChapterGroupId.value, versionId)
        selectedVersionContent.value = response
      } catch (error) {
        ElMessage.error('获取版本内容失败')
        console.error('获取版本内容失败:', error)
        selectedVersionContent.value = null
      } finally {
        isLoadingVersionContent.value = false
      }
    }

    // AI生成相关方法
    const showAIGenerationDialog = () => {
      if (!chapterGroupForm.value.title) {
        ElMessage.warning('请先填写分组标题')
        return
      }
      aiGenerationDialogVisible.value = true
    }

    // 获取现有章节分组内容（用于AI生成时的上下文）
    const getExistingChapterGroupContent = () => {
      const form = chapterGroupForm.value
      let content = ''

      if (form.title) {
        content += `分组标题: ${form.title}\n`
      }
      if (form.chapterRange) {
        content += `章节范围: ${form.chapterRange}\n`
      }
      if (form.summary) {
        content += `现有梗概: ${form.summary}\n`
      }
      if (form.progressRate !== undefined) {
        content += `推动度: ${form.progressRate}%\n`
      }

      return content
    }

    // 获取章节分组上下文数据（用于AI生成）
    const getChapterGroupContextData = () => {
      return {
        volumeTitle: volumeTitle.value,
        chapterGroupTitle: chapterGroupForm.value.title,
        chapterRange: chapterGroupForm.value.chapterRange,
        progressRate: chapterGroupForm.value.progressRate,
        existingGroups: chapterGroups.value.map(group => ({
          title: group.title,
          chapterRange: group.chapterRange,
          summary: group.summary,
          progressRate: group.progressRate
        }))
      }
    }

    // 处理AI生成的内容
    const handleAIContentGenerated = (data) => {
      if (data.action === 'replace') {
        chapterGroupForm.value.summary = data.content
      } else if (data.action === 'append') {
        const currentContent = chapterGroupForm.value.summary || ''
        chapterGroupForm.value.summary = currentContent + (currentContent ? '\n\n' : '') + data.content
      }
      ElMessage.success('AI生成内容已应用')
    }

    // 组件挂载时获取数据
    onMounted(() => {
      if (volumeId) {
        fetchVolumeInfo()
        fetchChapterGroups()
      } else {
        ElMessage.warning('未指定分卷ID')
      }
    })

    return {
      volumeTitle,
      chapterGroups,
      dialogVisible,
      dialogType,
      chapterGroupForm,
      chapterGroupFormRef,
      rules,
      showCreateDialog,
      showEditDialog,
      handleSubmit,
      handleDelete,
      getSummaryPreview,
      goBack,
      versionsDialogVisible,
      chapterGroupVersions,
      selectedVersionId,
      selectedVersionContent,
      isLoadingVersions,
      isLoadingVersionContent,
      showVersionsDialog,
      handleVersionSelect,
      aiGenerationDialogVisible,
      showAIGenerationDialog,
      getExistingChapterGroupContent,
      getChapterGroupContextData,
      handleAIContentGenerated,
      projectId
    }
  }
}
</script>

<style scoped>
.chapter-groups-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.summary-preview {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 添加版本管理相关样式 */
.loading-container {
  padding: 20px;
  text-align: center;
}

.empty-versions {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 16px;
}

.version-content {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.version-info {
  margin: 15px 0;
  color: #606266;
}

.version-summary {
  margin-top: 20px;
}

.version-summary h4 {
  margin-bottom: 10px;
  color: #303133;
}

/* AI生成相关样式 */
.summary-input-container {
  position: relative;
}

.ai-generate-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.ai-tip {
  font-size: 12px;
  color: #909399;
}
</style>
