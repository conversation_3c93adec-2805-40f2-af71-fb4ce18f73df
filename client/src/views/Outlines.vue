<template>
  <PageLoading page-key="outlines" @retry="fetchOutlines">
    <div class="outlines-container">
    <div class="header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>大纲管理</h2>
      </div>
      <el-button type="primary" @click="showCreateDialog">创建大纲</el-button>
    </div>

    <!-- 大纲列表 -->
    <el-table :data="outlines" style="width: 100%">
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="level" label="层级">
        <template #default="scope">
          <span>{{ formatLevel(scope.row.level) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="内容预览">
        <template #default="scope">
          <div class="content-preview">{{ getContentPreview(scope.row.content) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑大纲对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建大纲' : '编辑大纲'"
      v-model="dialogVisible"
      width="70%"
    >
      <el-form :model="outlineForm" label-width="100px">
        <el-form-item label="标题">
          <el-input v-model="outlineForm.title" placeholder="请输入大纲标题" />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="outlineForm.type" placeholder="请选择大纲类型">
            <el-option label="故事大纲" value="story" />
            <el-option label="章节大纲" value="chapter" />
            <el-option label="情节大纲" value="plot" />
          </el-select>
        </el-form-item>
        <el-form-item label="层级">
          <el-select v-model="outlineForm.level" placeholder="请选择大纲层级">
            <el-option label="一级大纲" value="level1" />
            <el-option label="二级大纲" value="level2" />
            <el-option label="三级大纲" value="level3" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容">
          <el-input
            type="textarea"
            v-model="outlineForm.content"
            placeholder="请输入大纲内容"
            :rows="10"
          />
        </el-form-item>

        <!-- AI生成功能 -->
        <el-form-item label="AI生成">
          <div class="ai-generation-section">
            <el-button
              type="success"
              @click="showAIGenerationDialog"
              :disabled="!outlineForm.title"
            >
              <el-icon><Star /></el-icon>
              AI生成大纲内容
            </el-button>
            <el-text type="info" size="small" style="margin-left: 10px;">
              需要先填写大纲标题
            </el-text>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI生成对话框 -->
    <AIGenerationDialog
      v-model:visible="aiGenerationDialogVisible"
      content-type="大纲"
       :project-id="projectId"
      :existing-content="outlineForm.content"
      :context-data="getOutlineContextData()"
      role-filter="writer"
      @content-generated="handleAIContentGenerated"
    />
    </div>
  </PageLoading>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star } from '@element-plus/icons-vue'
import { outlineAPI } from '@/services/api'
import { useRoute, useRouter } from 'vue-router'
import AIGenerationDialog from '@/components/AIGenerationDialog.vue'
import PageLoading from '@/components/PageLoading.vue'
import { usePageLoading, useOperationLoading } from '@/composables/useLoading'

export default {
  name: 'Outlines',
  components: {
    AIGenerationDialog,
    PageLoading
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectId = route.params.projectId

    // 返回上一页方法
    const goBack = () => {
      router.go(-1)
    }

    // 大纲列表数据
    const outlines = ref([])

    // 对话框相关数据
    const dialogVisible = ref(false)
    const dialogType = ref('create')
    const outlineForm = ref({
      title: '',
      type: 'story',
      level: 'level1', // 添加层级字段，默认为一级大纲
      content: '',
      projectId: projectId
    })

    // AI生成相关数据
    const aiGenerationDialogVisible = ref(false)

    // 加载状态管理
    const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('outlines')
    const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

    // 获取大纲列表
    const fetchOutlines = async () => {
      try {
        showPageLoading('正在加载大纲列表...')
        const response = await outlineAPI.getAllOutlines(projectId)
        // 由于axios拦截器已经处理了response.data，所以response就是数据本身
        outlines.value = response
        console.log('获取到的大纲列表:', outlines.value)
        hidePageLoading()
      } catch (error) {
        setPageError(error)
        ElMessage.error('获取大纲列表失败')
        console.error('获取大纲列表错误:', error)
      }
    }

    // 获取内容预览
    const getContentPreview = (content) => {
      if (!content) return '无内容'
      return content.length > 50 ? content.substring(0, 50) + '...' : content
    }

    // 格式化层级显示
    const formatLevel = (level) => {
      if (!level) return '未知层级'
      const levelMap = {
        'level1': '一级大纲',
        'level2': '二级大纲',
        'level3': '三级大纲'
      }
      return levelMap[level] || level
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      dialogType.value = 'create'
      outlineForm.value = {
        title: '',
        type: 'story',
        level: 'level1', // 添加层级字段，默认为一级大纲
        content: '',
        projectId: projectId
      }
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (outline) => {
      dialogType.value = 'edit'
      outlineForm.value = { ...outline }
      dialogVisible.value = true
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        if (dialogType.value === 'create') {
          await outlineAPI.createOutline(outlineForm.value)
          ElMessage.success('创建大纲成功')
        } else {
          await outlineAPI.updateOutline(outlineForm.value.id, outlineForm.value)
          ElMessage.success('更新大纲成功')
        }
        dialogVisible.value = false
        fetchOutlines()
      } catch (error) {
        ElMessage.error(dialogType.value === 'create' ? '创建大纲失败' : '更新大纲失败')
        console.error(error)
      }
    }

    // 删除大纲
    const handleDelete = async (outline) => {
      try {
        await ElMessageBox.confirm('确定要删除这个大纲吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await outlineAPI.deleteOutline(outline.id)
        ElMessage.success('删除大纲成功')
        fetchOutlines()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除大纲失败')
          console.error(error)
        }
      }
    }

    // 组件挂载时获取大纲列表
    onMounted(() => {
      if (projectId) {
        fetchOutlines()
      } else {
        ElMessage.warning('未指定项目ID')
      }
    })

    // AI生成相关方法
    const showAIGenerationDialog = () => {
      if (!outlineForm.value.title) {
        ElMessage.warning('请先填写大纲标题')
        return
      }
      aiGenerationDialogVisible.value = true
    }

    const getOutlineContextData = () => {
      return {
        outlineTitle: outlineForm.value.title,
        outlineType: outlineForm.value.type,
        outlineLevel: outlineForm.value.level
      }
    }

    const handleAIContentGenerated = ({ content, action }) => {
      if (action === 'replace') {
        outlineForm.value.content = content
      } else if (action === 'append') {
        if (outlineForm.value.content) {
          outlineForm.value.content += '\n\n' + content
        } else {
          outlineForm.value.content = content
        }
      }
      ElMessage.success('AI生成内容已应用')
    }

    return {
      outlines,
      dialogVisible,
      dialogType,
      outlineForm,
      aiGenerationDialogVisible,
      projectId,
      showCreateDialog,
      showEditDialog,
      handleSubmit,
      handleDelete,
      getContentPreview,
      formatLevel,
      goBack,
      showAIGenerationDialog,
      getOutlineContextData,
      handleAIContentGenerated,
      fetchOutlines
    }
  }
}
</script>

<style scoped>
.outlines-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.content-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.ai-generation-section {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
