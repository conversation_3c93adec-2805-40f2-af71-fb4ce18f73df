<template>
  <PageLoading page-key="prompt-generator" @retry="fetchTemplates">
    <div class="prompt-generator-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-left">
          <el-button
            icon="ArrowLeft"
            @click="goBack"
            class="back-btn"
          >
            返回
          </el-button>
          <div class="page-title">
            <h1>提示词生成器</h1>
            <p class="page-subtitle">创建和管理AI提示词模板，提升创作效率</p>
          </div>
        </div>
        <el-button
          type="primary"
          @click="showCreateDialog"
          class="create-btn"
          icon="Plus"
        >
          创建模板
        </el-button>
      </div>

    <div class="content-layout">
        <!-- 左侧模板面板 -->
        <div class="templates-panel">
          <div class="panel-header">
            <h3>模板库</h3>
            <span class="template-count">{{ filteredTemplates.length }} 个模板</span>
          </div>

          <!-- 搜索框 -->
          <el-input
            v-model="searchQuery"
            placeholder="搜索模板名称或描述..."
            prefix-icon="Search"
            clearable
            class="search-input"
          />

          <!-- 角色分类卡片 -->
          <div class="role-categories">
            <div class="category-header" @click="toggleCategoryExpanded">
              <div class="category-title">
                <span>按角色分类</span>
                <el-tag v-if="activeRole !== 'all'" size="small" type="primary" class="active-filter-tag">
                  {{ getRoleName(activeRole) }}
                </el-tag>
              </div>
              <div class="category-toggle">
                <el-icon class="toggle-icon" :class="{ expanded: categoryExpanded }">
                  <ArrowDown />
                </el-icon>
              </div>
            </div>
            <el-collapse-transition>
              <div v-show="categoryExpanded" class="category-cards">
                <div
                  v-for="category in roleCategories"
                  :key="category.value"
                  :class="['category-card', { active: activeRole === category.value }]"
                  @click="handleRoleChange(category.value)"
                >
                  <div class="category-icon">
                    <el-icon><component :is="category.icon" /></el-icon>
                  </div>
                  <div class="category-info">
                    <div class="category-name">{{ category.label }}</div>
                    <div class="category-count">{{ getCategoryCount(category.value) }}</div>
                  </div>
                </div>
              </div>
            </el-collapse-transition>
          </div>

          <!-- 模板列表 -->
          <div class="templates-list">
            <div class="list-header">
              <span>模板列表</span>
              <el-tag v-if="activeRole !== 'all'" size="small" type="primary">
                {{ getRoleName(activeRole) }}
              </el-tag>
            </div>

            <el-scrollbar height="calc(100vh - 520px)" class="template-scrollbar">
              <div v-if="filteredTemplates.length === 0" class="empty-state">
                <el-empty
                  description="暂无模板"
                  :image-size="80"
                />
              </div>
              <div v-else class="template-items">
                <div
                  v-for="template in filteredTemplates"
                  :key="template.id"
                  :class="['template-item', { active: activeTemplateId === template.id }]"
                  @click="handleTemplateSelect(template.id)"
                >
                  <div class="template-content">
                    <div class="template-header">
                      <h4 class="template-name">{{ template.name }}</h4>
                      <el-tag
                        size="small"
                        :type="getRoleTagType(template.role)"
                        class="role-tag"
                      >
                        {{ getRoleName(template.role) }}
                      </el-tag>
                    </div>
                    <p class="template-description">
                      {{ template.description || '暂无描述' }}
                    </p>
                  </div>
                  <div class="template-actions">
                    <el-button
                      size="small"
                      type="primary"
                      text
                      @click.stop="showEditDialog(template)"
                      class="action-btn edit-btn"
                    >
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button
                      size="small"
                      type="danger"
                      text
                      @click.stop="handleDelete(template)"
                      class="action-btn delete-btn"
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
      </div>

      <!-- 生成面板 -->
      <div class="generator-panel">
        <div v-if="selectedTemplate" class="template-info">
          <h3>{{ selectedTemplate.name }}</h3>
          <p class="template-description">{{ selectedTemplate.description }}</p>

          <el-divider content-position="left">项目选择</el-divider>
          <el-form :model="generatorForm" label-width="120px">
            <el-form-item label="项目">
              <el-select v-model="generatorForm.projectId" placeholder="选择项目" clearable @change="handleProjectChange">
                <el-option
                  v-for="project in projects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </el-select>
            </el-form-item>

            <template v-if="generatorForm.projectId && false">
              <el-form-item label="角色">
                <el-select v-model="generatorForm.characterIds" multiple placeholder="选择角色" clearable>
                  <el-option
                    v-for="character in characters"
                    :key="character.id"
                    :label="character.name"
                    :value="character.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="世界观设定">
                <el-select v-model="generatorForm.worldSettingIds" multiple placeholder="选择世界观设定" clearable>
                  <el-option
                    v-for="worldSetting in worldSettings"
                    :key="worldSetting.id"
                    :label="worldSetting.title"
                    :value="worldSetting.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="大纲">
                <el-select v-model="generatorForm.outlineIds" multiple placeholder="选择大纲" clearable>
                  <el-option
                    v-for="outline in outlines"
                    :key="outline.id"
                    :label="outline.title"
                    :value="outline.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="章节">
                <el-select v-model="generatorForm.chapterId" placeholder="选择章节" clearable @change="handleChapterChange">
                  <el-option
                    v-for="chapter in chapters"
                    :key="chapter.id"
                    :label="chapter.title"
                    :value="chapter.id"
                  />
                </el-select>
                <div class="form-item-tip" v-if="generatorForm.chapterId">
                  <el-icon><InfoFilled /></el-icon>
                  <span>已选择章节，将自动包含章节内容和编辑意见</span>
                </div>
              </el-form-item>
            </template>

            <el-divider content-position="left">参数设置</el-divider>

            <el-form-item
              v-for="(param, index) in selectedTemplate.parameters"
              :key="index"
              :label="param.name"
            >
              <el-input
                v-if="param.type === 'text'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description"
              />
              <el-input-number
                v-else-if="param.type === 'number'"
                v-model="generatorForm.params[param.key]"
                :min="param.min"
                :max="param.max"
              />
              <el-select
                v-else-if="param.type === 'select'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description"
              >
                <el-option
                  v-for="option in param.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'worldSetting'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择世界观设定'"
                :multiple="param.multiple"
              >
                <el-option
                  v-for="item in worldSettings"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'character'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择角色'"
                :multiple="param.multiple"
              >
                <el-option
                  v-for="item in characters"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'outline'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择大纲'"
                :multiple="param.multiple"
              >
                <el-option
                  v-for="item in outlines"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'chapter'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择章节'"
                :multiple="param.multiple"
              >
                <el-option
                  v-for="item in chapters"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'volume'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择分卷'"
                :multiple="param.multiple"
                @change="handleVolumeChange(param.key)"
              >
                <el-option
                  v-for="item in volumes"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'chapterGroup'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择章节分组'"
                :multiple="param.multiple"
                @click="handleChapterGroupClick(param)"
              >
                <el-option
                  v-for="item in chapterGroups[param.key] || []"
                  :key="item.id"
                  :label="item.title"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'clue'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择线索'"
                :multiple="param.multiple"
              >
                <el-option
                  v-for="item in clues"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-select
                v-else-if="param.type === 'system' && param.systemType === 'editorComment'"
                v-model="generatorForm.params[param.key]"
                :placeholder="param.description || '选择章节编辑意见'"
                :multiple="param.multiple"
                @click="handleEditorCommentClick(param)"
              >
                <el-option
                  v-for="item in editorComments[param.key] || []"
                  :key="item.id"
                  :label="item.content.substring(0, 30) + (item.content.length > 30 ? '...' : '')"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="generatePrompt"
                :loading="generating"
              >生成提示词</el-button>
            </el-form-item>
          </el-form>

          <el-divider content-position="left">生成结果</el-divider>

          <div class="generated-prompt">
            <el-input
              type="textarea"
              v-model="generatedPrompt"
              :rows="10"
              readonly
            />
            <div class="prompt-actions" v-if="generatedPrompt">
              <el-button
                type="primary"
                size="small"
                @click="copyToClipboard"
              >复制到剪贴板</el-button>
              <el-button
                type="success"
                size="small"
                @click="saveAsTemplate"
              >另存为新模板</el-button>
            </div>
          </div>
        </div>

        <div v-else class="no-template-selected">
          <el-empty description="请选择一个提示词模板" />
        </div>
      </div>
    </div>

    <!-- 创建/编辑模板对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建提示词模板' : '编辑提示词模板'"
      v-model="dialogVisible"
      width="70%"
    >
      <el-form :model="templateForm" label-width="100px">
        <el-form-item label="名称">
          <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="角色类型">
          <el-select v-model="templateForm.role" placeholder="请选择角色类型">
            <el-option label="通用" value="general" />
            <el-option label="设定师" value="world_builder" />
            <el-option label="导演" value="story_director" />
            <el-option label="人物塑造师" value="character_developer" />
            <el-option label="游戏设计师" value="system_designer" />
            <el-option label="写手" value="writer" />
            <el-option label="编辑" value="editor" />
            <el-option label="读者" value="reader" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            type="textarea"
            v-model="templateForm.description"
            placeholder="请输入模板描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="模板内容">
          <el-input
            type="textarea"
            v-model="templateForm.content"
            placeholder="请输入模板内容，使用 {{parameter}} 格式表示参数"
            :rows="10"
          />
          <el-button type="primary" size="small" style="margin-top: 8px;" @click="extractParametersFromContent">自动生成变量</el-button>
          <el-collapse v-model="templateHelpCollapse" class="template-help-collapse" style="margin-top: 10px;">
            <el-collapse-item name="help">
              <template #title>
                <span>模板语法帮助（点击展开/收起）</span>
              </template>
              <div class="template-help">
                <p>模板语法帮助：</p>
                <ul>
                  <li>使用 <code>&#123;&#123;paramName&#125;&#125;</code> 表示参数</li>
                  <li>例如：<code>写一个关于&#123;&#123;theme&#125;&#125;的故事，主角是&#123;&#123;protagonist&#125;&#125;，风格是&#123;&#123;style&#125;&#125;</code></li>
                </ul>

                <p>系统参数使用说明：</p>
                <ul>
                  <li>系统参数类型：
                    <ul>
                      <li><code>worldSetting</code> - 世界观设定</li>
                      <li><code>character</code> - 角色管理</li>
                      <li><code>outline</code> - 大纲管理</li>
                      <li><code>chapter</code> - 章节管理</li>
                      <li><code>editorComment</code> - 章节编辑意见</li>
                      <li><code>volume</code> - 分卷管理</li>
                      <li><code>chapterGroup</code> - 章节分组管理</li>
                      <li><code>clue</code> - 线索管理</li>
                    </ul>
                  </li>
                  <li>参数配置说明：
                    <ul>
                      <li><strong>必填项设置</strong>：参数可以设置为必填或非必填。非必填参数在未提供值时会被替换为空字符串。</li>
                      <li><strong>系统参数多选</strong>：系统参数可以设置为多选模式，允许用户选择多个项目，生成时会自动处理多个选项。</li>
                    </ul>
                  </li>
                  <li>访问系统参数属性：
                    <ul>
                      <li>直接访问对象：<code>&#123;&#123;paramName&#125;&#125;</code> - 返回完整JSON对象</li>
                      <li>访问对象属性：<code>&#123;&#123;paramName.property&#125;&#125;</code> - 返回属性值</li>
                      <li>访问嵌套属性：<code>&#123;&#123;paramName.property.nestedProperty&#125;&#125;</code> - 返回嵌套属性值</li>
                      <li>访问章节编辑意见：<code>&#123;&#123;chapter.editorComments&#125;&#125;</code> - 返回所有编辑意见</li>
                      <li>访问特定编辑意见：<code>&#123;&#123;chapter.editorComments[0].content&#125;&#125;</code> - 返回第一条编辑意见内容</li>
                      <li>多选参数：使用数组索引访问，如 <code>&#123;&#123;paramName[0].property&#125;&#125;</code> 访问第一个选项的属性</li>
                    </ul>
                  </li>
                  <li>常用属性示例：
                    <ul>
                      <li>世界观：<code>&#123;&#123;worldSetting.title&#125;&#125;</code>, <code>&#123;&#123;worldSetting.content&#125;&#125;</code></li>
                      <li>角色：<code>&#123;&#123;character.name&#125;&#125;</code>, <code>&#123;&#123;character.description&#125;&#125;</code>, <code>&#123;&#123;character.personality&#125;&#125;</code>, <code>&#123;&#123;character.abilities&#125;&#125;</code></li>
                      <li>大纲：<code>&#123;&#123;outline.title&#125;&#125;</code>, <code>&#123;&#123;outline.content&#125;&#125;</code></li>
                      <li>章节：<code>&#123;&#123;chapter.title&#125;&#125;</code>, <code>&#123;&#123;chapter.content&#125;&#125;</code></li>
                      <li>编辑意见：<code>&#123;&#123;editorComment.content&#125;&#125;</code>, <code>&#123;&#123;editorComment.editorName&#125;&#125;</code>, <code>&#123;&#123;editorComment.createdAt&#125;&#125;</code></li>
                      <li>分卷：<code>&#123;&#123;volume.title&#125;&#125;</code>, <code>&#123;&#123;volume.summary&#125;&#125;</code>, <code>&#123;&#123;volume.wordCount&#125;&#125;</code></li>
                      <li>章节分组：<code>&#123;&#123;chapterGroup.title&#125;&#125;</code>, <code>&#123;&#123;chapterGroup.summary&#125;&#125;</code>, <code>&#123;&#123;chapterGroup.chapterRange&#125;&#125;</code>, <code>&#123;&#123;chapterGroup.progressRate&#125;&#125;</code></li>
                    </ul>
                  </li>
                </ul>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>

        <el-divider content-position="left">参数设置</el-divider>

        <div v-for="(param, index) in templateForm.parameters" :key="index" class="parameter-item">
          <el-row :gutter="10">
            <el-col :span="5">
              <el-form-item :label="'参数 ' + (index + 1)" label-width="80px">
                <el-input v-model="param.name" placeholder="参数名称" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="键名" label-width="50px">
                <el-input v-model="param.key" placeholder="参数键名" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="类型" label-width="50px">
                <el-select v-model="param.type">
                  <el-option label="文本" value="text" />
                  <el-option label="数字" value="number" />
                  <el-option label="选择" value="select" />
                  <el-option label="系统参数" value="system" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="必填" label-width="50px">
                <el-switch v-model="param.required" :active-value="true" :inactive-value="false" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="描述" label-width="50px">
                <el-input v-model="param.description" placeholder="参数描述" />
              </el-form-item>
            </el-col>
            <el-col :span="2" class="parameter-actions">
              <el-button
                type="danger"
                circle
                size="small"
                @click="removeParameter(index)"
                title="删除参数"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-col>
          </el-row>

          <!-- 数字类型的额外属性 -->
          <el-row v-if="param.type === 'number'" :gutter="10">
            <el-col :span="5" :offset="5">
              <el-form-item label="最小值" label-width="50px">
                <el-input-number v-model="param.min" :precision="0" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="最大值" label-width="50px">
                <el-input-number v-model="param.max" :precision="0" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 选择类型的额外属性 -->
          <template v-if="param.type === 'select'">
            <el-row v-for="(option, optIndex) in param.options || []" :key="optIndex" :gutter="10">
              <el-col :span="5" :offset="5">
                <el-form-item :label="'选项 ' + (optIndex + 1)" label-width="50px">
                  <el-input v-model="option.label" placeholder="选项标签" />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="值" label-width="50px">
                  <el-input v-model="option.value" placeholder="选项值" />
                </el-form-item>
              </el-col>
              <el-col :span="2" class="parameter-actions">
                <el-button
                  type="danger"
                  circle
                  size="small"
                  @click="removeOption(param, optIndex)"
                  title="删除选项"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="5" :offset="5">
                <el-button
                  type="primary"
                  size="small"
                  @click="addOption(param)"
                >添加选项</el-button>
              </el-col>
            </el-row>
          </template>

          <!-- 系统参数类型的额外属性 -->
          <template v-if="param.type === 'system'">
            <el-row :gutter="10">
              <el-col :span="10" :offset="5">
                <el-form-item label="系统参数类型" label-width="100px">
                  <el-select v-model="param.systemType" placeholder="选择系统参数类型">
                    <el-option label="世界观设定" value="worldSetting" />
                    <el-option label="角色管理" value="character" />
                    <el-option label="大纲管理" value="outline" />
                    <el-option label="章节管理" value="chapter" />
                    <el-option label="章节编辑意见" value="editorComment" />
                    <el-option label="分卷管理" value="volume" />
                    <el-option label="章节分组管理" value="chapterGroup" />
                    <el-option label="线索管理" value="clue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="多选" label-width="50px">
                  <el-switch v-model="param.multiple" />
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-divider v-if="index < templateForm.parameters.length - 1" />
        </div>

        <el-form-item>
          <el-button type="primary" @click="addParameter">添加参数</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
    </div>
  </PageLoading>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Edit, Delete, InfoFilled, Menu, Setting, VideoCamera,
  User, Cpu, EditPen, Document, View, Star, ArrowDown
} from '@element-plus/icons-vue'
import { promptTemplateAPI, characterAPI, worldSettingAPI, outlineAPI, chapterAPI, volumeAPI, chapterGroupAPI } from '@/services/api'
import api from '@/services/api'
import { useRouter } from 'vue-router'
import PageLoading from '@/components/PageLoading.vue'
import { usePageLoading, useOperationLoading } from '@/composables/useLoading'

export default {
  name: 'PromptGenerator',
  components: {
    Edit, Delete, InfoFilled, Menu, Setting, VideoCamera,
    User, Cpu, EditPen, Document, View, Star, ArrowDown
  },
  setup() {
    const router = useRouter()

    // 返回上一页方法
    const goBack = () => {
      router.go(-1)
    }

    // 模板列表数据
    const templates = ref([])
    const searchQuery = ref('')
    const activeTemplateId = ref('')
    const selectedTemplate = ref(null)
    const activeRole = ref('all') // 当前选中的角色分类
    const categoryExpanded = ref(false) // 分类展开状态

    // 角色分类配置
    const roleCategories = ref([
      { value: 'all', label: '全部', icon: 'Menu' },
      { value: 'world_builder', label: '设定师', icon: 'Setting' },
      { value: 'story_director', label: '导演', icon: 'VideoCamera' },
      { value: 'character_developer', label: '人物塑造师', icon: 'User' },
      { value: 'system_designer', label: '游戏设计师', icon: 'Cpu' },
      { value: 'writer', label: '写手', icon: 'EditPen' },
      { value: 'editor', label: '编辑', icon: 'Document' },
      { value: 'reader', label: '读者', icon: 'View' },
      { value: 'general', label: '通用', icon: 'Star' }
    ])

    // 对话框相关数据
    const dialogVisible = ref(false)
    const dialogType = ref('create')
    const templateForm = ref({
      name: '',
      description: '',
      content: '',
      role: 'general', // 添加角色类型字段，默认为'general'
      parameters: []
    })

    // 控制帮助折叠，默认折叠
    const templateHelpCollapse = ref([])

    // 项目相关数据
    const projects = ref([])
    const characters = ref([])
    const worldSettings = ref([])
    const outlines = ref([])
    const chapters = ref([])
    const volumes = ref([])
    const chapterGroups = ref({})
    const editorComments = ref({})
    const clues = ref([])

    // 生成器相关数据
    const generatorForm = ref({
      projectId: null,
      characterIds: [],
      worldSettingIds: [],
      outlineIds: [],
      chapterId: null,
      params: {}
    })
    const generating = ref(false)
    const generatedPrompt = ref('')

    // 自动生成变量按钮逻辑
    const SYSTEM_PARAM_MAP = {
      worldSetting: 'worldSetting',
      character: 'character',
      outline: 'outline',
      chapter: 'chapter',
      editorComment: 'editorComment',
      volume: 'volume',
      chapterGroup: 'chapterGroup',
      clue: 'clue'
    };
    // 支持的系统参数key
    const SYSTEM_PARAM_KEYS = Object.keys(SYSTEM_PARAM_MAP);

    // 变量名与系统类型的映射（可根据实际需要扩展）
    const SYSTEM_PARAM_KEYWORD_MAP = {
      worldSetting: ['worldSetting', 'world', 'setting', '设定', '世界观'],
      character: ['character', '主角', '角色'],
      outline: ['outline', '大纲'],
      chapter: ['chapter', '章节'],
      editorComment: ['editorComment', '编辑意见', 'comment'],
      volume: ['volume', '分卷'],
      chapterGroup: ['chapterGroup', '章节分组', 'group'],
      clue: ['clue', '线索', 'clues']
    };

    // 判断变量名是否为系统参数
    function getSystemTypeByKey(key) {
      for (const [type, keywords] of Object.entries(SYSTEM_PARAM_KEYWORD_MAP)) {
        if (keywords.some(kw => key.toLowerCase().includes(kw.toLowerCase()))) {
          return type;
        }
      }
      return null;
    }

    // 判断变量名是否已存在且有multiple属性
    function paramExistsName(key) {
      const old = templateForm.value.parameters.find(p => p.key === key);
      return old ? old.name : key;
    }

    // 判断变量名是否已存在且有multiple属性
    function paramExistsMultiple(key) {
      const old = templateForm.value.parameters.find(p => p.key === key);
      return old && typeof old.multiple === 'boolean' ? old.multiple : false;
    }

    // 判断变量名是否已存在且有required属性
    function paramExistsRequired(key) {
      const old = templateForm.value.parameters.find(p => p.key === key);
      return old && typeof old.required === 'boolean' ? old.required : false;
    }

    // 自动提取模板内容中的变量
    const extractParametersFromContent = () => {
      const content = templateForm.value.content || '';
      // 匹配 {{xxx}}，支持 {{xxx.yyy}}
      const regex = /\{\{\s*([a-zA-Z0-9_]+)(?:\.[^\}]*)?\s*\}\}/g;
      const found = new Set();
      let match;
      const params = [];
      while ((match = regex.exec(content)) !== null) {
        const key = match[1];
        if (!found.has(key)) {
          found.add(key);
          // 判断是否为系统参数
          const systemType = getSystemTypeByKey(key);
          if (systemType) {
            params.push({
              name: paramExistsName(key),
              key: key,
              type: 'system',
              systemType: systemType,
              required: paramExistsRequired(key),
              multiple: paramExistsMultiple(key)
            });
          } else {
            params.push({
              name: paramExistsName(key),
              key: key,
              type: 'text',
              required: paramExistsRequired(key)
            });
          }
        }
      }
      templateForm.value.parameters = params;
      ElMessage.success('已自动提取模板变量！');
    };

    // 过滤后的模板列表
    const filteredTemplates = computed(() => {
      // 首先按角色过滤
      let filtered = templates.value
      if (activeRole.value !== 'all') {
        filtered = filtered.filter(template => template.role === activeRole.value)
      }

      // 然后按搜索关键词过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(template =>
          template.name.toLowerCase().includes(query) ||
          (template.description && template.description.toLowerCase().includes(query))
        )
      }

      return filtered
    })

    // 获取模板列表
    const fetchTemplates = async () => {
      try {
        const response = await promptTemplateAPI.getAllTemplates()
        // 由于axios拦截器已经处理了response.data，所以response就是数据本身
        templates.value = response
        console.log('获取到的模板列表:', templates.value)

        // 检查模板数据结构
        if (templates.value && templates.value.length > 0) {
          console.log('第一个模板的数据结构:', JSON.stringify(templates.value[0], null, 2))
          console.log('模板角色类型统计:', templates.value.reduce((acc, template) => {
            acc[template.role] = (acc[template.role] || 0) + 1
            return acc
          }, {}))
        } else {
          console.log('模板列表为空或无效')
        }
      } catch (error) {
        ElMessage.error('获取提示词模板列表失败')
        console.error(error)
      }
    }

    // 选择模板
    const handleTemplateSelect = async (id) => {
      activeTemplateId.value = id
      try {
        const response = await promptTemplateAPI.getTemplate(id)
        // 将后端的template字段映射为前端的content字段
        // 由于axios拦截器已经处理了response.data，所以response就是数据本身
        const templateData = response;
        selectedTemplate.value = {
          ...templateData,
          content: templateData.template // 将template映射为content
        };
        console.log('选中的模板详情:', selectedTemplate.value)

        // 初始化参数表单
        generatorForm.value.params = {}

        // 处理参数配置
        // 先确保参数是数组
        if (!Array.isArray(selectedTemplate.value.parameters)) {
          try {
            if (typeof selectedTemplate.value.parameters === 'string') {
              selectedTemplate.value.parameters = JSON.parse(selectedTemplate.value.parameters);
            }
          } catch (e) {
            console.error('解析参数失败:', e);
            selectedTemplate.value.parameters = [];
          }

          // 如果解析后仍然不是数组，则初始化为空数组
          if (!Array.isArray(selectedTemplate.value.parameters)) {
            console.log('参数不是数组，初始化为空数组');
            selectedTemplate.value.parameters = [];
          }
        }

        // 过滤无效参数
        selectedTemplate.value.parameters = selectedTemplate.value.parameters.filter(param =>
          param && typeof param === 'object' && param.name && param.key
        );

        console.log('处理后的模板参数配置:', selectedTemplate.value.parameters);

        // 初始化参数表单
        selectedTemplate.value.parameters.forEach(param => {
          if (param.key) {
            generatorForm.value.params[param.key] = param.defaultValue || '';
          }
        });
      } catch (error) {
        ElMessage.error('获取模板详情失败')
        console.error(error)
      }
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      dialogType.value = 'create'
      templateForm.value = {
        name: '',
        description: '',
        content: '',
        role: 'general',
        parameters: []
      }
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (template) => {
      dialogType.value = 'edit'
      // 深拷贝并确保字段映射正确
      const templateCopy = JSON.parse(JSON.stringify(template));

      // 如果模板有template字段但没有content字段，则映射为content
      if (templateCopy.template && !templateCopy.content) {
        templateCopy.content = templateCopy.template;
      }

      // 确保有role字段
      if (!templateCopy.role) {
        templateCopy.role = 'general';
      }

      // 确保parameters是数组并且正确处理
      if (!Array.isArray(templateCopy.parameters)) {
        // 如果parameters不是数组，尝试解析它
        try {
          if (typeof templateCopy.parameters === 'string') {
            templateCopy.parameters = JSON.parse(templateCopy.parameters);
          }
        } catch (e) {
          console.error('解析parameters失败:', e);
          templateCopy.parameters = [];
        }

        // 如果解析后仍然不是数组，则初始化为空数组
        if (!Array.isArray(templateCopy.parameters)) {
          templateCopy.parameters = [];
        }
      }

      templateForm.value = templateCopy;
      dialogVisible.value = true
    }

    // 添加参数
    const addParameter = () => {
      templateForm.value.parameters.push({
        name: '',
        key: '',
        type: 'text',
        description: '',
        defaultValue: '',
        systemType: '',
        required: true,  // 默认为必填
        multiple: false  // 默认不是多选
      })
    }

    // 移除参数
    const removeParameter = (index) => {
      templateForm.value.parameters.splice(index, 1)
    }

    // 添加选项
    const addOption = (param) => {
      if (!param.options) {
        param.options = []
      }
      param.options.push({
        label: '',
        value: ''
      })
    }

    // 移除选项
    const removeOption = (param, index) => {
      param.options.splice(index, 1)
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        // 验证参数配置
        const parameters = templateForm.value.parameters || [];
        for (let i = 0; i < parameters.length; i++) {
          const param = parameters[i];
          if (!param.name || !param.key) {
            ElMessage.warning(`参数 ${i + 1} 的名称和键名不能为空`);
            return;
          }

          // 如果是选择类型，验证选项
          if (param.type === 'select' && (!param.options || param.options.length === 0)) {
            ElMessage.warning(`参数 ${param.name} 是选择类型，必须添加选项`);
            return;
          }

          // 如果是系统参数类型，验证系统参数类型
          if (param.type === 'system' && !param.systemType) {
            ElMessage.warning(`参数 ${param.name} 是系统参数类型，必须选择系统参数类型`);
            return;
          }
        }

        // 创建一个新对象，将content映射为template
        // 确保参数数组是有效的
        let cleanParameters = [];
        if (Array.isArray(templateForm.value.parameters)) {
          // 深拷贝参数数组，并去除任何无效或多余的属性
          cleanParameters = templateForm.value.parameters.map(param => {
            const cleanParam = {
              name: param.name || '',
              key: param.key || '',
              type: param.type || 'text',
              description: param.description || '',
              defaultValue: param.defaultValue || '',
              required: param.required !== false // 默认为必填
            };

            // 根据参数类型添加特定属性
            if (param.type === 'number') {
              cleanParam.min = param.min !== undefined ? param.min : 0;
              cleanParam.max = param.max !== undefined ? param.max : 100;
            } else if (param.type === 'select' && Array.isArray(param.options)) {
              cleanParam.options = param.options.filter(opt => opt.label && opt.value);
            } else if (param.type === 'system') {
              cleanParam.systemType = param.systemType || '';
              cleanParam.multiple = !!param.multiple;
            }

            return cleanParam;
          });
        }

        const formData = {
          name: templateForm.value.name,
          role: templateForm.value.role,
          template: templateForm.value.content, // 将content映射为template
          description: templateForm.value.description,
          parameters: cleanParameters
        };

        if (templateForm.value.id) {
          formData.id = templateForm.value.id;
        }

        console.log('提交的模板数据:', formData);

        if (dialogType.value === 'create') {
          await promptTemplateAPI.createTemplate(formData)
          ElMessage.success('创建提示词模板成功')
        } else {
          await promptTemplateAPI.updateTemplate(formData.id, formData)
          ElMessage.success('更新提示词模板成功')
        }
        dialogVisible.value = false
        fetchTemplates()
      } catch (error) {
        ElMessage.error(dialogType.value === 'create' ? '创建提示词模板失败' : '更新提示词模板失败')
        console.error(error)
      }
    }

    // 删除模板
    const handleDelete = async (template) => {
      try {
        await ElMessageBox.confirm('确定要删除这个提示词模板吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await promptTemplateAPI.deleteTemplate(template.id)
        ElMessage.success('删除提示词模板成功')

        if (selectedTemplate.value && selectedTemplate.value.id === template.id) {
          selectedTemplate.value = null
          activeTemplateId.value = ''
        }

        fetchTemplates()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除提示词模板失败')
          console.error(error)
        }
      }
    }

    // 生成提示词
    const generatePrompt = async () => {
      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择一个模板')
        return
      }

      // 验证必填参数
      if (selectedTemplate.value.parameters && selectedTemplate.value.parameters.length > 0) {
        for (const param of selectedTemplate.value.parameters) {
          if (!param.key) continue; // 跳过没有key的参数
          if (param.required === false) continue; // 跳过非必填参数

          const paramValue = generatorForm.value.params[param.key];
          if (paramValue === undefined || paramValue === null || paramValue === '') {
            ElMessage.warning(`请填写必填参数: ${param.name}`)
            return
          }
        }
      }

      generating.value = true
      generatedPrompt.value = ''

      try {
        // 构建请求数据
        const requestData = {
          params: generatorForm.value.params,
          projectId: generatorForm.value.projectId,
          chapterId: generatorForm.value.chapterId
        }

        // 检查系统参数类型，确保正确传递
        if (selectedTemplate.value.parameters) {
          // 记录系统参数类型
          const systemParamTypes = {};

          for (const param of selectedTemplate.value.parameters) {
            if (param.type === 'system' && param.systemType && param.key && generatorForm.value.params[param.key]) {
              systemParamTypes[param.key] = param.systemType;
              console.log(`检测到系统参数: ${param.key}, 类型: ${param.systemType}, 值: ${generatorForm.value.params[param.key]}`);
            }
          }

          // 添加系统参数类型信息到请求中
          requestData.systemParamTypes = systemParamTypes;
        }

        // 打印调试信息
        console.log('发送的参数:', {
          id: selectedTemplate.value.id,
          ...requestData
        })

        const response = await promptTemplateAPI.generatePrompt(
          selectedTemplate.value.id,
          requestData
        )

        // 由于axios拦截器已经处理了response.data，所以response就是数据本身
        generatedPrompt.value = response.prompt
        console.log('生成的提示词:', response)
        ElMessage.success('提示词生成成功')
      } catch (error) {
        ElMessage.error('生成提示词失败')
        console.error(error)
      } finally {
        generating.value = false
      }
    }

    // 复制到剪贴板
    const copyToClipboard = () => {
      navigator.clipboard.writeText(generatedPrompt.value)
        .then(() => {
          ElMessage.success('已复制到剪贴板')
        })
        .catch(() => {
          ElMessage.error('复制失败')
        })
    }

    // 另存为新模板
    const saveAsTemplate = () => {
      dialogType.value = 'create'
      templateForm.value = {
        name: selectedTemplate.value.name + ' (副本)',
        description: selectedTemplate.value.description,
        content: generatedPrompt.value,
        role: selectedTemplate.value.role || 'general',
        parameters: JSON.parse(JSON.stringify(selectedTemplate.value.parameters || []))
      }
      dialogVisible.value = true
    }

    // 处理角色切换
    const handleRoleChange = (roleValue = null) => {
      if (roleValue !== null) {
        activeRole.value = roleValue
      }
      // 切换角色时重置选中的模板
      selectedTemplate.value = null
      activeTemplateId.value = ''
      console.log('切换到角色:', activeRole.value)

      // 计算当前角色的模板数量
      const filteredCount = templates.value.filter(template =>
        activeRole.value === 'all' || template.role === activeRole.value
      ).length
      console.log('当前角色的模板数量:', filteredCount)
    }

    // 获取分类模板数量
    const getCategoryCount = (roleValue) => {
      if (roleValue === 'all') {
        return templates.value.length
      }
      return templates.value.filter(template => template.role === roleValue).length
    }

    // 获取角色名称
    const getRoleName = (roleValue) => {
      const category = roleCategories.value.find(cat => cat.value === roleValue)
      return category ? category.label : roleValue
    }

    // 获取角色标签类型
    const getRoleTagType = (roleValue) => {
      const typeMap = {
        'world_builder': 'success',
        'story_director': 'warning',
        'character_developer': 'info',
        'system_designer': 'danger',
        'writer': 'primary',
        'editor': 'success',
        'reader': 'info',
        'general': ''
      }
      return typeMap[roleValue] || ''
    }

    // 切换分类展开状态
    const toggleCategoryExpanded = () => {
      categoryExpanded.value = !categoryExpanded.value
    }

    // 获取项目列表
    const fetchProjects = async () => {
      try {
        const response = await api.get('/projects')
        projects.value = response
        console.log('获取到的项目列表:', projects.value)
      } catch (error) {
        ElMessage.error('获取项目列表失败')
        console.error(error)
      }
    }

    // 处理项目选择变化
    const handleProjectChange = async (projectId) => {
      // 重置相关数据
      generatorForm.value.characterIds = []
      generatorForm.value.worldSettingIds = []
      generatorForm.value.outlineIds = []
      generatorForm.value.chapterId = null

      characters.value = []
      worldSettings.value = []
      outlines.value = []
      chapters.value = []
      volumes.value = []
      chapterGroups.value = {}

      if (!projectId) return

      try {
        // 获取项目相关数据
        const [charactersRes, worldSettingsRes, outlinesRes, chaptersRes, volumesRes, cluesRes] = await Promise.all([
          characterAPI.getAllCharacters(projectId),
          worldSettingAPI.getAllWorldSettings(projectId),
          outlineAPI.getAllOutlines(projectId),
          chapterAPI.getAllChapters(projectId),
          volumeAPI.getAllVolumes(projectId),
          api.get(`/projects/${projectId}/clues`)
        ])

        characters.value = charactersRes
        worldSettings.value = worldSettingsRes
        outlines.value = outlinesRes
        chapters.value = chaptersRes
        volumes.value = volumesRes
        clues.value = cluesRes

        console.log('项目相关数据加载完成')
      } catch (error) {
        ElMessage.error('获取项目相关数据失败')
        console.error(error)
      }
    }

    // 处理分卷选择变化
    const handleVolumeChange = async (paramKey) => {
      const volumeId = generatorForm.value.params[paramKey]
      if (!volumeId) return

      try {
        // 获取分卷相关的章节分组数据
        const chapterGroupsRes = await chapterGroupAPI.getAllChapterGroups(volumeId)
        chapterGroups.value[paramKey] = chapterGroupsRes

        // 查找与该分卷关联的章节分组参数，并更新其数据
        if (selectedTemplate.value && selectedTemplate.value.parameters) {
          for (const param of selectedTemplate.value.parameters) {
            if (param.type === 'system' && param.systemType === 'chapterGroup') {
              // 将章节分组数据也关联到章节分组参数
              chapterGroups.value[param.key] = chapterGroupsRes
            }
          }
        }
      } catch (error) {
        ElMessage.error('获取章节分组数据失败')
        console.error(error)
      }
    }

    // 处理章节分组点击事件
    const handleChapterGroupClick = async (param) => {
      // 如果已经有数据，不需要重新获取
      if (chapterGroups.value[param.key] && chapterGroups.value[param.key].length > 0) {
        return
      }

      // 查找与该章节分组关联的分卷参数
      let volumeParamKey = null
      let volumeId = null

      if (selectedTemplate.value && selectedTemplate.value.parameters) {
        for (const p of selectedTemplate.value.parameters) {
          if (p.type === 'system' && p.systemType === 'volume') {
            volumeParamKey = p.key
            volumeId = generatorForm.value.params[p.key]
            if (volumeId) break
          }
        }
      }

      // 如果找到了分卷ID，获取章节分组数据
      if (volumeId) {
        try {
          const chapterGroupsRes = await chapterGroupAPI.getAllChapterGroups(volumeId)
          chapterGroups.value[param.key] = chapterGroupsRes
        } catch (error) {
          ElMessage.error('获取章节分组数据失败')
          console.error(error)
        }
      } else {
        ElMessage.warning('请先选择分卷')
      }
    }

    // 处理编辑意见点击事件
    const handleEditorCommentClick = async (param) => {
      // 如果已经有数据，不需要重新获取
      if (editorComments.value[param.key] && editorComments.value[param.key].length > 0) {
        return
      }

      // 查找是否有选中的章节
      let chapterId = generatorForm.value.chapterId

      // 如果没有直接选择章节，查找是否有章节系统参数
      if (!chapterId && selectedTemplate.value && selectedTemplate.value.parameters) {
        for (const p of selectedTemplate.value.parameters) {
          if (p.type === 'system' && p.systemType === 'chapter') {
            chapterId = generatorForm.value.params[p.key]
            if (chapterId) break
          }
        }
      }

      // 如果找到了章节ID，获取编辑意见数据
      if (chapterId) {
        try {
          const editorCommentsRes = await chapterAPI.getChapterEditorComments(chapterId)
          editorComments.value[param.key] = editorCommentsRes
        } catch (error) {
          ElMessage.error('获取章节编辑意见数据失败')
          console.error(error)
        }
      } else {
        ElMessage.warning('请先选择章节')
      }
    }

    // 组件挂载时获取模板列表和项目列表
    onMounted(() => {
      fetchTemplates()
      fetchProjects()
    })

    return {
      templates,
      filteredTemplates,
      searchQuery,
      activeTemplateId,
      selectedTemplate,
      activeRole,
      categoryExpanded,
      roleCategories,
      dialogVisible,
      dialogType,
      templateForm,
      generatorForm,
      generating,
      generatedPrompt,
      // 项目相关数据
      projects,
      characters,
      worldSettings,
      outlines,
      chapters,
      volumes,
      chapterGroups,
      editorComments,
      clues,
      // 控制帮助折叠
      templateHelpCollapse,
      // 方法
      handleTemplateSelect,
      showCreateDialog,
      showEditDialog,
      addParameter,
      removeParameter,
      addOption,
      removeOption,
      handleSubmit,
      handleDelete,
      generatePrompt,
      copyToClipboard,
      saveAsTemplate,
      handleRoleChange,
      handleProjectChange,
      handleVolumeChange,
      handleChapterGroupClick,
      handleEditorCommentClick,
      extractParametersFromContent,
      getCategoryCount,
      getRoleName,
      getRoleTagType,
      toggleCategoryExpanded,
      goBack
    }
  }
}
</script>

<style scoped>
.prompt-generator-container {
  padding: 24px;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  background: white;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #f5f7fa;
  border-color: #409eff;
  color: #409eff;
}

.page-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #909399;
}

.create-btn {
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
}

/* 内容布局 */
.content-layout {
  display: flex;
  flex: 1;
  gap: 24px;
  overflow: hidden;
}

/* 左侧模板面板 */
.templates-panel {
  width: 380px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.template-count {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 4px 8px;
  border-radius: 12px;
}

.search-input {
  margin-bottom: 16px;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 角色分类样式 */
.role-categories {
  margin-bottom: 20px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 12px;
}

.category-header:hover {
  background: #f0f2f5;
  border-color: #409eff;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.active-filter-tag {
  font-size: 11px;
}

.category-toggle {
  display: flex;
  align-items: center;
}

.toggle-icon {
  transition: transform 0.3s ease;
  color: #909399;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.category-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.category-card {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.category-card:hover {
  border-color: #409eff;
  background: #f0f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.category-card.active {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.category-icon {
  margin-right: 8px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
}

.category-count {
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
}

/* 模板列表样式 */
.templates-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.template-scrollbar {
  flex: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.template-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-1px);
}

.template-item.active {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3);
}

.template-content {
  margin-bottom: 12px;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-name {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.role-tag {
  margin-left: 8px;
}

.template-description {
  margin: 0;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-item:hover .template-actions {
  opacity: 1;
}

.action-btn {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background: #409eff;
  color: white;
}

.delete-btn:hover {
  background: #f56c6c;
  color: white;
}

/* 右侧生成面板 */
.generator-panel {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

/* 生成面板内容样式 */
.template-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.template-description {
  color: #606266;
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.6;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.generated-prompt {
  margin-top: 24px;
}

.generated-prompt :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.prompt-actions {
  margin-top: 12px;
  display: flex;
  gap: 12px;
}

.prompt-actions .el-button {
  border-radius: 6px;
  font-weight: 500;
}

.no-template-selected {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
}

.el-form-item :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.el-form-item :deep(.el-input__wrapper),
.el-form-item :deep(.el-select .el-input__wrapper),
.el-form-item :deep(.el-textarea__inner) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.el-form-item :deep(.el-input__wrapper:hover),
.el-form-item :deep(.el-select .el-input__wrapper:hover),
.el-form-item :deep(.el-textarea__inner:hover) {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

/* 弹窗样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.parameter-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.parameter-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.parameter-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-help {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 8px;
  font-size: 13px;
  border: 1px solid #e4e7ed;
}

.template-help code {
  background: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.template-help-collapse {
  margin-top: 16px;
}

.template-help-collapse :deep(.el-collapse-item__header) {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0 12px;
  font-weight: 500;
}

.form-item-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #409eff;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.form-item-tip i {
  margin-right: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: #fafbfc;
  border-top: 1px solid #e4e7ed;
}

.dialog-footer .el-button {
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .templates-panel {
    width: 320px;
  }

  .category-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
  }

  .templates-panel {
    width: 100%;
    margin-bottom: 20px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}
</style>
