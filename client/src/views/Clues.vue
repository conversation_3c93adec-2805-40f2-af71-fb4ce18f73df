<template>
  <PageLoading page-key="clues" @retry="fetchClues">
    <div class="clues-container">
    <div class="header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>线索管理</h2>
      </div>
      <el-button type="primary" @click="showCreateDialog">添加线索</el-button>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>线索列表</span>
            </div>
          </template>

          <el-table v-if="clues.length > 0" :data="clues" style="width: 100%">
            <el-table-column prop="name" label="线索名称" width="200" />
            <el-table-column prop="type" label="线索类型" width="120">
              <template #default="scope">
                <el-tag :type="getTypeTagType(scope.row.type)">{{ scope.row.type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="线索描述" show-overflow-tooltip />
            <el-table-column prop="firstAppearChapter.title" label="首次出现章节" width="200">
              <template #default="scope">
                <span v-if="scope.row.firstAppearChapter">{{ scope.row.firstAppearChapter.title }}</span>
                <span v-else class="text-gray">未设置</span>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" size="small" @click="editClue(scope.row)">编辑</el-button>
                <el-button type="danger" size="small" @click="confirmDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-empty v-else description="暂无线索，请添加新线索" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 创建/编辑线索对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑线索' : '添加线索'" width="50%">
      <el-form :model="currentClue" label-width="120px" :rules="rules" ref="clueForm">
        <el-form-item label="线索名称" prop="name">
          <el-input v-model="currentClue.name" placeholder="请输入线索名称" />
        </el-form-item>
        <el-form-item label="线索类型" prop="type">
          <el-select v-model="currentClue.type" placeholder="请选择线索类型" style="width: 100%">
            <el-option label="重大事件" value="重大事件" />
            <el-option label="重要道具" value="重要道具" />
            <el-option label="重要伏笔" value="重要伏笔" />
          </el-select>
        </el-form-item>
        <el-form-item label="线索描述" prop="description">
          <el-input 
            v-model="currentClue.description" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入线索描述" 
          />
        </el-form-item>
        <el-form-item label="首次出现章节">
          <el-select 
            v-model="currentClue.firstAppearChapterId" 
            placeholder="请选择首次出现的章节" 
            style="width: 100%"
            clearable
          >
            <el-option 
              v-for="chapter in chapters" 
              :key="chapter.id" 
              :label="chapter.title" 
              :value="chapter.id" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveClue">{{ isEdit ? '更新' : '创建' }}</el-button>
        </span>
      </template>
    </el-dialog>
    </div>
  </PageLoading>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import api from '../services/api';
import PageLoading from '../components/PageLoading.vue';
import { usePageLoading, useOperationLoading } from '../composables/useLoading';

const route = useRoute();
const router = useRouter();
const projectId = route.params.projectId;

// 加载状态管理
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('clues');
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading();

const clues = ref([]);
const chapters = ref([]);
const dialogVisible = ref(false);
const isEdit = ref(false);
const clueForm = ref(null);

const currentClue = ref({
  name: '',
  type: '',
  description: '',
  firstAppearChapterId: null
});

const rules = {
  name: [{ required: true, message: '请输入线索名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择线索类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入线索描述', trigger: 'blur' }]
};

// 获取线索列表
const fetchClues = async () => {
  try {
    const data = await api.get(`/projects/${projectId}/clues`);
    clues.value = data;
  } catch (error) {
    console.error('获取线索列表失败:', error);
    ElMessage.error('获取线索列表失败');
  }
};

// 获取章节列表
const fetchChapters = async () => {
  try {
    const data = await api.get(`/projects/${projectId}/chapters`);
    chapters.value = data;
  } catch (error) {
    console.error('获取章节列表失败:', error);
    ElMessage.error('获取章节列表失败');
  }
};

// 显示创建对话框
const showCreateDialog = () => {
  isEdit.value = false;
  currentClue.value = {
    name: '',
    type: '',
    description: '',
    firstAppearChapterId: null
  };
  dialogVisible.value = true;
};

// 编辑线索
const editClue = (clue) => {
  isEdit.value = true;
  currentClue.value = {
    id: clue.id,
    name: clue.name,
    type: clue.type,
    description: clue.description,
    firstAppearChapterId: clue.firstAppearChapterId
  };
  dialogVisible.value = true;
};

// 保存线索
const saveClue = () => {
  clueForm.value.validate(async (valid) => {
    if (valid) {
      try {
        const clueData = {
          projectId: parseInt(projectId),
          name: currentClue.value.name,
          type: currentClue.value.type,
          description: currentClue.value.description,
          firstAppearChapterId: currentClue.value.firstAppearChapterId
        };

        if (isEdit.value) {
          await api.put(`/clues/${currentClue.value.id}`, clueData);
          ElMessage.success('线索更新成功');
        } else {
          await api.post('/clues', clueData);
          ElMessage.success('线索创建成功');
        }

        dialogVisible.value = false;
        await fetchClues();
      } catch (error) {
        console.error('保存线索失败:', error);
        ElMessage.error('保存线索失败');
      }
    }
  });
};

// 确认删除
const confirmDelete = (clue) => {
  ElMessageBox.confirm(
    `确定要删除线索 "${clue.name}" 吗？此操作不可逆。`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await api.delete(`/clues/${clue.id}`);
      ElMessage.success('线索删除成功');
      await fetchClues();
    } catch (error) {
      console.error('删除线索失败:', error);
      ElMessage.error('删除线索失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 获取类型标签样式
const getTypeTagType = (type) => {
  switch (type) {
    case '重大事件':
      return 'danger';
    case '重要道具':
      return 'warning';
    case '重要伏笔':
      return 'success';
    default:
      return '';
  }
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleString();
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([fetchClues(), fetchChapters()]);
});
</script>

<style scoped>
.clues-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left h2 {
  margin: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-gray {
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>