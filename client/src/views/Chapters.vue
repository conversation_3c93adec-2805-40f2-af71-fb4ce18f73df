<template>
  <PageLoading page-key="chapters" @retry="fetchChapters">
    <div class="chapters-container">
    <div class="header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>章节管理</h2>
      </div>
      <el-button type="primary" @click="showCreateDialog">创建章节</el-button>
    </div>

    <!-- 章节列表 -->
    <el-table :data="chapters" style="width: 100%">
      <el-table-column prop="order" label="章节序号" width="100" />
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300">
        <template #default="scope">
          <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
<!--          <el-button-->
<!--            size="small"-->
<!--            type="success"-->
<!--            @click="showGenerateDialog(scope.row)"-->
<!--          >AI生成</el-button>-->
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <el-button
            size="small"
            type="info"
            @click="showManageDialog(scope.row)"
          >管理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑章节对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建章节' : '编辑章节'"
      v-model="dialogVisible"
      width="80%"
    >
      <el-form :model="chapterForm" label-width="100px">
        <el-form-item label="章节序号">
          <el-input-number v-model="chapterForm.chapterNumber" :min="1" />
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="chapterForm.title" placeholder="请输入章节标题" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="chapterForm.status" placeholder="请选择章节状态">
            <el-option label="计划中" value="planned" />
            <el-option label="草稿" value="draft" />
            <el-option label="已完成" value="completed" />
            <el-option label="修订中" value="revising" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容">
          <el-input
            type="textarea"
            v-model="chapterForm.content"
            placeholder="请输入章节内容"
            :rows="15"
          />
        </el-form-item>

        <!-- AI生成功能 -->
        <el-form-item label="AI生成">
          <div class="ai-generation-section">
            <el-button
              type="success"
              @click="showNewAIGenerationDialog"
              :disabled="!chapterForm.title"
            >
              <el-icon><Star /></el-icon>
              AI生成章节内容
            </el-button>
            <el-text type="info" size="small" style="margin-left: 10px;">
              需要先填写章节标题
            </el-text>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- AI生成内容对话框 -->
    <el-dialog
      title="AI生成章节内容"
      v-model="generateDialogVisible"
      width="80%"
    >
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="生成提示">
          <el-input
            type="textarea"
            v-model="generateForm.prompt"
            placeholder="请输入生成提示，例如：这是一个关于冒险的章节，主角遇到了一个神秘人物..."
            :rows="5"
          />
        </el-form-item>
        <el-form-item label="生成选项">
          <el-checkbox v-model="generateForm.useCharacters">引用角色信息</el-checkbox>
          <el-checkbox v-model="generateForm.useWorldSettings">引用世界观设定</el-checkbox>
          <el-checkbox v-model="generateForm.useOutlines">引用大纲信息</el-checkbox>
        </el-form-item>
      </el-form>
      <div v-if="generatingContent" class="generating-indicator">
        <el-progress type="circle" :percentage="generatingProgress" />
        <p>正在生成内容，请稍候...</p>
      </div>
      <div v-if="generatedContent" class="generated-content">
        <h3>生成结果</h3>
        <div class="content-preview">{{ generatedContent }}</div>
        <div class="action-buttons">
          <el-button type="primary" @click="applyGeneratedContent">应用到章节</el-button>
          <el-button @click="regenerateContent">重新生成</el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleGenerate"
            :disabled="generatingContent || !generateForm.prompt"
          >开始生成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 管理章节详情对话框 -->
    <el-dialog
      title="管理章节详情"
      v-model="manageChapterDialogVisible"
      width="85%"
      top="5vh"
      @close="resetManageDialog"
    >
      <div v-if="currentManagingChapter">
        <h3>正在管理: {{ currentManagingChapter.title }} (第 {{ currentManagingChapter.order }} 章)</h3>
        <el-tabs v-model="activeTabInManageDialog" class="manage-tabs">
          <el-tab-pane label="版本历史" name="versions">
            <div v-if="isLoadingVersions">正在加载版本历史...</div>
            <div v-else-if="chapterVersions.length === 0">暂无版本历史。</div>
            <div v-else class="version-management">
              <el-select v-model="selectedVersionId" placeholder="选择一个版本查看内容" @change="handleVersionSelect" style="margin-bottom: 15px;">
                <el-option
                  v-for="version in chapterVersions"
                  :key="version.id"
                  :label="`版本 ${version.versionNumber} - 创建于: ${new Date(version.createdAt).toLocaleString()}`"
                  :value="version.id">
                </el-option>
              </el-select>
              <div v-if="isLoadingVersionContent">正在加载版本内容...</div>
              <el-input
                v-if="selectedVersionContent !== null"
                type="textarea"
                :value="selectedVersionContent"
                :rows="15"
                readonly
                placeholder="版本内容"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="编辑意见" name="editorComments">
            <div v-if="isLoadingEditorComments">正在加载编辑意见...</div>
            <div v-else-if="editorComments.length === 0">暂无编辑意见。</div>
            <div v-else>
              <el-card v-for="comment in editorComments" :key="comment.id" style="margin-bottom: 10px;">
                <p>{{ comment.content }}</p>
                <small>评论人: {{ comment.editorName }} - {{ new Date(comment.createdAt).toLocaleString() }}</small>
              </el-card>
            </div>
            <!-- 添加编辑意见的表单 -->
            <el-form :model="newEditorCommentForm" @submit.prevent="handleAddEditorComment" style="margin-top: 20px;">
              <el-form-item label="新意见">
                <el-input type="textarea" v-model="newEditorCommentForm.content" placeholder="输入编辑意见" :rows="3"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleAddEditorComment">提交意见</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <el-tab-pane label="读者评价" name="readerReviews">
            <div v-if="isLoadingReaderReviews">正在加载读者评价...</div>
            <div v-else-if="readerReviews.length === 0">暂无读者评价。</div>
            <div v-else>
              <el-card v-for="review in readerReviews" :key="review.id" style="margin-bottom: 10px;">
                <p>{{ review.content }}</p>
                <small>评价人: {{ review.readerName }} - 评分: {{ review.rating }}/5 - {{ new Date(review.createdAt).toLocaleString() }}</small>
              </el-card>
            </div>
            <!-- 添加读者评价的表单 -->
            <el-form :model="newReaderReviewForm" @submit.prevent="handleAddReaderReview" style="margin-top: 20px;">
              <el-form-item label="新评价">
                <el-input type="textarea" v-model="newReaderReviewForm.content" placeholder="输入读者评价" :rows="3"></el-input>
              </el-form-item>
              <el-form-item label="评分">
                <el-rate v-model="newReaderReviewForm.rating"></el-rate>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleAddReaderReview">提交评价</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="manageChapterDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新的AI生成对话框 -->
    <AIGenerationDialog
      v-model:visible="newAIGenerationDialogVisible"
      content-type="章节"
      :project-id="projectId"
      :existing-content="chapterForm.content"
      :context-data="getChapterContextData()"
      role-filter="writer"
      @content-generated="handleNewAIContentGenerated"
    />
    </div>
  </PageLoading>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star } from '@element-plus/icons-vue'
import { chapterAPI } from '@/services/api'
import { useRoute, useRouter } from 'vue-router'
import AIGenerationDialog from '@/components/AIGenerationDialog.vue'
import PageLoading from '@/components/PageLoading.vue'
import { usePageLoading, useOperationLoading } from '@/composables/useLoading'

export default {
  name: 'Chapters',
  components: {
    AIGenerationDialog,
    PageLoading
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectId = route.params.projectId

    // 返回上一页方法
    const goBack = () => {
      router.go(-1)
    }

    // 章节列表数据
    const chapters = ref([])

    // 对话框相关数据
    const dialogVisible = ref(false)
    const dialogType = ref('create')
    const chapterForm = ref({
      chapterNumber: 1,
      title: '',
      content: '',
      status: 'planned',
      projectId: projectId
    })

    // AI生成相关数据
    const generateDialogVisible = ref(false)
    const generateForm = ref({
      prompt: '',
      useCharacters: true,
      useWorldSettings: true,
      useOutlines: true
    })
    const generatingContent = ref(false)
    const generatingProgress = ref(0)
    const generatedContent = ref('')
    const currentChapterId = ref(null)

    // 新的AI生成对话框
    const newAIGenerationDialogVisible = ref(false)

    // 加载状态管理
    const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('chapters')
    const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

    // 管理章节详情对话框相关数据
    const manageChapterDialogVisible = ref(false)
    const currentManagingChapter = ref(null)
    const chapterVersions = ref([])
    const selectedVersionId = ref(null)
    const selectedVersionContent = ref(null)
    const editorComments = ref([])
    const readerReviews = ref([])
    const activeTabInManageDialog = ref('versions') // 默认激活版本历史标签页
    const isLoadingVersions = ref(false)
    const isLoadingVersionContent = ref(false)
    const isLoadingEditorComments = ref(false)
    const isLoadingReaderReviews = ref(false)

    const newEditorCommentForm = ref({
      content: '',
      // editorName: '当前编辑' //  可从用户状态获取
    });

    const newReaderReviewForm = ref({
      content: '',
      rating: 0,
      // readerName: '当前读者' // 可从用户状态获取
    });

    // 获取章节列表
    const fetchChapters = async () => {
      try {
        const response = await chapterAPI.getAllChapters(projectId)
        // 检查response是否是数组或者包含data属性
        if (Array.isArray(response)) {
          chapters.value = response
        } else if (response && response.data) {
          chapters.value = response.data
        } else {
          console.error('获取章节列表返回的数据格式不正确:', response)
          ElMessage.error('获取章节列表返回的数据格式不正确')
          chapters.value = []
        }
      } catch (error) {
        ElMessage.error('获取章节列表失败')
        console.error(error)
        chapters.value = []
      }
    }

    // 获取状态类型
    const getStatusType = (status) => {
      const types = {
        planned: 'info',
        draft: 'warning',
        completed: 'success',
        revising: 'danger'
      }
      return types[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        planned: '计划中',
        draft: '草稿',
        completed: '已完成',
        revising: '修订中'
      }
      return texts[status] || '未知'
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      dialogType.value = 'create'
      // 安全地计算章节序号，防止 chapters.value 为 undefined 或 null
      const nextChapterNumber = Array.isArray(chapters.value) && chapters.value.length > 0
        ? chapters.value.length + 1
        : 1;

      chapterForm.value = {
        order: nextChapterNumber,
        chapterNumber: nextChapterNumber,
        title: '',
        content: '',
        status: 'planned',
        projectId: projectId
      }
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (chapter) => {
      dialogType.value = 'edit'
      chapterForm.value = { 
        ...chapter,
        chapterNumber: chapter.order // 确保章节序号正确显示在表单中
      }
      dialogVisible.value = true
    }

    // 显示生成对话框
    const showGenerateDialog = (chapter) => {
      currentChapterId.value = chapter.id
      generateForm.value = {
        prompt: `为章节"${chapter.title}"生成内容，这是第${chapter.order}章。`,
        useCharacters: true,
        useWorldSettings: true,
        useOutlines: true
      }
      generatedContent.value = ''
      generateDialogVisible.value = true
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        if (dialogType.value === 'create') {
          // 在创建时确保传递order字段
          const payload = {
            ...chapterForm.value,
            order: chapterForm.value.chapterNumber
          };
          
          await chapterAPI.createChapter(payload)
          ElMessage.success('创建章节成功')
        } else {
          // 在更新时确保传递order字段
          const payload = {
            ...chapterForm.value,
            order: chapterForm.value.chapterNumber
          };
          
          await chapterAPI.updateChapter(chapterForm.value.id, payload)
          // After successful update, create a new version
          try {
            await chapterAPI.createChapterVersion(chapterForm.value.id, { content: chapterForm.value.content })
            ElMessage.success('更新章节并创建新版本成功')
          } catch (versionError) {
            console.error('创建章节版本失败:', versionError);
            ElMessage.error('章节更新成功，但创建新版本失败')
            // Still proceed as chapter update was successful
          }
        }
        dialogVisible.value = false
        fetchChapters()
      } catch (error) {
        ElMessage.error(dialogType.value === 'create' ? '创建章节失败' : '更新章节失败')
        console.error(error)
      }
    }

    // 删除章节
    const handleDelete = async (chapter) => {
      try {
        await ElMessageBox.confirm('确定要删除这个章节吗？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await chapterAPI.deleteChapter(chapter.id)
        ElMessage.success('删除章节成功')
        fetchChapters()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除章节失败')
          console.error(error)
        }
      }
    }

    // 生成内容
    const handleGenerate = async () => {
      if (!currentChapterId.value) {
        ElMessage.error('章节ID无效')
        return
      }

      generatingContent.value = true
      generatingProgress.value = 0
      generatedContent.value = ''

      try {
        // 模拟生成进度
        const progressInterval = setInterval(() => {
          if (generatingProgress.value < 90) {
            generatingProgress.value += 10
          }
        }, 1000)

        // 调用API生成内容
        const response = await chapterAPI.generateContent(currentChapterId.value, generateForm.value)

        clearInterval(progressInterval)
        generatingProgress.value = 100
        generatingContent.value = false
        generatedContent.value = response.data.content

        ElMessage.success('内容生成成功')
      } catch (error) {
        generatingContent.value = false
        ElMessage.error('生成内容失败')
        console.error(error)
      }
    }

    // 重新生成内容
    const regenerateContent = () => {
      generatedContent.value = ''
      handleGenerate()
    }

    // 应用生成的内容
    const applyGeneratedContent = async () => {
      try {
        const chapter = chapters.value.find(c => c.id === currentChapterId.value)
        if (chapter) {
          const updatedChapter = {
            ...chapter,
            content: generatedContent.value,
            status: 'draft'
          }

          await chapterAPI.updateChapter(currentChapterId.value, updatedChapter)
          ElMessage.success('已应用生成的内容')
          generateDialogVisible.value = false
          fetchChapters()
        }
      } catch (error) {
        ElMessage.error('应用内容失败')
        console.error(error)
      }
    }

    // 显示管理章节详情对话框
    const showManageDialog = async (chapter) => {
      currentManagingChapter.value = chapter;
      activeTabInManageDialog.value = 'versions'; // 重置到第一个tab
      selectedVersionId.value = null; // 重置选中的版本
      selectedVersionContent.value = null; // 清空版本内容
      manageChapterDialogVisible.value = true;
      await fetchChapterVersions(chapter.id);
      await fetchChapterEditorComments(chapter.id);
      await fetchChapterReaderReviews(chapter.id);
    };

    const resetManageDialog = () => {
      currentManagingChapter.value = null;
      chapterVersions.value = [];
      selectedVersionId.value = null;
      selectedVersionContent.value = null;
      editorComments.value = [];
      readerReviews.value = [];
      isLoadingVersions.value = false;
      isLoadingVersionContent.value = false;
      isLoadingEditorComments.value = false;
      isLoadingReaderReviews.value = false;
      newEditorCommentForm.value.content = '';
      newReaderReviewForm.value.content = '';
      newReaderReviewForm.value.rating = 0;
    };

    // 新的AI生成相关方法
    const showNewAIGenerationDialog = () => {
      if (!chapterForm.value.title) {
        ElMessage.warning('请先填写章节标题')
        return
      }
      newAIGenerationDialogVisible.value = true
    }

    const getChapterContextData = () => {
      return {
        chapterTitle: chapterForm.value.title,
        chapterNumber: chapterForm.value.chapterNumber,
        chapterStatus: chapterForm.value.status
      }
    }

    const handleNewAIContentGenerated = ({ content, action }) => {
      if (action === 'replace') {
        chapterForm.value.content = content
      } else if (action === 'append') {
        if (chapterForm.value.content) {
          chapterForm.value.content += '\n\n' + content
        } else {
          chapterForm.value.content = content
        }
      }
      ElMessage.success('AI生成内容已应用')
    }

    // 获取章节版本列表
    const fetchChapterVersions = async (chapterId) => {
      if (!chapterId) return;
      isLoadingVersions.value = true;
      try {
        // 假设API返回的数据结构是 { data: [...] } 或者直接是数组
        const response = await chapterAPI.getChapterVersions(chapterId);
        chapterVersions.value = Array.isArray(response) ? response : (response.data || []);
      } catch (error) {
        ElMessage.error('获取章节版本列表失败');
        console.error('Error fetching chapter versions:', error);
        chapterVersions.value = [];
      } finally {
        isLoadingVersions.value = false;
      }
    };

    // 获取选中版本的内容
    const handleVersionSelect = async (versionId) => {
      if (!versionId || !currentManagingChapter.value) return;
      isLoadingVersionContent.value = true;
      selectedVersionContent.value = null; // 清空旧内容
      try {
        const response = await chapterAPI.getChapterVersion(currentManagingChapter.value.id, versionId);
        // 假设API返回的数据结构是 { data: { content: '...' } } 或者 { content: '...' }
        selectedVersionContent.value = response.content || (response.data && response.data.content) || '无法加载版本内容';
      } catch (error) {
        ElMessage.error('获取版本内容失败');
        console.error('Error fetching chapter version content:', error);
        selectedVersionContent.value = '加载内容失败';
      } finally {
        isLoadingVersionContent.value = false;
      }
    };

    // 获取编辑意见列表
    const fetchChapterEditorComments = async (chapterId) => {
      if (!chapterId) return;
      isLoadingEditorComments.value = true;
      try {
        const response = await chapterAPI.getChapterEditorComments(chapterId);
        editorComments.value = Array.isArray(response) ? response : (response.data || []);
      } catch (error) {
        ElMessage.error('获取编辑意见列表失败');
        console.error('Error fetching editor comments:', error);
        editorComments.value = [];
      } finally {
        isLoadingEditorComments.value = false;
      }
    };

    // 获取读者评价列表
    const fetchChapterReaderReviews = async (chapterId) => {
      if (!chapterId) return;
      isLoadingReaderReviews.value = true;
      try {
        const response = await chapterAPI.getChapterReaderReviews(chapterId);
        readerReviews.value = Array.isArray(response) ? response : (response.data || []);
      } catch (error) {
        ElMessage.error('获取读者评价列表失败');
        console.error('Error fetching reader reviews:', error);
        readerReviews.value = [];
      } finally {
        isLoadingReaderReviews.value = false;
      }
    };

    // 添加编辑意见
    const handleAddEditorComment = async () => {
      if (!currentManagingChapter.value || !newEditorCommentForm.value.content.trim()) {
        ElMessage.warning('请输入意见内容');
        return;
      }
      try {
        const payload = {
          content: newEditorCommentForm.value.content,
          // editorName: '当前编辑' // 需要实际的用户数据
        };
        await chapterAPI.createChapterEditorComment(currentManagingChapter.value.id, payload);
        ElMessage.success('编辑意见提交成功');
        newEditorCommentForm.value.content = '';
        fetchChapterEditorComments(currentManagingChapter.value.id); // 重新加载列表
      } catch (error) {
        ElMessage.error('提交编辑意见失败');
        console.error('Error adding editor comment:', error);
      }
    };

    // 添加读者评价
    const handleAddReaderReview = async () => {
      if (!currentManagingChapter.value || !newReaderReviewForm.value.content.trim()) {
        ElMessage.warning('请输入评价内容');
        return;
      }
      if (newReaderReviewForm.value.rating === 0) {
        ElMessage.warning('请给章节评分');
        return;
      }
      try {
        const payload = {
          content: newReaderReviewForm.value.content,
          rating: newReaderReviewForm.value.rating,
          // readerName: '当前读者' // 需要实际的用户数据
        };
        await chapterAPI.createChapterReaderReview(currentManagingChapter.value.id, payload);
        ElMessage.success('读者评价提交成功');
        newReaderReviewForm.value.content = '';
        newReaderReviewForm.value.rating = 0;
        fetchChapterReaderReviews(currentManagingChapter.value.id); // 重新加载列表
      } catch (error) {
        ElMessage.error('提交读者评价失败');
        console.error('Error adding reader review:', error);
      }
    };

    // 组件挂载时获取章节列表
    onMounted(() => {
      if (projectId) {
        fetchChapters()
      } else {
        ElMessage.warning('未指定项目ID')
      }
    })

    return {
      chapters,
      dialogVisible,
      dialogType,
      chapterForm,
      generateDialogVisible,
      generateForm,
      generatingContent,
      generatingProgress,
      generatedContent,
      showCreateDialog,
      showEditDialog,
      showGenerateDialog,
      handleSubmit,
      handleDelete,
      handleGenerate,
      regenerateContent,
      applyGeneratedContent,
      getStatusType,
      getStatusText,
      // 管理章节详情相关
      manageChapterDialogVisible,
      currentManagingChapter,
      chapterVersions,
      selectedVersionId,
      selectedVersionContent,
      editorComments,
      readerReviews,
      activeTabInManageDialog,
      isLoadingVersions,
      isLoadingVersionContent,
      isLoadingEditorComments,
      isLoadingReaderReviews,
      newEditorCommentForm,
      newReaderReviewForm,
      showManageDialog,
      handleVersionSelect,
      handleAddEditorComment,
      handleAddReaderReview,
      resetManageDialog,
      goBack,
      newAIGenerationDialogVisible,
      showNewAIGenerationDialog,
      getChapterContextData,
      handleNewAIContentGenerated,
      projectId
    }
  }
}
</script>


<style scoped>
.chapters-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.generating-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.generated-content {
  margin-top: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.content-preview {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
}

.action-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.ai-generation-section {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
