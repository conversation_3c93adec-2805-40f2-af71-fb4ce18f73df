<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" link>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="workflow-info">
          <h2>{{ workflowName || '新建流程' }}</h2>
          <span class="workflow-status">{{ isEditing ? '编辑模式' : '查看模式' }}</span>
        </div>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="toggleNodePalette">
          <el-icon><Grid /></el-icon>
          {{ showNodePalette ? '隐藏' : '显示' }}节点面板
        </el-button>
        <el-button @click="togglePropertyPanel">
          <el-icon><Setting /></el-icon>
          {{ showPropertyPanel ? '隐藏' : '显示' }}属性面板
        </el-button>
        <el-divider direction="vertical" />
        <el-button v-if="isEditing" @click="saveWorkflow" type="primary" :loading="saving">
          <el-icon><DocumentAdd /></el-icon>
          保存
        </el-button>
        <el-button @click="executeWorkflow" type="success">
          <el-icon><VideoPlay /></el-icon>
          执行
        </el-button>
        <el-button v-if="isEditing" @click="testAddNode" type="warning">
          测试添加节点
        </el-button>
        <el-button @click="debugInfo" type="info">
          调试信息
        </el-button>
        <el-button v-if="isEditing" @click="testConnection" type="success">
          测试连接
        </el-button>
        <el-button @click="debugSelection" type="warning">
          调试选择
        </el-button>
        <el-dropdown @command="handleMenuCommand">
          <el-button>
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="export">导出流程</el-dropdown-item>
              <el-dropdown-item command="import">导入流程</el-dropdown-item>
              <el-dropdown-item command="version">版本管理</el-dropdown-item>
              <el-dropdown-item v-if="isEditing" command="settings" divided>流程设置</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧节点面板 -->
      <div v-show="showNodePalette" class="node-palette">
        <h3>节点类型</h3>
        <div class="node-list">
          <div
            v-for="nodeType in nodeTypes"
            :key="nodeType.type"
            class="node-item"
            draggable="true"
            @dragstart="onNodeDragStart($event, nodeType)"
            @dragend="onNodeDragEnd"
            @mousedown="onNodeMouseDown($event, nodeType)"
          >
            <div class="node-icon" style="pointer-events: none;">
              <el-icon :size="20">
                <component :is="nodeType.icon" />
              </el-icon>
            </div>
            <div class="node-info" style="pointer-events: none;">
              <div class="node-name">{{ nodeType.name }}</div>
              <div class="node-desc">{{ nodeType.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div
        class="canvas-container"
        ref="canvasContainer"
      >
        <VueFlow
          v-model="elements"
          :class="{ 'readonly': !isEditing }"
          @node-click="onNodeClick"
          @edge-click="onEdgeClick"
          @pane-click="onPaneClick"
          @drop="onDrop"
          @dragover="onDragOver"
          @connect="onConnect"
          @connect-start="onConnectStart"
          @connect-end="onConnectEnd"
          :node-types="nodeTypeComponents"
          :connection-mode="'loose'"
          :snap-to-grid="true"
          :snap-grid="[15, 15]"
          :default-edge-options="defaultEdgeOptions"
          :nodes-connectable="true"
          :edges-updatable="true"
          :nodes-draggable="true"
          :zoom-on-scroll="true"
          :pan-on-scroll="false"
        >
          <!-- 背景 -->
          <Background pattern-color="#aaa" :gap="16" />
          
          <!-- 控制器 -->
          <Controls />
          
          <!-- 小地图 -->
          <MiniMap />
          
          <!-- 自定义节点 -->
          <template #node-start="nodeProps">
            <StartNode :data="nodeProps.data" :selected="selectedNode?.id === nodeProps.id" />
          </template>

          <template #node-ai_generation="nodeProps">
            <AIGenerationNode :data="nodeProps.data" :selected="selectedNode?.id === nodeProps.id" />
          </template>

          <template #node-user_input="nodeProps">
            <UserInputNode :data="nodeProps.data" :selected="selectedNode?.id === nodeProps.id" />
          </template>

          <template #node-condition="nodeProps">
            <ConditionNode :data="nodeProps.data" :selected="selectedNode?.id === nodeProps.id" />
          </template>

          <template #node-end="nodeProps">
            <EndNode :data="nodeProps.data" :selected="selectedNode?.id === nodeProps.id" />
          </template>
        </VueFlow>
      </div>

      <!-- 右侧属性面板 -->
      <div v-show="showPropertyPanel" class="property-panel">
        <div v-if="!selectedNode && !selectedEdge" class="no-selection">
          <el-empty description="请选择节点或连接线查看属性" />
        </div>
        
        <!-- 节点属性 -->
        <div v-if="selectedNode" class="node-properties">
          <h3>节点属性</h3>
          <NodePropertyForm
            :node="selectedNode"
            :is-editing="isEditing"
            @update="updateNodeProperties"
          />
        </div>
        
        <!-- 连接线属性 -->
        <div v-if="selectedEdge" class="edge-properties">
          <h3>连接属性</h3>
          <EdgePropertyForm
            :edge="selectedEdge"
            :is-editing="isEditing"
            @update="updateEdgeProperties"
          />
        </div>
      </div>
    </div>

    <!-- 保存对话框 -->
    <el-dialog v-model="saveDialogVisible" title="保存流程" width="500px">
      <el-form :model="saveForm" :rules="saveRules" ref="saveFormRef" label-width="100px">
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="saveForm.name" placeholder="请输入流程名称" />
        </el-form-item>
        <el-form-item label="变更说明" prop="changeLog">
          <el-input
            v-model="saveForm.changeLog"
            type="textarea"
            :rows="3"
            placeholder="请描述本次修改的内容"
          />
        </el-form-item>
        <el-form-item label="创建新版本">
          <el-switch v-model="saveForm.createNewVersion" />
          <span class="form-tip">开启后将创建新版本，保留历史记录</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="saveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSave" :loading="saving">保存</el-button>
      </template>
    </el-dialog>

    <!-- 执行对话框 -->
    <el-dialog v-model="executeDialogVisible" title="执行流程" width="600px">
      <ExecutionConfigForm
        :workflow="currentWorkflow"
        @execute="startExecution"
        @cancel="executeDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Grid,
  Setting,
  DocumentAdd,
  VideoPlay,
  MoreFilled,
  User,
  MagicStick,
  QuestionFilled,
  Flag
} from '@element-plus/icons-vue'

// Vue Flow 相关导入
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'

// 自定义组件导入
import StartNode from '@/components/workflow/nodes/StartNode.vue'
import AIGenerationNode from '@/components/workflow/nodes/AIGenerationNode.vue'
import UserInputNode from '@/components/workflow/nodes/UserInputNode.vue'
import ConditionNode from '@/components/workflow/nodes/ConditionNode.vue'
import EndNode from '@/components/workflow/nodes/EndNode.vue'
import NodePropertyForm from '@/components/workflow/NodePropertyForm.vue'
import EdgePropertyForm from '@/components/workflow/EdgePropertyForm.vue'
import ExecutionConfigForm from '@/components/workflow/ExecutionConfigForm.vue'

import api from '@/services/api'
import workflowAPI from '@/services/workflowAPI'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const elements = ref([])
const selectedNode = ref(null)
const selectedEdge = ref(null)
const currentWorkflow = ref(null)
const showNodePalette = ref(true)
const showPropertyPanel = ref(true)
const saveDialogVisible = ref(false)
const executeDialogVisible = ref(false)
const canvasContainer = ref()
const saveFormRef = ref()

// 节点类型组件映射
const nodeTypeComponents = {
  start: StartNode,
  ai_generation: AIGenerationNode,
  user_input: UserInputNode,
  condition: ConditionNode,
  end: EndNode
}

// 默认边配置
const defaultEdgeOptions = {
  type: 'smoothstep',
  markerEnd: {
    type: 'arrowclosed',
    width: 20,
    height: 20,
    color: '#999'
  },
  style: {
    strokeWidth: 2,
    stroke: '#999'
  }
}

// 表单数据
const saveForm = reactive({
  name: '',
  changeLog: '',
  createNewVersion: false
})

const saveRules = {
  name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
  changeLog: [{ required: true, message: '请输入变更说明', trigger: 'blur' }]
}

// 节点类型定义
const nodeTypes = [
  {
    type: 'start',
    name: '开始',
    description: '流程开始节点',
    icon: VideoPlay
  },
  {
    type: 'ai_generation',
    name: 'AI生成',
    description: '使用AI生成内容',
    icon: MagicStick
  },
  {
    type: 'user_input',
    name: '用户输入',
    description: '等待用户输入',
    icon: User
  },
  {
    type: 'condition',
    name: '条件判断',
    description: '根据条件分支',
    icon: QuestionFilled
  },
  {
    type: 'end',
    name: '结束',
    description: '流程结束节点',
    icon: Flag
  }
]

// 计算属性
const workflowId = computed(() => route.params.id)
const isEditing = computed(() => route.query.mode === 'edit' || !workflowId.value)
const workflowName = computed(() => currentWorkflow.value?.name || '')

// 方法
const goBack = () => {
  router.push('/workflow-templates')
}

const toggleNodePalette = () => {
  showNodePalette.value = !showNodePalette.value
}

const togglePropertyPanel = () => {
  showPropertyPanel.value = !showPropertyPanel.value
}

const loadWorkflow = async () => {
  if (!workflowId.value) return
  
  loading.value = true
  try {
    const response = await workflowAPI.getTemplateById(workflowId.value)
    currentWorkflow.value = response
    
    // 转换数据格式为Vue Flow格式
    const nodes = response.nodes?.map(node => ({
      id: node.nodeId,
      type: node.nodeType,
      position: { x: node.positionX || 0, y: node.positionY || 0 },
      data: {
        id: node.nodeId,
        name: node.name,
        description: node.description,
        config: node.config || {}
      }
    })) || []
    
    const edges = response.connections?.map(conn => ({
      id: `${conn.sourceNodeId}-${conn.targetNodeId}`,
      source: conn.sourceNodeId,
      target: conn.targetNodeId,
      sourceHandle: conn.sourceHandle,
      targetHandle: conn.targetHandle,
      data: {
        conditionConfig: conn.conditionConfig || {}
      }
    })) || []
    
    elements.value = [...nodes, ...edges]
    
    // 设置保存表单默认值
    saveForm.name = response.name
    
  } catch (error) {
    ElMessage.error('加载流程失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const saveWorkflow = () => {
  if (!isEditing.value) return
  saveDialogVisible.value = true
}

const confirmSave = async () => {
  try {
    await saveFormRef.value.validate()
    saving.value = true
    
    // 转换Vue Flow数据为后端格式
    const nodes = elements.value
      .filter(el => el.type && el.type !== 'default')
      .map(node => ({
        nodeId: node.id,
        nodeType: node.type,
        name: node.data.name,
        description: node.data.description,
        positionX: node.position.x,
        positionY: node.position.y,
        config: node.data.config || {}
      }))
    
    const connections = elements.value
      .filter(el => el.source && el.target)
      .map(edge => ({
        sourceNodeId: edge.source,
        targetNodeId: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        conditionConfig: edge.data?.conditionConfig || {}
      }))
    
    const workflowData = {
      name: saveForm.name,
      config: { nodes, connections },
      createNewVersion: saveForm.createNewVersion,
      changeLog: saveForm.changeLog
    }
    
    if (workflowId.value) {
      // 更新现有流程
      await workflowAPI.updateTemplate(workflowId.value, workflowData)
    } else {
      // 创建新流程
      const response = await workflowAPI.createTemplate(workflowData)
      // 跳转到新创建的流程
      router.replace(`/workflow-designer/${response.id}?mode=edit`)
    }
    
    ElMessage.success('流程保存成功')
    saveDialogVisible.value = false
    
    // 重新加载数据
    if (workflowId.value) {
      await loadWorkflow()
    }
    
  } catch (error) {
    if (error.errors) return // 表单验证错误
    ElMessage.error('保存流程失败')
    console.error(error)
  } finally {
    saving.value = false
  }
}

const executeWorkflow = () => {
  executeDialogVisible.value = true
}

const startExecution = async (config) => {
  try {
    const response = await workflowAPI.executeWorkflow(workflowId.value, config)
    ElMessage.success('流程执行已启动')
    executeDialogVisible.value = false
    
    // 跳转到执行监控页面
    router.push(`/workflow-execution/${response.executionId}`)
  } catch (error) {
    ElMessage.error('启动流程执行失败')
    console.error(error)
  }
}

const handleMenuCommand = (command) => {
  switch (command) {
    case 'export':
      exportWorkflow()
      break
    case 'import':
      importWorkflow()
      break
    case 'version':
      showVersionHistory()
      break
    case 'settings':
      showWorkflowSettings()
      break
  }
}

const exportWorkflow = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

const importWorkflow = () => {
  // TODO: 实现导入功能
  ElMessage.info('导入功能开发中')
}

const showVersionHistory = () => {
  // TODO: 实现版本历史功能
  ElMessage.info('版本管理功能开发中')
}

const showWorkflowSettings = () => {
  // TODO: 实现流程设置功能
  ElMessage.info('流程设置功能开发中')
}

// Vue Flow 事件处理
const onNodeClick = (event) => {
  console.log('节点点击事件:', event)

  // 确保获取完整的节点对象
  const clickedNode = event.node || event
  console.log('选中的节点:', clickedNode)

  selectedNode.value = clickedNode
  selectedEdge.value = null

  // 确保属性面板显示
  if (!showPropertyPanel.value) {
    showPropertyPanel.value = true
  }
}

const onEdgeClick = (event) => {
  selectedEdge.value = event.edge
  selectedNode.value = null
}

const onPaneClick = () => {
  selectedNode.value = null
  selectedEdge.value = null
}

// 拖拽状态
let draggedNodeType = null

const onNodeMouseDown = (event, nodeType) => {
  console.log('鼠标按下:', nodeType)
}

const onNodeDragStart = (event, nodeType) => {
  console.log('拖拽开始事件触发:', nodeType, 'isEditing:', isEditing.value)

  // 暂时移除编辑模式限制来测试拖拽
  // if (!isEditing.value) {
  //   console.log('不在编辑模式，阻止拖拽')
  //   event.preventDefault()
  //   return false
  // }

  console.log('拖拽开始:', nodeType)
  draggedNodeType = nodeType

  // 设置拖拽数据
  event.dataTransfer.setData('application/reactflow', JSON.stringify(nodeType))
  event.dataTransfer.setData('text/plain', nodeType.type)
  event.dataTransfer.effectAllowed = 'move'

  // 添加拖拽样式
  event.target.style.opacity = '0.5'

  console.log('拖拽数据已设置')
  return true
}

const onNodeDragEnd = (event) => {
  console.log('拖拽结束')
  event.target.style.opacity = '1'
  draggedNodeType = null
}

const onDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'move'
}

const onDrop = (event) => {
  event.preventDefault()

  // 暂时移除编辑模式限制来测试拖拽
  if (!draggedNodeType) {
    console.log('无法拖拽: 拖拽数据=', draggedNodeType)
    return
  }

  console.log('处理拖拽放置:', draggedNodeType)

  // 获取 Vue Flow 实例的位置
  const vueFlowBounds = event.currentTarget.getBoundingClientRect()

  // 计算相对于 Vue Flow 的位置
  const position = {
    x: event.clientX - vueFlowBounds.left,
    y: event.clientY - vueFlowBounds.top
  }

  console.log('拖拽位置:', position)

  // 生成唯一ID
  const nodeId = `${draggedNodeType.type}_${Date.now()}`

  const newNode = {
    id: nodeId,
    type: draggedNodeType.type,
    position: position,
    data: {
      id: nodeId,
      name: draggedNodeType.name,
      description: draggedNodeType.description,
      config: {},
      executionStatus: 'pending'
    }
  }

  console.log('创建新节点:', newNode)

  // 添加节点到画布
  elements.value = [...elements.value, newNode]

  // 选中新创建的节点
  selectedNode.value = newNode

  // 清除拖拽状态
  draggedNodeType = null
}

// 测试添加节点功能
const testAddNode = () => {
  console.log('测试添加节点')

  const nodeId = `test_${Date.now()}`
  const newNode = {
    id: nodeId,
    type: 'start',
    position: { x: 100, y: 100 },
    data: {
      id: nodeId,
      name: '测试开始节点',
      description: '这是一个测试节点',
      config: {},
      executionStatus: 'pending'
    }
  }

  console.log('添加测试节点:', newNode)
  elements.value = [...elements.value, newNode]
  selectedNode.value = newNode
}

// 调试信息
const debugInfo = () => {
  console.log('=== 调试信息 ===')
  console.log('当前路由:', route)
  console.log('workflowId:', workflowId.value)
  console.log('route.query.mode:', route.query.mode)
  console.log('isEditing:', isEditing.value)
  console.log('elements数量:', elements.value.length)
  console.log('nodeTypes:', nodeTypes)
  console.log('===============')
}

// 调试选择状态
const debugSelection = () => {
  console.log('=== 选择状态调试 ===')
  console.log('selectedNode:', selectedNode.value)
  console.log('selectedEdge:', selectedEdge.value)
  console.log('showPropertyPanel:', showPropertyPanel.value)
  console.log('isEditing:', isEditing.value)
  console.log('elements:', elements.value)
  console.log('==================')
}

// 连接事件处理
const onConnect = (connection) => {
  console.log('创建连接:', connection)

  // 检查连接是否有效
  if (!connection.source || !connection.target) {
    console.error('无效的连接:', connection)
    return
  }

  // 检查是否已存在相同的连接
  const existingEdge = elements.value.find(el =>
    el.source === connection.source &&
    el.target === connection.target &&
    el.sourceHandle === connection.sourceHandle &&
    el.targetHandle === connection.targetHandle
  )

  if (existingEdge) {
    console.log('连接已存在，跳过创建')
    return
  }

  // 生成唯一的边ID
  const edgeId = `edge_${connection.source}_${connection.target}_${Date.now()}`

  const newEdge = {
    id: edgeId,
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle || null,
    targetHandle: connection.targetHandle || null,
    type: 'smoothstep',
    animated: false,
    markerEnd: {
      type: 'arrowclosed',
      width: 20,
      height: 20,
      color: '#999'
    },
    style: {
      strokeWidth: 2,
      stroke: '#999'
    }
  }

  console.log('添加新边:', newEdge)
  elements.value = [...elements.value, newEdge]
}

const onConnectStart = (event, params) => {
  console.log('开始连接:', event, params)
  if (params) {
    const { nodeId, handleId, handleType } = params
    console.log('连接参数:', { nodeId, handleId, handleType })
  }
}

const onConnectEnd = (event) => {
  console.log('连接结束:', event)
}

// 测试连接功能
const testConnection = () => {
  console.log('测试连接功能')

  // 确保至少有两个节点
  if (elements.value.filter(el => !el.source).length < 2) {
    console.log('需要至少两个节点才能测试连接')
    // 先添加两个测试节点
    const startNode = {
      id: 'test_start',
      type: 'start',
      position: { x: 100, y: 100 },
      data: { id: 'test_start', name: '测试开始', description: '测试开始节点' }
    }

    const endNode = {
      id: 'test_end',
      type: 'end',
      position: { x: 300, y: 100 },
      data: { id: 'test_end', name: '测试结束', description: '测试结束节点' }
    }

    elements.value = [...elements.value, startNode, endNode]
    console.log('已添加测试节点')
  }

  // 创建测试连接
  const nodes = elements.value.filter(el => !el.source)
  if (nodes.length >= 2) {
    const connection = {
      source: nodes[0].id,
      target: nodes[1].id,
      sourceHandle: null,
      targetHandle: null
    }

    console.log('创建测试连接:', connection)
    onConnect(connection)
  }
}

const updateNodeProperties = (nodeId, properties) => {
  const nodeIndex = elements.value.findIndex(el => el.id === nodeId)
  if (nodeIndex > -1) {
    elements.value[nodeIndex].data = { ...elements.value[nodeIndex].data, ...properties }
  }
}

const updateEdgeProperties = (edgeId, properties) => {
  const edgeIndex = elements.value.findIndex(el => el.id === edgeId)
  if (edgeIndex > -1) {
    elements.value[edgeIndex].data = { ...elements.value[edgeIndex].data, ...properties }
  }
}

// 生命周期
onMounted(() => {
  if (workflowId.value) {
    loadWorkflow()
  }
})
</script>

<style scoped>
.workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.workflow-info h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.workflow-status {
  font-size: 12px;
  color: #999;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-palette {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  padding: 16px;
  overflow-y: auto;
}

.node-palette h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
}

.node-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.node-item:active {
  cursor: grabbing;
}

.node-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-info {
  flex: 1;
}

.node-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.node-desc {
  font-size: 12px;
  color: #666;
}

.canvas-container {
  flex: 1;
  position: relative;
}

.property-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e4e7ed;
  padding: 16px;
  overflow-y: auto;
}

.property-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}

/* Vue Flow 样式覆盖 */
:deep(.vue-flow__node) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__node.selected) {
  box-shadow: 0 0 0 2px #409eff;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #409eff;
  stroke-width: 3;
}

.readonly :deep(.vue-flow__node) {
  pointer-events: none;
}

.readonly :deep(.vue-flow__edge) {
  pointer-events: none;
}
</style>
