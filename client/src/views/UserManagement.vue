<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        创建用户
      </el-button>
    </div>

    <!-- 用户列表表格 -->
    <el-table :data="users" v-loading="loading" stripe>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="role" label="角色">
        <template #default="{ row }">
          <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
            {{ row.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginAt" label="最后登录时间">
        <template #default="{ row }">
          {{ row.lastLoginAt ? formatDate(row.lastLoginAt) : '从未登录' }}
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="editUser(row)">编辑</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="deleteUser(row)"
            :disabled="row.id === currentUser.id"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog 
      :title="dialogMode === 'create' ? '创建用户' : '编辑用户'" 
      v-model="dialogVisible"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱（可选）" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="普通用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
        
        <el-form-item 
          :label="dialogMode === 'create' ? '密码' : '新密码'" 
          prop="password"
        >
          <el-input 
            v-model="userForm.password" 
            type="password" 
            :placeholder="dialogMode === 'create' ? '请输入密码' : '留空则不修改密码'"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ dialogMode === 'create' ? '创建' : '更新' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { userAPI } from '../services/api';
import authService from '../services/auth';

// 响应式数据
const users = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const dialogMode = ref('create'); // 'create' 或 'edit'
const submitting = ref(false);
const userFormRef = ref(null);

// 当前登录用户
const currentUser = computed(() => authService.getUser() || {});

// 表单数据
const userForm = reactive({
  id: null,
  username: '',
  email: '',
  role: 'user',
  password: ''
});

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 30, message: '用户名长度在 3 到 30 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { 
      validator: (rule, value, callback) => {
        if (dialogMode.value === 'create' && !value) {
          callback(new Error('请输入密码'));
        } else if (value && value.length < 6) {
          callback(new Error('密码长度不能少于6位'));
        } else {
          callback();
        }
      }, 
      trigger: 'blur' 
    }
  ]
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true;
  try {
    const response = await userAPI.getAllUsers();
    users.value = response.users;
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error(error.response?.data?.message || '获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 显示创建用户对话框
const showCreateDialog = () => {
  dialogMode.value = 'create';
  resetForm();
  dialogVisible.value = true;
};

// 编辑用户
const editUser = (user) => {
  dialogMode.value = 'edit';
  userForm.id = user.id;
  userForm.username = user.username;
  userForm.email = user.email || '';
  userForm.role = user.role;
  userForm.password = '';
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  userForm.id = null;
  userForm.username = '';
  userForm.email = '';
  userForm.role = 'user';
  userForm.password = '';
  if (userFormRef.value) {
    userFormRef.value.clearValidate();
  }
};

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return;

  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true;
      try {
        const formData = {
          username: userForm.username,
          email: userForm.email || null,
          role: userForm.role
        };
        
        // 只有在密码不为空时才包含密码字段
        if (userForm.password) {
          formData.password = userForm.password;
        }
        
        if (dialogMode.value === 'create') {
          await userAPI.createUser(formData);
          ElMessage.success('用户创建成功');
        } else {
          await userAPI.updateUser(userForm.id, formData);
          ElMessage.success('用户更新成功');
        }
        
        dialogVisible.value = false;
        await fetchUsers();
      } catch (error) {
        console.error('操作失败:', error);
        ElMessage.error(error.response?.data?.message || '操作失败');
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await userAPI.deleteUser(user.id);
    ElMessage.success('用户删除成功');
    await fetchUsers();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error);
      ElMessage.error(error.response?.data?.message || '删除用户失败');
    }
  }
};

// 组件挂载时获取用户列表
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
