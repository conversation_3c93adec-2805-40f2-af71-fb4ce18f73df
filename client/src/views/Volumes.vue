<template>
  <PageLoading page-key="volumes" @retry="fetchVolumes">
    <div class="volumes-container">
    <div class="header">
      <div class="header-left">
        <el-button icon="ArrowLeft" @click="goBack">返回</el-button>
        <h2>分卷剧情管理</h2>
      </div>
      <el-button type="primary" @click="showCreateDialog">创建分卷</el-button>
    </div>

    <!-- 分卷列表 -->
    <el-table :data="volumes" style="width: 100%">
      <el-table-column prop="title" label="分卷标题" />
      <el-table-column prop="wordCount" label="预计字数" />
      <el-table-column prop="chapterCount" label="预设章节数" />
      <el-table-column label="剧情梗概">
        <template #default="scope">
          <div class="summary-preview">{{ getSummaryPreview(scope.row.summary) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="400">
        <template #default="scope">
          <el-button size="small" @click="showEditDialog(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="success"
            @click="navigateToChapterGroups(scope.row.id)"
          >章节分组</el-button>
          <el-button
            size="small"
            type="primary"
            @click="showVersionsDialog(scope.row)"
          >历史版本</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑分卷对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建分卷' : '编辑分卷'"
      v-model="dialogVisible"
      width="80%"
    >
      <el-form :model="volumeForm" label-width="100px" :rules="rules" ref="volumeFormRef">
        <el-form-item label="分卷标题" prop="title">
          <el-input v-model="volumeForm.title" placeholder="请输入分卷标题" />
        </el-form-item>
        <el-form-item label="预计字数" prop="wordCount">
          <el-input-number v-model="volumeForm.wordCount" :min="0" :step="10000" />
        </el-form-item>
        <el-form-item label="预设章节数" prop="chapterCount">
          <el-input-number v-model="volumeForm.chapterCount" :min="1" />
        </el-form-item>
        <el-form-item label="剧情梗概" prop="summary">
          <el-input
            type="textarea"
            v-model="volumeForm.summary"
            placeholder="请输入分卷剧情梗概"
            :rows="10"
          />
        </el-form-item>
        <el-form-item label="分卷写作要求" prop="writingRequirements">
          <el-input
            type="textarea"
            v-model="volumeForm.writingRequirements"
            placeholder="请输入分卷写作要求（可选）"
            :rows="6"
          />
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number v-model="volumeForm.order" :min="0" />
        </el-form-item>

        <!-- AI生成功能 -->
        <el-form-item label="AI生成">
          <div class="ai-generation-section">
            <el-button
              type="success"
              @click="showAIGenerationDialog"
              :disabled="!volumeForm.title"
            >
              <el-icon><Star /></el-icon>
              AI生成分卷内容
            </el-button>
            <el-text type="info" size="small" style="margin-left: 10px;">
              需要先填写分卷标题
            </el-text>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加历史版本对话框 -->
    <el-dialog
      title="历史版本"
      v-model="versionsDialogVisible"
      width="80%"
    >
      <div v-if="isLoadingVersions" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="volumeVersions.length === 0" class="empty-versions">
        暂无历史版本
      </div>
      <div v-else>
        <el-select v-model="selectedVersionId" placeholder="选择版本查看内容" @change="handleVersionSelect" style="width: 100%; margin-bottom: 15px;">
          <el-option
            v-for="version in volumeVersions"
            :key="version.id"
            :label="`版本 ${version.versionNumber} (${new Date(version.createdAt).toLocaleString()})`"
            :value="version.id"
          />
        </el-select>

        <div v-if="isLoadingVersionContent" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>
        <div v-else-if="selectedVersionContent" class="version-content">
          <h3>{{ selectedVersionContent.title }}</h3>
          <div class="version-info">
            <p><strong>预计字数:</strong> {{ selectedVersionContent.wordCount }}</p>
            <p><strong>预设章节数:</strong> {{ selectedVersionContent.chapterCount }}</p>
          </div>
          <div class="version-summary">
            <h4>剧情梗概:</h4>
            <p style="white-space: pre-line">{{ selectedVersionContent.summary }}</p>
          </div>
          <div v-if="selectedVersionContent.writingRequirements" class="version-requirements">
            <h4>分卷写作要求:</h4>
            <p style="white-space: pre-line">{{ selectedVersionContent.writingRequirements }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- AI生成对话框 -->
    <AIGenerationDialog
      v-model:visible="aiGenerationDialogVisible"
      content-type="分卷"
      :project-id="projectId"
      :existing-content="getExistingVolumeContent()"
      :context-data="getVolumeContextData()"
      role-filter="writer"
      @content-generated="handleAIContentGenerated"
    />
    </div>
  </PageLoading>
</template>

<script>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star } from '@element-plus/icons-vue'
import { volumeAPI } from '@/services/api'
import { useRoute, useRouter } from 'vue-router'
import AIGenerationDialog from '@/components/AIGenerationDialog.vue'
import PageLoading from '@/components/PageLoading.vue'
import { usePageLoading, useOperationLoading } from '@/composables/useLoading'

export default {
  name: 'Volumes',
  components: {
    AIGenerationDialog,
    PageLoading
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const projectId = route.params.projectId

    // 返回上一页方法
    const goBack = () => {
      router.go(-1)
    }

    // 分卷列表数据
    const volumes = ref([])

    // 对话框相关数据
    const dialogVisible = ref(false)
    const dialogType = ref('create')
    const volumeFormRef = ref(null)
    const volumeForm = ref({
      title: '',
      wordCount: 50000,
      chapterCount: 20,
      summary: '',
      writingRequirements: '',
      order: 0,
      projectId: projectId
    })

    // AI生成相关数据
    const aiGenerationDialogVisible = ref(false)

    // 加载状态管理
    const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('volumes')
    const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

    // 表单验证规则
    const rules = {
      title: [{ required: true, message: '请输入分卷标题', trigger: 'blur' }],
      summary: [{ required: true, message: '请输入剧情梗概', trigger: 'blur' }]
    }

    // 获取分卷列表
    const fetchVolumes = async () => {
      try {
        const response = await volumeAPI.getAllVolumes(projectId)
        volumes.value = response
      } catch (error) {
        ElMessage.error('获取分卷列表失败')
        console.error(error)
      }
    }

    // 获取梗概预览
    const getSummaryPreview = (summary) => {
      if (!summary) return '无梗概'
      return summary.length > 50 ? summary.substring(0, 50) + '...' : summary
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      dialogType.value = 'create'
      volumeForm.value = {
        title: '',
        wordCount: 50000,
        chapterCount: 20,
        summary: '',
        writingRequirements: '',
        order: volumes.value.length,
        projectId: projectId
      }
      dialogVisible.value = true
    }

    // 显示编辑对话框
    const showEditDialog = (volume) => {
      dialogType.value = 'edit'
      volumeForm.value = { ...volume }
      dialogVisible.value = true
    }

    // 提交表单
    const handleSubmit = async () => {
      if (!volumeFormRef.value) return

      volumeFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            if (dialogType.value === 'create') {
              await volumeAPI.createVolume(volumeForm.value)
              ElMessage.success('创建分卷成功')
            } else {
              await volumeAPI.updateVolume(volumeForm.value.id, volumeForm.value)
              ElMessage.success('更新分卷成功')
            }
            dialogVisible.value = false
            fetchVolumes()
          } catch (error) {
            ElMessage.error(dialogType.value === 'create' ? '创建分卷失败' : '更新分卷失败')
            console.error(error)
          }
        }
      })
    }

    // 删除分卷
    const handleDelete = async (volume) => {
      try {
        await ElMessageBox.confirm('确定要删除这个分卷吗？这将同时删除所有相关的章节分组。', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await volumeAPI.deleteVolume(volume.id)
        ElMessage.success('删除分卷成功')
        fetchVolumes()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除分卷失败')
          console.error(error)
        }
      }
    }

    // 导航到章节分组页面
    const navigateToChapterGroups = (volumeId) => {
      router.push({
        name: 'VolumeChapterGroups',
        params: { projectId: projectId, volumeId: volumeId }
      })
    }

    // 添加版本管理相关的状态
    const versionsDialogVisible = ref(false)
    const volumeVersions = ref([])
    const selectedVersionId = ref(null)
    const selectedVersionContent = ref(null)
    const isLoadingVersions = ref(false)
    const isLoadingVersionContent = ref(false)
    const currentVolumeId = ref(null)

    // 显示版本历史对话框
    const showVersionsDialog = async (volume) => {
      versionsDialogVisible.value = true
      currentVolumeId.value = volume.id
      selectedVersionId.value = null
      selectedVersionContent.value = null
      await fetchVolumeVersions(volume.id)
    }

    // 获取分卷版本列表
    const fetchVolumeVersions = async (volumeId) => {
      if (!volumeId) return
      isLoadingVersions.value = true
      try {
        const response = await volumeAPI.getVolumeVersions(volumeId)
        volumeVersions.value = Array.isArray(response) ? response : (response.data || [])
      } catch (error) {
        ElMessage.error('获取分卷版本列表失败')
        console.error('获取分卷版本列表失败:', error)
        volumeVersions.value = []
      } finally {
        isLoadingVersions.value = false
      }
    }

    // 获取选中版本的内容
    const handleVersionSelect = async (versionId) => {
      if (!versionId || !currentVolumeId.value) return
      isLoadingVersionContent.value = true
      selectedVersionContent.value = null
      try {
        const response = await volumeAPI.getVolumeVersion(currentVolumeId.value, versionId)
        selectedVersionContent.value = response
      } catch (error) {
        ElMessage.error('获取版本内容失败')
        console.error('获取版本内容失败:', error)
        selectedVersionContent.value = null
      } finally {
        isLoadingVersionContent.value = false
      }
    }

    // AI生成相关方法
    const showAIGenerationDialog = () => {
      if (!volumeForm.value.title) {
        ElMessage.warning('请先填写分卷标题')
        return
      }
      aiGenerationDialogVisible.value = true
    }

    const getExistingVolumeContent = () => {
      const parts = []
      if (volumeForm.value.summary) parts.push(`剧情梗概: ${volumeForm.value.summary}`)
      if (volumeForm.value.writingRequirements) parts.push(`写作要求: ${volumeForm.value.writingRequirements}`)
      return parts.join('\n\n')
    }

    const getVolumeContextData = () => {
      return {
        volumeTitle: volumeForm.value.title,
        wordCount: volumeForm.value.wordCount,
        chapterCount: volumeForm.value.chapterCount,
        order: volumeForm.value.order
      }
    }

    const handleAIContentGenerated = ({ content, action }) => {
      if (action === 'replace') {
        // 尝试解析生成的内容并分配到不同字段
        parseAndAssignVolumeContent(content)
      } else if (action === 'append') {
        // 追加到剧情梗概字段
        if (volumeForm.value.summary) {
          volumeForm.value.summary += '\n\n' + content
        } else {
          volumeForm.value.summary = content
        }
      }
      ElMessage.success('AI生成内容已应用')
    }

    const parseAndAssignVolumeContent = (content) => {
      // 尝试解析结构化的分卷内容
      const lines = content.split('\n')
      let currentField = 'summary'
      let currentContent = ''

      for (const line of lines) {
        const trimmedLine = line.trim()
        if (trimmedLine.includes('剧情梗概') || trimmedLine.includes('梗概：')) {
          if (currentContent) {
            assignVolumeContentToField(currentField, currentContent.trim())
          }
          currentField = 'summary'
          currentContent = ''
        } else if (trimmedLine.includes('写作要求') || trimmedLine.includes('要求：')) {
          if (currentContent) {
            assignVolumeContentToField(currentField, currentContent.trim())
          }
          currentField = 'writingRequirements'
          currentContent = ''
        } else if (trimmedLine && !trimmedLine.includes('：') && !trimmedLine.includes(':')) {
          currentContent += (currentContent ? '\n' : '') + trimmedLine
        }
      }

      // 处理最后一个字段
      if (currentContent) {
        assignVolumeContentToField(currentField, currentContent.trim())
      }

      // 如果没有识别到结构化内容，则全部放到剧情梗概中
      if (!volumeForm.value.summary && !volumeForm.value.writingRequirements) {
        volumeForm.value.summary = content
      }
    }

    const assignVolumeContentToField = (field, content) => {
      if (content) {
        volumeForm.value[field] = content
      }
    }

    // 组件挂载时获取分卷列表
    onMounted(() => {
      if (projectId) {
        fetchVolumes()
      } else {
        ElMessage.warning('未指定项目ID')
      }
    })

    return {
      volumes,
      dialogVisible,
      dialogType,
      volumeForm,
      volumeFormRef,
      rules,
      showCreateDialog,
      showEditDialog,
      handleSubmit,
      handleDelete,
      getSummaryPreview,
      navigateToChapterGroups,
      goBack,
      versionsDialogVisible,
      volumeVersions,
      selectedVersionId,
      selectedVersionContent,
      isLoadingVersions,
      isLoadingVersionContent,
      showVersionsDialog,
      handleVersionSelect,
      aiGenerationDialogVisible,
      showAIGenerationDialog,
      getExistingVolumeContent,
      getVolumeContextData,
      handleAIContentGenerated,
      projectId
    }
  }
}
</script>

<style scoped>
.volumes-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.summary-preview {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 添加版本管理相关样式 */
.loading-container {
  padding: 20px;
  text-align: center;
}

.empty-versions {
  padding: 20px;
  text-align: center;
  color: #909399;
}

.version-content {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.version-info {
  margin: 15px 0;
  display: flex;
  gap: 20px;
}

.version-summary {
  margin-top: 15px;
}

.version-requirements {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.ai-generation-section {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
