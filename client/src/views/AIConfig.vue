<template>
  <PageLoading page-key="ai-config" @retry="fetchConfigs">
    <div class="ai-config-container">
    <!-- 返回按钮 -->
    <div class="header-actions">
      <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
      <h2>大模型配置管理</h2>
    </div>

    <!-- 配置列表 -->
    <el-card class="config-list-card">
      <template #header>
        <div class="card-header">
          <span>AI配置列表</span>
          <el-button type="primary" @click="showCreateDialog">添加配置</el-button>
        </div>
      </template>

      <el-table :data="configs" v-loading="loading">
        <el-table-column prop="provider" label="提供商" width="120">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)">
              {{ getProviderName(row.provider) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="model" label="模型" width="200" />
        <el-table-column prop="apiKey" label="API密钥" width="150">
          <template #default="{ row }">
            <span class="api-key-display">{{ row.apiKey }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认配置" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isDefault" type="success">默认</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'">
              {{ row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300">
          <template #default="{ row }">
            <el-button size="small" @click="testConfig(row)">测试连接</el-button>
            <el-button size="small" @click="editConfig(row)">编辑</el-button>
            <el-button 
              v-if="!row.isDefault" 
              size="small" 
              type="success" 
              @click="setDefault(row)"
            >
              设为默认
            </el-button>
            <el-button size="small" type="danger" @click="deleteConfig(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑配置对话框 -->
    <el-dialog 
      :title="dialogType === 'create' ? '添加AI配置' : '编辑AI配置'"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="configForm" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="提供商" prop="provider">
          <el-select v-model="configForm.provider" placeholder="请选择提供商" style="width: 100%" @change="handleProviderChange">
            <el-option label="DeepSeek" value="deepseek" />
            <el-option label="Kimi (月之暗面)" value="kimi" />
          </el-select>
        </el-form-item>

        <el-form-item label="模型名称" prop="model">
          <el-input
            v-model="configForm.model"
            :placeholder="getModelPlaceholder()"
            :disabled="configForm.provider === 'deepseek' || configForm.provider === 'kimi'"
          />
        </el-form-item>

        <el-form-item label="API密钥" prop="apiKey">
          <el-input
            v-model="configForm.apiKey"
            type="password"
            :placeholder="getApiKeyPlaceholder()"
            show-password
          />
        </el-form-item>

        <el-form-item label="基础URL" prop="baseUrl">
          <el-input
            v-model="configForm.baseUrl"
            :placeholder="getBaseUrlPlaceholder()"
          />
        </el-form-item>

        <el-form-item label="最大Token数" prop="maxTokens">
          <el-input-number 
            v-model="configForm.maxTokens" 
            :min="100" 
            :max="32000" 
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="温度参数" prop="temperature">
          <el-input-number 
            v-model="configForm.temperature" 
            :min="0" 
            :max="2" 
            :step="0.1" 
            :precision="1"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="设为默认">
          <el-switch v-model="configForm.isDefault" />
        </el-form-item>

        <el-form-item label="启用配置">
          <el-switch v-model="configForm.isActive" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
    </div>
  </PageLoading>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '../services/api'
import PageLoading from '../components/PageLoading.vue'
import { usePageLoading, useOperationLoading } from '../composables/useLoading'

const router = useRouter()

// 加载状态管理
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('ai-config')
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

// 响应式数据
const configs = ref([])
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const dialogType = ref('create') // 'create' 或 'edit'

// 表单数据
const configForm = reactive({
  id: null,
  provider: 'deepseek',
  model: 'deepseek-chat',
  apiKey: '',
  baseUrl: '',
  maxTokens: 4000,
  temperature: 0.7,
  isDefault: false,
  isActive: true
})

// 表单引用
const formRef = ref(null)

// 表单验证规则
const formRules = {
  provider: [
    { required: true, message: '请选择提供商', trigger: 'change' }
  ],
  model: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ],
  maxTokens: [
    { required: true, message: '请输入最大Token数', trigger: 'blur' },
    { type: 'number', min: 100, max: 32000, message: 'Token数必须在100-32000之间', trigger: 'blur' }
  ],
  temperature: [
    { required: true, message: '请输入温度参数', trigger: 'blur' },
    { type: 'number', min: 0, max: 2, message: '温度参数必须在0-2之间', trigger: 'blur' }
  ]
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 获取配置列表
const fetchConfigs = async () => {
  loading.value = true
  try {
    const response = await api.get('/ai-configs')
    configs.value = response
  } catch (error) {
    ElMessage.error('获取AI配置失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 显示创建对话框
const showCreateDialog = () => {
  dialogType.value = 'create'
  resetForm()
  dialogVisible.value = true
}

// 编辑配置
const editConfig = (config) => {
  dialogType.value = 'edit'
  Object.assign(configForm, {
    id: config.id,
    provider: config.provider,
    model: config.model,
    apiKey: '', // 编辑时不显示原密钥
    baseUrl: config.baseUrl || '',
    maxTokens: config.maxTokens,
    temperature: config.temperature,
    isDefault: config.isDefault,
    isActive: config.isActive
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(configForm, {
    id: null,
    provider: 'deepseek',
    model: 'deepseek-chat',
    apiKey: '',
    baseUrl: '',
    maxTokens: 4000,
    temperature: 0.7,
    isDefault: false,
    isActive: true
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 保存配置
const saveConfig = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    const data = { ...configForm }
    delete data.id

    if (dialogType.value === 'create') {
      await api.post('/ai-configs', data)
      ElMessage.success('配置创建成功')
    } else {
      // 编辑时，如果API密钥为空，则不更新
      if (!data.apiKey) {
        delete data.apiKey
      }
      await api.put(`/ai-configs/${configForm.id}`, data)
      ElMessage.success('配置更新成功')
    }

    dialogVisible.value = false
    await fetchConfigs()
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '保存配置失败')
  } finally {
    saving.value = false
  }
}

// 删除配置
const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm('确定要删除这个配置吗？', '确认删除', {
      type: 'warning'
    })

    await api.delete(`/ai-configs/${config.id}`)
    ElMessage.success('配置删除成功')
    await fetchConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除配置失败')
    }
  }
}

// 设为默认配置
const setDefault = async (config) => {
  try {
    await api.post(`/ai-configs/${config.id}/set-default`)
    ElMessage.success('默认配置设置成功')
    await fetchConfigs()
  } catch (error) {
    ElMessage.error('设置默认配置失败')
  }
}

// 测试配置
const testConfig = async (config) => {
  const loadingMessage = ElMessage({
    message: '正在测试连接...',
    type: 'info',
    duration: 0
  })

  try {
    const response = await api.post(`/ai-configs/${config.id}/test`)
    loadingMessage.close()
    
    if (response.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(response.message || '连接测试失败')
    }
  } catch (error) {
    loadingMessage.close()
    ElMessage.error(error.response?.data?.message || '连接测试失败')
  }
}

// 获取提供商标签类型
const getProviderTagType = (provider) => {
  const types = {
    deepseek: 'primary',
    kimi: 'success',
    openai: 'success',
    claude: 'warning'
  }
  return types[provider] || 'info'
}

// 获取提供商名称
const getProviderName = (provider) => {
  const names = {
    deepseek: 'DeepSeek',
    kimi: 'Kimi',
    openai: 'OpenAI',
    claude: 'Claude'
  }
  return names[provider] || provider
}

// 处理提供商变化
const handleProviderChange = () => {
  if (configForm.provider === 'deepseek') {
    configForm.model = 'deepseek-chat'
    configForm.maxTokens = 4000
  } else if (configForm.provider === 'kimi') {
    configForm.model = 'moonshot-v1-8k'
    configForm.maxTokens = 8000
  }
}

// 获取模型输入框占位符
const getModelPlaceholder = () => {
  const placeholders = {
    deepseek: '例如: deepseek-chat',
    kimi: '例如: moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k'
  }
  return placeholders[configForm.provider] || '请输入模型名称'
}

// 获取API密钥输入框占位符
const getApiKeyPlaceholder = () => {
  const placeholders = {
    deepseek: '请输入DeepSeek API密钥',
    kimi: '请输入Kimi API密钥'
  }
  return placeholders[configForm.provider] || '请输入API密钥'
}

// 获取基础URL输入框占位符
const getBaseUrlPlaceholder = () => {
  const placeholders = {
    deepseek: '可选，留空使用默认URL (https://api.deepseek.com/v1)',
    kimi: '可选，留空使用默认URL (https://api.moonshot.cn/v1)'
  }
  return placeholders[configForm.provider] || '可选，留空使用默认URL'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchConfigs()
})
</script>

<style scoped>
.ai-config-container {
  padding: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.header-actions h2 {
  margin: 0;
  color: #303133;
}

.config-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-key-display {
  font-family: monospace;
  color: #909399;
}

.el-form-item {
  margin-bottom: 20px;
}
</style>
