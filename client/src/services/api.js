import axios from 'axios';

// 创建默认axios实例（10秒超时）
const api = axios.create({
  baseURL: '/api', // 通过Vite代理连接到后端服务器
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true // 允许携带cookie
});

// 创建AI生成专用axios实例（10分钟超时）
const aiApi = axios.create({
  baseURL: '/api', // 通过Vite代理连接到后端服务器
  timeout: 600000, // 10分钟超时，适用于AI生成等长时间操作
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true // 允许携带cookie
});



// 通用拦截器配置函数
const setupInterceptors = (axiosInstance) => {
  // 请求拦截器
  axiosInstance.interceptors.request.use(
    config => {
      // 在发送请求之前做些什么
      const token = localStorage.getItem('authToken'); // 假设token存储在localStorage
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    error => {
      // 对请求错误做些什么
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  axiosInstance.interceptors.response.use(
    response => {
      // 对响应数据做点什么
      return response.data;
    },
    error => {
      // 处理特定状态码错误
      if (error.response?.status === 401) {
        // 检查是否是登录请求，如果是登录请求则不自动跳转
        const isLoginRequest = error.config?.url?.includes('/auth/login');

        if (!isLoginRequest) {
          // 处理未授权错误（非登录请求）
          console.error('未授权访问，请登录');
          // 清除本地存储的认证信息
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          // 跳转到登录页面
          window.location.href = '/login';
        }
      } else if (error.response?.status === 403) {
        console.error('禁止访问，没有权限');
      }

      // 对响应错误做点什么
      console.error('API请求错误:', error);
      return Promise.reject(error);
    }
  );
};

// 为两个axios实例设置拦截器
setupInterceptors(api);
setupInterceptors(aiApi);

// 项目管理相关API
export const projectAPI = {
  // 获取所有项目（包含统计信息，兼容旧版本）
  getAllProjects: () => api.get('/projects'),
  // 获取项目基本信息（快速加载）
  getProjectsBasic: () => api.get('/projects-basic'),
  // 获取单个项目
  getProject: (id) => api.get(`/projects/${id}`),
  // 获取单个项目统计信息
  getProjectStats: (id) => api.get(`/projects/${id}/stats`),
  // 批量获取项目统计信息
  getBatchProjectStats: (projectIds) => api.post('/projects/batch-stats', { projectIds }),
  // 获取项目预览数据
  getProjectPreview: (id) => api.get(`/projects/${id}/preview`),
  // 创建项目
  createProject: (data) => api.post('/projects', data),
  // 更新项目
  updateProject: (id, data) => api.put(`/projects/${id}`, data),
  // 删除项目
  deleteProject: (id) => api.delete(`/projects/${id}`)
};

// 角色管理相关API
export const characterAPI = {
  // 获取所有角色
  getAllCharacters: (projectId) => api.get(`/projects/${projectId}/characters`),
  // 获取单个角色
  getCharacter: (id) => api.get(`/characters/${id}`),
  // 创建角色
  createCharacter: (data) => api.post('/characters', data),
  // 更新角色
  updateCharacter: (id, data) => api.put(`/characters/${id}`, data),
  // 删除角色
  deleteCharacter: (id) => api.delete(`/characters/${id}`)
};

// 大纲管理相关API
export const outlineAPI = {
  // 获取所有大纲
  getAllOutlines: (projectId) => api.get(`/projects/${projectId}/outlines`),
  // 获取单个大纲
  getOutline: (id) => api.get(`/outlines/${id}`),
  // 创建大纲
  createOutline: (data) => api.post('/outlines', data),
  // 更新大纲
  updateOutline: (id, data) => api.put(`/outlines/${id}`, data),
  // 删除大纲
  deleteOutline: (id) => api.delete(`/outlines/${id}`)
};

// 章节管理相关API
export const chapterAPI = {
  getAllChapters: (projectId) => api.get(`/projects/${projectId}/chapters`),
  getChapterById: (id) => api.get(`/chapters/${id}`),
  createChapter: (data) => api.post('/chapters', data),
  updateChapter: (id, data) => api.put(`/chapters/${id}`, data),
  deleteChapter: (id) => api.delete(`/chapters/${id}`),
  generateContent: (id, data) => api.post(`/chapters/${id}/generate-content`, data), // 假设有这个AI生成内容的接口
  createChapterVersion: (chapterId, data) => api.post(`/chapters/${chapterId}/versions`, data),
  getChapterVersions: (chapterId) => api.get(`/chapters/${chapterId}/versions`),
  getChapterVersion: (chapterId, versionId) => api.get(`/chapters/${chapterId}/versions/${versionId}`),
  getChapterEditorComments: (chapterId) => api.get(`/chapters/${chapterId}/editor-comments`),
  createChapterEditorComment: (chapterId, data) => api.post(`/chapters/${chapterId}/editor-comments`, data),
  getChapterReaderReviews: (chapterId) => api.get(`/chapters/${chapterId}/reader-reviews`),
  createChapterReaderReview: (chapterId, data) => api.post(`/chapters/${chapterId}/reader-reviews`, data),
};

// 提示词模板相关API
export const promptTemplateAPI = {
  // 获取所有提示词模板（使用普通超时）
  getAllTemplates: () => api.get('/prompt-templates'),
  // 获取单个提示词模板（使用普通超时）
  getTemplate: (id) => api.get(`/prompt-templates/${id}`),
  // 创建提示词模板（使用普通超时）
  createTemplate: (data) => api.post('/prompt-templates', data),
  // 更新提示词模板（使用普通超时）
  updateTemplate: (id, data) => api.put(`/prompt-templates/${id}`, data),
  // 删除提示词模板（使用普通超时）
  deleteTemplate: (id) => api.delete(`/prompt-templates/${id}`),
  // 使用模板生成提示词（使用长超时，可能涉及复杂处理）
  generatePrompt: (id, data) => aiApi.post(`/prompt-templates/${id}/generate`, data)
};

// 世界观设定相关API
export const worldSettingAPI = {
  // 获取所有世界观设定
  getAllWorldSettings: (projectId) => api.get(`/projects/${projectId}/world-settings`),
  // 获取单个世界观设定
  getWorldSetting: (id) => api.get(`/world-settings/${id}`),
  // 创建世界观设定
  createWorldSetting: (data) => api.post('/world-settings', data),
  // 更新世界观设定
  updateWorldSetting: (id, data) => api.put(`/world-settings/${id}`, data),
  // 删除世界观设定
  deleteWorldSetting: (id) => api.delete(`/world-settings/${id}`)
};

// 分卷管理相关API
export const volumeAPI = {
  // 获取所有分卷
  getAllVolumes: (projectId) => api.get(`/projects/${projectId}/volumes`),
  // 获取单个分卷
  getVolume: (id) => api.get(`/volumes/${id}`),
  // 创建分卷
  createVolume: (data) => api.post('/volumes', data),
  // 更新分卷
  updateVolume: (id, data) => api.put(`/volumes/${id}`, data),
  // 删除分卷
  deleteVolume: (id) => api.delete(`/volumes/${id}`),

  // 添加版本管理相关方法
  getVolumeVersions: (volumeId) => api.get(`/volumes/${volumeId}/versions`),
  getVolumeVersion: (volumeId, versionId) => api.get(`/volumes/${volumeId}/versions/${versionId}`),
  createVolumeVersion: (volumeId, data) => api.post(`/volumes/${volumeId}/versions`, data)
};

// 章节分组管理相关API
export const chapterGroupAPI = {
  // 获取所有章节分组
  getAllChapterGroups: (volumeId) => api.get(`/volumes/${volumeId}/chapter-groups`),
  // 获取单个章节分组
  getChapterGroupById: (id) => api.get(`/chapter-groups/${id}`),
  // 创建章节分组
  createChapterGroup: (data) => api.post('/chapter-groups', data),
  // 更新章节分组
  updateChapterGroup: (id, data) => api.put(`/chapter-groups/${id}`, data),
  // 删除章节分组
  deleteChapterGroup: (id) => api.delete(`/chapter-groups/${id}`),

  // 添加版本管理相关方法
  getChapterGroupVersions: (chapterGroupId) => api.get(`/chapter-groups/${chapterGroupId}/versions`),
  getChapterGroupVersion: (chapterGroupId, versionId) => api.get(`/chapter-groups/${chapterGroupId}/versions/${versionId}`),
  createChapterGroupVersion: (chapterGroupId, data) => api.post(`/chapter-groups/${chapterGroupId}/versions`, data)
};

// 线索管理相关API
export const clueAPI = {
  // 获取所有线索
  getAllClues: (projectId) => api.get(`/projects/${projectId}/clues`),
  // 获取单个线索
  getClue: (id) => api.get(`/clues/${id}`),
  // 创建线索
  createClue: (data) => api.post('/clues', data),
  // 更新线索
  updateClue: (id, data) => api.put(`/clues/${id}`, data),
  // 删除线索
  deleteClue: (id) => api.delete(`/clues/${id}`)
};

// 认证相关API
export const authAPI = {
  // 用户注册
  register: (data) => api.post('/auth/register', data),
  // 用户登录
  login: (data) => api.post('/auth/login', data),
  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),
  // 修改密码
  changePassword: (data) => api.post('/auth/change-password', data)
};

// 用户管理相关API（仅admin可访问）
export const userAPI = {
  // 获取所有用户
  getAllUsers: () => api.get('/users'),
  // 获取单个用户
  getUserById: (id) => api.get(`/users/${id}`),
  // 创建用户
  createUser: (data) => api.post('/users', data),
  // 更新用户
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  // 删除用户
  deleteUser: (id) => api.delete(`/users/${id}`)
};

// AI配置相关API
export const aiConfigAPI = {
  // 获取用户的AI配置列表
  getUserConfigs: () => api.get('/ai-configs'),
  // 创建AI配置
  createConfig: (data) => api.post('/ai-configs', data),
  // 更新AI配置
  updateConfig: (id, data) => api.put(`/ai-configs/${id}`, data),
  // 删除AI配置
  deleteConfig: (id) => api.delete(`/ai-configs/${id}`),
  // 设置默认配置
  setDefaultConfig: (id) => api.post(`/ai-configs/${id}/set-default`),
  // 测试配置连接
  testConfig: (id) => api.post(`/ai-configs/${id}/test`)
};

// AI生成相关API（使用长超时实例）
export const aiGenerationAPI = {
  // 获取可用的AI配置（使用普通超时）
  getAvailableConfigs: () => api.get('/ai-generation/configs'),
  // 生成内容（使用长超时，10分钟）
  generateContent: (data) => aiApi.post('/ai-generation/generate', data),
  // 流式生成内容
  generateContentStream: (data, onChunk, onComplete, onError) => {
    return new Promise((resolve, reject) => {
      // 使用fetch + ReadableStream处理SSE
      fetch('/api/ai-generation/generate-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(data)
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        const readStream = () => {
          reader.read().then(({ done, value }) => {
            if (done) {
              if (onComplete) onComplete();
              resolve();
              return;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop(); // 保留不完整的行

            for (const line of lines) {
              if (line.trim() === '') continue;
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));

                  if (data.type === 'chunk' && onChunk) {
                    onChunk(data.content, data.fullContent);
                  } else if (data.type === 'complete' && onComplete) {
                    onComplete(data.content);
                  } else if (data.type === 'error' && onError) {
                    onError(new Error(data.message));
                  } else if (data.type === 'start') {
                    // 处理开始消息
                    console.log('AI生成开始:', data.message);
                  }
                } catch (parseError) {
                  console.warn('解析SSE数据失败:', parseError);
                }
              }
            }

            readStream();
          }).catch(error => {
            if (onError) onError(error);
            reject(error);
          });
        };

        readStream();
      }).catch(error => {
        if (onError) onError(error);
        reject(error);
      });
    });
  },
  // 生成世界观设定（使用长超时，10分钟）
  generateWorldSetting: (data) => aiApi.post('/ai-generation/world-setting', data)
};

export default api;
