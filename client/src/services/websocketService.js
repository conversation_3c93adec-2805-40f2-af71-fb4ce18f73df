/**
 * 前端WebSocket服务
 * 用于接收实时的流程执行状态更新
 */

import { useAuthStore } from '@/store/auth'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.listeners = new Map()
    this.isConnecting = false
    this.isConnected = false
    this.subscriptions = new Set()
  }

  // 连接WebSocket
  connect() {
    if (this.isConnecting || this.isConnected) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        const authStore = useAuthStore()
        const token = authStore.token
        
        if (!token) {
          reject(new Error('未找到认证令牌'))
          return
        }

        this.isConnecting = true
        const wsUrl = `${this.getWebSocketUrl()}?token=${encodeURIComponent(token)}`
        
        console.log('正在连接WebSocket服务器...')
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立')
          this.isConnecting = false
          this.isConnected = true
          this.reconnectAttempts = 0
          
          // 重新订阅之前的订阅
          this.resubscribe()
          
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason)
          this.isConnecting = false
          this.isConnected = false
          
          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.isConnecting = false
          this.isConnected = false
          reject(error)
        }

      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, '主动断开连接')
      this.ws = null
    }
    this.isConnected = false
    this.isConnecting = false
    this.subscriptions.clear()
  }

  // 处理接收到的消息
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data)
      
      // 触发对应类型的监听器
      if (this.listeners.has(message.type)) {
        const callbacks = this.listeners.get(message.type)
        callbacks.forEach(callback => {
          try {
            callback(message)
          } catch (error) {
            console.error('WebSocket消息处理回调错误:', error)
          }
        })
      }

      // 特殊处理执行更新消息
      if (message.type === 'execution_update') {
        this.handleExecutionUpdate(message)
      }

    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  // 处理执行更新消息
  handleExecutionUpdate(message) {
    const { executionId, data } = message
    
    // 触发执行更新监听器
    const updateListeners = this.listeners.get(`execution_update_${executionId}`)
    if (updateListeners) {
      updateListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('执行更新回调错误:', error)
        }
      })
    }
  }

  // 发送消息
  send(message) {
    if (this.isConnected && this.ws) {
      try {
        this.ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
        return false
      }
    }
    return false
  }

  // 订阅执行状态更新
  subscribeExecution(executionId) {
    this.subscriptions.add(executionId)
    
    if (this.isConnected) {
      this.send({
        type: 'subscribe_execution',
        executionId
      })
    }
  }

  // 取消订阅执行状态更新
  unsubscribeExecution(executionId) {
    this.subscriptions.delete(executionId)
    
    if (this.isConnected) {
      this.send({
        type: 'unsubscribe_execution',
        executionId
      })
    }
  }

  // 重新订阅所有订阅
  resubscribe() {
    this.subscriptions.forEach(executionId => {
      this.send({
        type: 'subscribe_execution',
        executionId
      })
    })
  }

  // 添加消息监听器
  on(type, callback) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type).push(callback)
  }

  // 移除消息监听器
  off(type, callback) {
    if (this.listeners.has(type)) {
      const callbacks = this.listeners.get(type)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 添加执行更新监听器
  onExecutionUpdate(executionId, callback) {
    this.on(`execution_update_${executionId}`, callback)
  }

  // 移除执行更新监听器
  offExecutionUpdate(executionId, callback) {
    this.off(`execution_update_${executionId}`, callback)
  }

  // 计划重连
  scheduleReconnect() {
    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`${delay / 1000}秒后尝试第${this.reconnectAttempts}次重连...`)
    
    setTimeout(() => {
      if (!this.isConnected && !this.isConnecting) {
        this.connect().catch(error => {
          console.error('重连失败:', error)
        })
      }
    }, delay)
  }

  // 获取WebSocket URL
  getWebSocketUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = import.meta.env.VITE_WS_HOST || window.location.hostname
    const port = import.meta.env.VITE_WS_PORT || '5001'
    return `${protocol}//${host}:${port}/ws`
  }

  // 发送心跳
  ping() {
    this.send({ type: 'ping' })
  }

  // 启动心跳
  startHeartbeat() {
    setInterval(() => {
      if (this.isConnected) {
        this.ping()
      }
    }, 30000) // 每30秒发送一次心跳
  }

  // 获取连接状态
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions)
    }
  }
}

// 创建单例实例
const websocketService = new WebSocketService()

export default websocketService
