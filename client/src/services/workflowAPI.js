/**
 * 工作流API服务
 * 处理创作流程配置相关的API调用
 */

import api from './api'

export const workflowAPI = {
  // 流程模板管理
  async getTemplates(params = {}) {
    const response = await api.get('/workflows/templates', { params })
    return response
  },

  async getTemplateById(id) {
    const response = await api.get(`/workflows/templates/${id}`)
    return response
  },

  async createTemplate(data) {
    const response = await api.post('/workflows/templates', data)
    return response
  },

  async updateTemplate(id, data) {
    const response = await api.put(`/workflows/templates/${id}`, data)
    return response
  },

  async deleteTemplate(id) {
    const response = await api.delete(`/workflows/templates/${id}`)
    return response
  },

  // 流程分类管理
  async getCategories() {
    const response = await api.get('/workflows/categories')
    return response
  },

  // 流程执行
  async executeWorkflow(id, data) {
    const response = await api.post(`/workflows/templates/${id}/execute`, data)
    return response
  },

  async getExecutionStatus(executionId) {
    const response = await api.get(`/workflows/executions/${executionId}`)
    return response
  },

  async continueExecution(executionId, data = {}) {
    const response = await api.post(`/workflows/executions/${executionId}/continue`, data)
    return response
  },

  async pauseExecution(executionId) {
    const response = await api.post(`/workflows/executions/${executionId}/pause`)
    return response
  },

  async cancelExecution(executionId) {
    const response = await api.post(`/workflows/executions/${executionId}/cancel`)
    return response
  },

  // 获取执行历史
  async getExecutionHistory(params = {}) {
    const response = await api.get('/workflows/executions', { params })
    return response
  },

  // 重新执行流程
  async reExecuteWorkflow(executionId) {
    const response = await api.post(`/workflows/executions/${executionId}/re-execute`)
    return response
  },

  // 版本管理
  async getVersions(templateId) {
    const response = await api.get(`/workflows/templates/${templateId}/versions`)
    return response
  },

  async createVersion(templateId, data) {
    const response = await api.post(`/workflows/templates/${templateId}/versions`, data)
    return response
  },

  async restoreVersion(templateId, versionId) {
    const response = await api.post(`/workflows/templates/${templateId}/versions/${versionId}/restore`)
    return response
  },

  // 导入导出
  async exportTemplate(templateId) {
    const response = await api.get(`/workflows/templates/${templateId}/export`)
    return response
  },

  async importTemplate(data) {
    const response = await api.post('/workflows/templates/import', data)
    return response
  },

  // 统计分析
  async getStatistics(params = {}) {
    const response = await api.get('/workflows/statistics', { params })
    return response
  },

  // 流程版本管理
  async getVersions(workflowId) {
    const response = await api.get(`/workflows/templates/${workflowId}/versions`)
    return response
  },

  async createVersion(workflowId, data) {
    const response = await api.post(`/workflows/templates/${workflowId}/versions`, data)
    return response
  },

  async restoreVersion(workflowId, versionId) {
    const response = await api.post(`/workflows/templates/${workflowId}/versions/${versionId}/restore`)
    return response
  },

  // 流程导入导出
  async exportWorkflow(id) {
    const response = await api.get(`/workflows/templates/${id}/export`)
    return response
  },

  async importWorkflow(data) {
    const response = await api.post('/workflows/templates/import', data)
    return response
  },

  // 统计分析
  async getStatistics(params = {}) {
    const response = await api.get('/workflows/statistics', { params })
    return response
  }
}

export default workflowAPI
