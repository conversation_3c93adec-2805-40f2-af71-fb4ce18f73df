import api from './api';

// 认证服务
const authService = {
  // 用户注册
  register: async (userData) => {
    try {
      const response = await api.post('/auth/register', userData);
      if (response.token) {
        // 保存令牌到本地存储
        localStorage.setItem('authToken', response.token);
        // 保存用户信息到本地存储
        localStorage.setItem('user', JSON.stringify(response.user));
      }
      return response;
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  },

  // 用户登录
  login: async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials);
      if (response.token) {
        // 保存令牌到本地存储
        localStorage.setItem('authToken', response.token);
        // 保存用户信息到本地存储
        localStorage.setItem('user', JSON.stringify(response.user));
      }
      return response;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  // 用户登出
  logout: () => {
    // 清除本地存储中的认证信息
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  },

  // 获取当前用户信息
  getCurrentUser: async () => {
    try {
      const response = await api.get('/auth/me');
      return response;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  // 修改密码
  changePassword: async (passwordData) => {
    try {
      const response = await api.post('/auth/change-password', passwordData);
      return response;
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  },

  // 检查用户是否已登录
  isAuthenticated: () => {
    return !!localStorage.getItem('authToken');
  },

  // 获取本地存储的用户信息
  getUser: () => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // 获取认证令牌
  getToken: () => {
    return localStorage.getItem('authToken');
  }
};

export default authService;
