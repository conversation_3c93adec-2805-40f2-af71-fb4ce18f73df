import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authService from '@/services/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => {
    return !!token.value || authService.isAuthenticated()
  })

  // 初始化认证状态
  const initAuth = () => {
    const savedToken = authService.getToken()
    const savedUser = authService.getUser()
    
    if (savedToken) {
      token.value = savedToken
    }
    if (savedUser) {
      user.value = savedUser
    }
  }

  // 登录
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await authService.login(credentials)
      
      if (response.token) {
        token.value = response.token
        user.value = response.user
      }
      
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      isLoading.value = true
      const response = await authService.register(userData)
      
      if (response.token) {
        token.value = response.token
        user.value = response.user
      }
      
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    authService.logout()
    token.value = null
    user.value = null
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      isLoading.value = true
      const response = await authService.getCurrentUser()
      user.value = response
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    try {
      isLoading.value = true
      const response = await authService.changePassword(passwordData)
      return response
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 初始化
  initAuth()

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    login,
    register,
    logout,
    getCurrentUser,
    changePassword,
    initAuth
  }
})
