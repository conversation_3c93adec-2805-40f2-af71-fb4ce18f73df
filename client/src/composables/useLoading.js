/**
 * 全局加载状态管理
 */
import { ref, reactive } from 'vue'

// 全局加载状态
const globalLoading = ref(false)
const loadingText = ref('加载中...')

// 页面级加载状态管理
const pageLoadingStates = reactive({})

// 操作级加载状态管理
const operationLoadingStates = reactive({})

/**
 * 全局加载状态管理
 */
export function useGlobalLoading() {
  const setGlobalLoading = (loading, text = '加载中...') => {
    globalLoading.value = loading
    loadingText.value = text
  }

  const showGlobalLoading = (text = '加载中...') => {
    setGlobalLoading(true, text)
  }

  const hideGlobalLoading = () => {
    setGlobalLoading(false)
  }

  return {
    globalLoading,
    loadingText,
    setGlobalLoading,
    showGlobalLoading,
    hideGlobalLoading
  }
}

/**
 * 页面级加载状态管理
 */
export function usePageLoading(pageKey) {
  if (!pageLoadingStates[pageKey]) {
    pageLoadingStates[pageKey] = {
      loading: false,
      text: '加载中...',
      error: null
    }
  }

  const setPageLoading = (loading, text = '加载中...', error = null) => {
    pageLoadingStates[pageKey].loading = loading
    pageLoadingStates[pageKey].text = text
    pageLoadingStates[pageKey].error = error
  }

  const showPageLoading = (text = '加载中...') => {
    setPageLoading(true, text)
  }

  const hidePageLoading = () => {
    setPageLoading(false)
  }

  const setPageError = (error) => {
    setPageLoading(false, '', error)
  }

  return {
    pageState: pageLoadingStates[pageKey],
    setPageLoading,
    showPageLoading,
    hidePageLoading,
    setPageError
  }
}

/**
 * 操作级加载状态管理
 */
export function useOperationLoading() {
  const createOperationState = (operationKey) => {
    if (!operationLoadingStates[operationKey]) {
      operationLoadingStates[operationKey] = {
        loading: false,
        text: '处理中...',
        error: null
      }
    }
    return operationLoadingStates[operationKey]
  }

  const setOperationLoading = (operationKey, loading, text = '处理中...', error = null) => {
    const state = createOperationState(operationKey)
    state.loading = loading
    state.text = text
    state.error = error
  }

  const showOperationLoading = (operationKey, text = '处理中...') => {
    setOperationLoading(operationKey, true, text)
  }

  const hideOperationLoading = (operationKey) => {
    setOperationLoading(operationKey, false)
  }

  const setOperationError = (operationKey, error) => {
    setOperationLoading(operationKey, false, '', error)
  }

  const getOperationState = (operationKey) => {
    return createOperationState(operationKey)
  }

  return {
    operationStates: operationLoadingStates,
    setOperationLoading,
    showOperationLoading,
    hideOperationLoading,
    setOperationError,
    getOperationState
  }
}

/**
 * API调用包装器，自动管理加载状态
 */
export function useApiCall() {
  /**
   * 包装API调用，自动管理全局加载状态
   */
  const withGlobalLoading = async (apiCall, loadingText = '加载中...') => {
    const { showGlobalLoading, hideGlobalLoading } = useGlobalLoading()
    
    try {
      showGlobalLoading(loadingText)
      const result = await apiCall()
      return result
    } finally {
      hideGlobalLoading()
    }
  }

  /**
   * 包装API调用，自动管理页面加载状态
   */
  const withPageLoading = async (pageKey, apiCall, loadingText = '加载中...') => {
    const { showPageLoading, hidePageLoading, setPageError } = usePageLoading(pageKey)
    
    try {
      showPageLoading(loadingText)
      const result = await apiCall()
      hidePageLoading()
      return result
    } catch (error) {
      setPageError(error)
      throw error
    }
  }

  /**
   * 包装API调用，自动管理操作加载状态
   */
  const withOperationLoading = async (operationKey, apiCall, loadingText = '处理中...') => {
    const { showOperationLoading, hideOperationLoading, setOperationError } = useOperationLoading()
    
    try {
      showOperationLoading(operationKey, loadingText)
      const result = await apiCall()
      hideOperationLoading(operationKey)
      return result
    } catch (error) {
      setOperationError(operationKey, error)
      throw error
    }
  }

  return {
    withGlobalLoading,
    withPageLoading,
    withOperationLoading
  }
}

/**
 * 批量操作加载状态管理
 */
export function useBatchLoading() {
  const batchStates = reactive({})

  const createBatchState = (batchKey, total = 0) => {
    batchStates[batchKey] = {
      loading: false,
      current: 0,
      total: total,
      text: '处理中...',
      error: null,
      progress: 0
    }
    return batchStates[batchKey]
  }

  const updateBatchProgress = (batchKey, current, text = null) => {
    const state = batchStates[batchKey]
    if (state) {
      state.current = current
      state.progress = state.total > 0 ? Math.round((current / state.total) * 100) : 0
      if (text) {
        state.text = text
      }
    }
  }

  const startBatch = (batchKey, total, text = '处理中...') => {
    const state = createBatchState(batchKey, total)
    state.loading = true
    state.text = text
    state.current = 0
    state.error = null
  }

  const finishBatch = (batchKey) => {
    const state = batchStates[batchKey]
    if (state) {
      state.loading = false
      state.current = state.total
      state.progress = 100
    }
  }

  const setBatchError = (batchKey, error) => {
    const state = batchStates[batchKey]
    if (state) {
      state.loading = false
      state.error = error
    }
  }

  return {
    batchStates,
    createBatchState,
    updateBatchProgress,
    startBatch,
    finishBatch,
    setBatchError
  }
}
