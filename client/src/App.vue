<template>
  <div class="app-container">
    <el-config-provider>
      <el-container class="layout-container">
        <!-- 侧边栏 -->
        <el-aside width="240px" class="aside">
          <div class="logo">
            <div class="logo-icon">📚</div>
            <h2>墨韵创作</h2>
            <div class="logo-subtitle">AI小说助手</div>
          </div>
          <el-menu
            :router="true"
            :default-active="$route.path"
            class="menu"
          >
            <el-menu-item index="/">
              <span>📖 书斋首页</span>
            </el-menu-item>

            <el-menu-item index="/projects">
              <span>📚 项目管理</span>
            </el-menu-item>

            <el-sub-menu index="project-items" v-if="$route.params.projectId">
              <template #title>
                <span>✍️ 创作内容</span>
              </template>

              <el-menu-item index="/world-settings">
                <span>🌍 世界观设定</span>
              </el-menu-item>

              <el-menu-item index="/characters">
                <span>👥 角色管理</span>
              </el-menu-item>

              <el-menu-item index="/outlines">
                <span>📋 大纲管理</span>
              </el-menu-item>

              <el-menu-item index="/chapters">
                <span>📄 章节管理</span>
              </el-menu-item>

              <el-menu-item index="/volumes">
                <span>📖 分卷剧情</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item index="/prompt-generator">
              <span>✨ 提示词生成</span>
            </el-menu-item>

            <el-menu-item index="/workflow-templates">
              <span>🎭 创作流程</span>
            </el-menu-item>

            <el-menu-item index="/ai-config">
              <span>⚙️ 大模型配置</span>
            </el-menu-item>

            <!-- 用户管理菜单 - 仅admin可见 -->
            <el-menu-item index="/user-management" v-if="isAdmin">
              <el-icon><UserFilled /></el-icon>
              <span>用户管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主要内容区域 -->
        <el-container>
          <el-header class="header">
            <div class="breadcrumb">
              <el-breadcrumb>
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item v-if="$route.meta.title">{{ $route.meta.title }}</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="header-right">
              <template v-if="isAuthenticated">
                <span class="username">{{ username }}</span>
                <el-button type="danger" size="small" @click="handleLogout">退出登录</el-button>
              </template>
              <template v-else>
                <el-button type="primary" size="small" @click="$router.push('/login')">登录</el-button>
              </template>
            </div>
          </el-header>

          <el-main class="main">
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </el-config-provider>

    <!-- 全局加载蒙层 -->
    <GlobalLoading />
  </div>
</template>

<script setup>
import {
  HomeFilled,
  Document,
  Edit,
  Setting,
  User,
  Notebook,
  Reading,
  Star,
  Files,
  UserFilled,
  Connection
} from '@element-plus/icons-vue';
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import authService from './services/auth';
import GlobalLoading from './components/GlobalLoading.vue';

const router = useRouter();
const isAuthenticated = ref(false);
const username = ref('');

// 检查是否为admin用户
const isAdmin = computed(() => {
  if (!isAuthenticated.value) return false;
  const user = authService.getUser();
  return user && user.role === 'admin';
});

// 检查用户是否已登录
// 在组件挂载时检查登录状态
onMounted(() => {
  checkAuthStatus();
});

// 检查登录状态
const checkAuthStatus = () => {
  isAuthenticated.value = authService.isAuthenticated();
  if (isAuthenticated.value) {
    const user = authService.getUser();
    if (user) {
      username.value = user.username;
    }
  }
};

// 处理登出
const handleLogout = () => {
  authService.logout();
  isAuthenticated.value = false;
  username.value = '';
  ElMessage.success('已成功退出登录');
  router.push('/login');
};
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Source Han Sans CN', sans-serif;
  background: linear-gradient(135deg, #f5f1eb 0%, #ede4d3 100%);
}

.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f1eb 0%, #ede4d3 100%);
}

.layout-container {
  min-height: 100vh;
}

/* 侧边栏古典风格 */
.aside {
  background: linear-gradient(180deg, #2c1810 0%, #4a2c1a 50%, #2c1810 100%);
  color: #f5deb3;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
  position: relative;
}

.aside::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Logo区域样式 */
.logo {
  height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-bottom: 2px solid #daa520;
  background: linear-gradient(45deg, #3d2317, #5a3520);
  position: relative;
  z-index: 2;
}

.logo-icon {
  font-size: 1.8rem;
  margin-bottom: 5px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.logo h2 {
  color: #daa520;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.logo-subtitle {
  color: #cd853f;
  font-size: 0.7rem;
  margin-top: 2px;
  opacity: 0.8;
  letter-spacing: 1px;
}

.menu {
  border-right: none;
  background: transparent !important;
  position: relative;
  z-index: 2;
}

/* 头部样式 */
.header {
  background: linear-gradient(90deg, #faf7f0 0%, #f0ead6 100%);
  border-bottom: 2px solid #daa520;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  box-shadow: 0 2px 10px rgba(218, 165, 32, 0.2);
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(90deg, transparent 0%, rgba(218, 165, 32, 0.05) 50%, transparent 100%);
  pointer-events: none;
}

/* 主内容区域 */
.main {
  background: linear-gradient(135deg, #f5f1eb 0%, #ede4d3 50%, #e8dcc0 100%);
  padding: 20px;
  min-height: calc(100vh - 80px);
  position: relative;
}

.main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(218, 165, 32, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(139, 69, 19, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* 头部右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 2;
}

.username {
  font-weight: 600;
  margin-right: 10px;
  color: #8b4513;
  font-size: 1rem;
  letter-spacing: 1px;
  padding: 8px 16px;
  background: linear-gradient(145deg, #f5deb3, #daa520);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(218, 165, 32, 0.3);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* 面包屑导航样式 */
.breadcrumb {
  position: relative;
  z-index: 2;
}

.el-breadcrumb {
  font-weight: 500;
}

.el-breadcrumb__item {
  color: #8b4513 !important;
}

.el-breadcrumb__item a {
  color: #a0522d !important;
  text-decoration: none;
  transition: color 0.3s ease;
}

.el-breadcrumb__item a:hover {
  color: #daa520 !important;
}

/* 古典菜单样式覆盖 */
.el-menu {
  background: transparent !important;
  border-right: none !important;
}

.el-menu-item {
  color: #f5deb3 !important;
  margin: 2px 8px;
  padding: 12px 16px !important;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
}

.el-menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.2), transparent);
  transition: left 0.5s;
}

.el-menu-item:hover::before {
  left: 100%;
}

.el-menu-item.is-active {
  color: #daa520 !important;
  background: linear-gradient(145deg, rgba(218, 165, 32, 0.2), rgba(205, 133, 63, 0.1)) !important;
  box-shadow: inset 0 2px 4px rgba(218, 165, 32, 0.3);
  border-left: 3px solid #daa520;
}

.el-menu-item:hover {
  background: linear-gradient(145deg, rgba(218, 165, 32, 0.15), rgba(205, 133, 63, 0.05)) !important;
  color: #daa520 !important;
  transform: translateX(5px);
}

.el-sub-menu__title {
  color: #f5deb3 !important;
  margin: 2px 8px;
  padding: 12px 16px !important;
  border-radius: 8px;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-size: 14px;
}

.el-sub-menu__title:hover {
  background: linear-gradient(145deg, rgba(218, 165, 32, 0.15), rgba(205, 133, 63, 0.05)) !important;
  color: #daa520 !important;
  transform: translateX(5px);
}

.el-sub-menu .el-menu-item {
  margin: 2px 12px;
  font-size: 13px;
  padding: 10px 20px !important;
  white-space: nowrap;
}

/* 按钮样式优化 */
.el-button {
  border-radius: 20px;
  font-weight: 500;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-button--primary {
  background: linear-gradient(145deg, #daa520, #cd853f);
  border: none;
  color: #fff;
}

.el-button--primary:hover {
  background: linear-gradient(145deg, #cd853f, #daa520);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(218, 165, 32, 0.4);
}

.el-button--danger {
  background: linear-gradient(145deg, #d2691e, #a0522d);
  border: none;
  color: #fff;
}

.el-button--danger:hover {
  background: linear-gradient(145deg, #a0522d, #d2691e);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(210, 105, 30, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .aside {
    width: 200px !important;
  }

  .logo h2 {
    font-size: 1.2rem;
  }

  .logo-subtitle {
    font-size: 0.6rem;
  }

  .header {
    padding: 0 15px;
  }

  .username {
    font-size: 0.9rem;
    padding: 6px 12px;
  }
}
</style>