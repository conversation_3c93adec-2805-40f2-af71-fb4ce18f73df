<template>
  <div class="typewriter-container">
    <div 
      class="typewriter-content" 
      :class="{ 'typing': isTyping }"
      v-html="displayedContent"
    ></div>
    <div v-if="isTyping" class="typing-cursor">|</div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  speed: {
    type: Number,
    default: 30 // 每秒显示的字符数
  },
  autoStart: {
    type: Boolean,
    default: true
  },
  preserveFormatting: {
    type: Boolean,
    default: true // 是否保持格式（换行等）
  }
})

// Emits
const emit = defineEmits(['typing-start', 'typing-complete', 'typing-progress'])

// 响应式数据
const displayedContent = ref('')
const isTyping = ref(false)
const currentIndex = ref(0)
const typingTimer = ref(null)

// 开始打字效果
const startTyping = () => {
  if (isTyping.value) return
  
  isTyping.value = true
  currentIndex.value = 0
  displayedContent.value = ''
  
  emit('typing-start')
  
  typeNextCharacter()
}

// 停止打字效果
const stopTyping = () => {
  if (typingTimer.value) {
    clearTimeout(typingTimer.value)
    typingTimer.value = null
  }
  isTyping.value = false
}

// 完成打字效果（立即显示全部内容）
const completeTyping = () => {
  stopTyping()
  displayedContent.value = formatContent(props.content)
  currentIndex.value = props.content.length
  emit('typing-complete')
}

// 重置打字效果
const resetTyping = () => {
  stopTyping()
  displayedContent.value = ''
  currentIndex.value = 0
}

// 打字下一个字符
const typeNextCharacter = () => {
  if (currentIndex.value >= props.content.length) {
    isTyping.value = false
    emit('typing-complete')
    return
  }

  const char = props.content[currentIndex.value]
  const currentContent = props.content.substring(0, currentIndex.value + 1)
  
  displayedContent.value = formatContent(currentContent)
  currentIndex.value++
  
  // 发送进度事件
  emit('typing-progress', {
    current: currentIndex.value,
    total: props.content.length,
    progress: (currentIndex.value / props.content.length) * 100
  })
  
  // 计算下一个字符的延迟时间
  let delay = 1000 / props.speed
  
  // 如果是标点符号，稍微延长停顿时间
  if (/[。！？，；：]/.test(char)) {
    delay *= 2
  }
  // 如果是换行符，稍微延长停顿时间
  else if (char === '\n') {
    delay *= 1.5
  }
  
  typingTimer.value = setTimeout(typeNextCharacter, delay)
}

// 格式化内容（处理换行等）
const formatContent = (content) => {
  if (!props.preserveFormatting) {
    return content
  }
  
  // 将换行符转换为HTML换行
  return content.replace(/\n/g, '<br>')
}

// 追加新内容（用于流式输出）
const appendContent = (newContent) => {
  if (!isTyping.value) {
    // 如果没有在打字，直接开始新的打字效果
    startTyping()
  }
  // 如果正在打字，内容会通过props.content的变化自动更新
}

// 监听content变化
watch(() => props.content, (newContent, oldContent) => {
  if (newContent !== oldContent) {
    if (props.autoStart && !isTyping.value) {
      nextTick(() => {
        startTyping()
      })
    }
  }
})

// 监听content长度变化（用于流式输出）
watch(() => props.content.length, (newLength, oldLength) => {
  if (newLength > oldLength && isTyping.value) {
    // 内容增加了，继续打字效果
    // 不需要特殊处理，typeNextCharacter会自动处理新增的内容
  }
})

// 暴露方法给父组件
defineExpose({
  startTyping,
  stopTyping,
  completeTyping,
  resetTyping,
  appendContent,
  isTyping: () => isTyping.value
})

// 组件挂载时自动开始
if (props.autoStart && props.content) {
  nextTick(() => {
    startTyping()
  })
}
</script>

<style scoped>
.typewriter-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.typewriter-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.6;
  font-family: inherit;
}

.typewriter-content.typing {
  /* 可以添加打字时的特殊样式 */
}

.typing-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  font-weight: bold;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .typing-cursor {
    color: #fff;
  }
}
</style>
