<template>
  <!-- 全局加载蒙层 -->
  <el-loading-service
    v-if="globalLoading"
    :text="loadingText"
    background="rgba(0, 0, 0, 0.7)"
    element-loading-spinner="el-icon-loading"
    element-loading-svg-view-box="-10, -10, 50, 50"
  />
  
  <!-- 页面级加载蒙层 -->
  <div
    v-if="globalLoading"
    class="global-loading-overlay"
    :class="{ 'global-loading-visible': globalLoading }"
  >
    <div class="global-loading-content">
      <div class="loading-spinner">
        <el-icon class="is-loading" size="40">
          <Loading />
        </el-icon>
      </div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script setup>
import { Loading } from '@element-plus/icons-vue'
import { useGlobalLoading } from '../composables/useLoading'

const { globalLoading, loadingText } = useGlobalLoading()
</script>

<style scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.global-loading-visible {
  opacity: 1;
  visibility: visible;
}

.global-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-spinner {
  color: #409eff;
}

.loading-text {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
  text-align: center;
  min-width: 120px;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
