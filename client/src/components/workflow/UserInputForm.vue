<template>
  <div class="user-input-form">
    <div class="form-header">
      <h3>{{ node.name || '用户输入' }}</h3>
      <p v-if="node.description" class="form-description">{{ node.description }}</p>
    </div>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <div 
        v-for="field in inputFields"
        :key="field.name"
        class="input-field"
      >
        <el-form-item 
          :label="field.label"
          :prop="field.name"
          :required="field.required"
        >
          <!-- 文本输入 -->
          <el-input
            v-if="field.type === 'text'"
            v-model="form[field.name]"
            :placeholder="`请输入${field.label}`"
            clearable
          />
          
          <!-- 多行文本输入 -->
          <el-input
            v-else-if="field.type === 'textarea'"
            v-model="form[field.name]"
            type="textarea"
            :rows="field.rows || 4"
            :placeholder="`请输入${field.label}`"
            show-word-limit
            :maxlength="field.maxLength || 1000"
          />
          
          <!-- 数字输入 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="form[field.name]"
            :min="field.min"
            :max="field.max"
            :step="field.step || 1"
            :placeholder="`请输入${field.label}`"
            style="width: 100%"
          />
          
          <!-- 选择框 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="form[field.name]"
            :placeholder="`请选择${field.label}`"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="option in field.options || []"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          
          <!-- 单选框 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="form[field.name]"
          >
            <el-radio
              v-for="option in field.options || []"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          
          <!-- 复选框 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="form[field.name]"
          >
            <el-checkbox
              v-for="option in field.options || []"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <!-- 日期选择 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="form[field.name]"
            type="date"
            :placeholder="`请选择${field.label}`"
            style="width: 100%"
          />
          
          <!-- 文件上传 -->
          <el-upload
            v-else-if="field.type === 'file'"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="(response, file) => handleFileSuccess(response, file, field.name)"
            :on-error="handleFileError"
            :before-upload="beforeFileUpload"
            :file-list="getFileList(field.name)"
            :limit="field.limit || 1"
            :accept="field.accept"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip" v-if="field.tip">
                {{ field.tip }}
              </div>
            </template>
          </el-upload>
          
          <!-- 默认文本输入 -->
          <el-input
            v-else
            v-model="form[field.name]"
            :placeholder="`请输入${field.label}`"
            clearable
          />
          
          <!-- 字段帮助文本 -->
          <div v-if="field.help" class="field-help">
            {{ field.help }}
          </div>
        </el-form-item>
      </div>

      <!-- 上下文数据显示 -->
      <el-divider content-position="left">上下文数据</el-divider>
      
      <div class="context-data">
        <el-collapse v-model="activeContexts">
          <el-collapse-item 
            v-for="(value, key) in filteredExecutionData"
            :key="key"
            :title="`${key} (${getDataType(value)})`"
            :name="key"
          >
            <div class="context-value">
              <pre>{{ formatValue(value) }}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
        
        <div v-if="Object.keys(filteredExecutionData).length === 0" class="no-context">
          <el-empty description="暂无上下文数据" />
        </div>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        提交
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'

const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  executionData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['submit', 'cancel'])

const authStore = useAuthStore()
const formRef = ref()
const submitting = ref(false)
const activeContexts = ref([])

// 表单数据
const form = reactive({})
const rules = reactive({})

// 计算属性
const inputFields = computed(() => {
  return props.node.config?.inputFields || []
})

const filteredExecutionData = computed(() => {
  // 过滤掉系统内部字段
  const filtered = {}
  for (const [key, value] of Object.entries(props.executionData)) {
    if (!key.startsWith('_') && key !== 'nodeExecutions') {
      filtered[key] = value
    }
  }
  return filtered
})

const uploadUrl = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL || '/api'}/upload`
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${authStore.token}`
  }
})

// 方法
const initializeForm = () => {
  // 初始化表单数据和验证规则
  inputFields.value.forEach(field => {
    // 设置默认值
    if (field.type === 'checkbox') {
      form[field.name] = field.defaultValue || []
    } else if (field.type === 'number') {
      form[field.name] = field.defaultValue || 0
    } else {
      form[field.name] = field.defaultValue || ''
    }
    
    // 设置验证规则
    if (field.required) {
      rules[field.name] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
    
    // 添加自定义验证规则
    if (field.validation) {
      if (!rules[field.name]) rules[field.name] = []
      
      if (field.validation.minLength) {
        rules[field.name].push({
          min: field.validation.minLength,
          message: `${field.label}至少需要${field.validation.minLength}个字符`,
          trigger: 'blur'
        })
      }
      
      if (field.validation.maxLength) {
        rules[field.name].push({
          max: field.validation.maxLength,
          message: `${field.label}不能超过${field.validation.maxLength}个字符`,
          trigger: 'blur'
        })
      }
      
      if (field.validation.pattern) {
        rules[field.name].push({
          pattern: new RegExp(field.validation.pattern),
          message: field.validation.message || `${field.label}格式不正确`,
          trigger: 'blur'
        })
      }
    }
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 准备提交数据
    const submitData = { ...form }
    
    // 处理文件字段
    inputFields.value.forEach(field => {
      if (field.type === 'file' && submitData[field.name]) {
        // 如果是文件，只提交文件URL或ID
        if (Array.isArray(submitData[field.name])) {
          submitData[field.name] = submitData[field.name].map(file => file.url || file.response?.url)
        }
      }
    })
    
    emit('submit', submitData)
  } catch (error) {
    if (error.errors) return // 表单验证错误
    ElMessage.error('提交失败')
    console.error(error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleFileSuccess = (response, file, fieldName) => {
  if (!form[fieldName]) {
    form[fieldName] = []
  }
  form[fieldName].push({
    name: file.name,
    url: response.url,
    response
  })
  ElMessage.success('文件上传成功')
}

const handleFileError = (error) => {
  ElMessage.error('文件上传失败')
  console.error(error)
}

const beforeFileUpload = (file) => {
  // 可以在这里添加文件上传前的验证
  return true
}

const getFileList = (fieldName) => {
  return form[fieldName] || []
}

const getDataType = (value) => {
  if (Array.isArray(value)) return 'Array'
  if (value === null) return 'null'
  return typeof value
}

const formatValue = (value) => {
  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2)
  }
  return String(value)
}

// 生命周期
onMounted(() => {
  initializeForm()
})
</script>

<style scoped>
.user-input-form {
  padding: 20px;
}

.form-header {
  margin-bottom: 24px;
}

.form-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.form-description {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.input-field {
  margin-bottom: 20px;
}

.field-help {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.context-data {
  margin: 16px 0;
  max-height: 300px;
  overflow-y: auto;
}

.context-value {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.context-value pre {
  margin: 0;
  font-size: 12px;
  color: #333;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-context {
  text-align: center;
  padding: 20px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

/* Element Plus 样式覆盖 */
:deep(.el-upload__tip) {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

:deep(.el-collapse-item__header) {
  font-size: 13px;
}

:deep(.el-radio) {
  margin-right: 16px;
  margin-bottom: 8px;
}

:deep(.el-checkbox) {
  margin-right: 16px;
  margin-bottom: 8px;
}
</style>
