<template>
  <div class="edge-property-form">
    <el-form :model="form" label-width="100px" @submit.prevent>
      <el-form-item label="连接名称">
        <el-input 
          v-model="form.label" 
          placeholder="请输入连接名称"
          :disabled="!isEditing"
          @input="updateProperty('label', $event)"
        />
      </el-form-item>
      
      <el-form-item label="连接描述">
        <el-input 
          v-model="form.description" 
          type="textarea"
          :rows="2"
          placeholder="请输入连接描述"
          :disabled="!isEditing"
          @input="updateProperty('description', $event)"
        />
      </el-form-item>

      <el-form-item label="连接类型">
        <el-select 
          v-model="form.type" 
          :disabled="!isEditing"
          @change="updateProperty('type', $event)"
        >
          <el-option label="默认连接" value="default" />
          <el-option label="条件连接" value="condition" />
          <el-option label="成功连接" value="success" />
          <el-option label="失败连接" value="error" />
        </el-select>
      </el-form-item>

      <!-- 条件连接配置 -->
      <template v-if="form.type === 'condition'">
        <el-divider content-position="left">条件配置</el-divider>
        
        <el-form-item label="条件表达式">
          <el-input 
            v-model="form.conditionConfig.expression" 
            placeholder="例如: result === 'success'"
            :disabled="!isEditing"
            @input="updateConditionConfig('expression', $event)"
          />
        </el-form-item>
        
        <el-form-item label="条件说明">
          <el-input 
            v-model="form.conditionConfig.description" 
            type="textarea"
            :rows="2"
            placeholder="请描述此条件的含义"
            :disabled="!isEditing"
            @input="updateConditionConfig('description', $event)"
          />
        </el-form-item>
      </template>

      <!-- 连接样式配置 -->
      <el-divider content-position="left">样式配置</el-divider>
      
      <el-form-item label="线条样式">
        <el-select 
          v-model="form.style.strokeDasharray" 
          :disabled="!isEditing"
          @change="updateStyle('strokeDasharray', $event)"
        >
          <el-option label="实线" value="" />
          <el-option label="虚线" value="5,5" />
          <el-option label="点线" value="2,2" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="线条颜色">
        <el-color-picker 
          v-model="form.style.stroke" 
          :disabled="!isEditing"
          @change="updateStyle('stroke', $event)"
        />
      </el-form-item>
      
      <el-form-item label="线条粗细">
        <el-slider 
          v-model="form.style.strokeWidth" 
          :min="1"
          :max="10"
          :disabled="!isEditing"
          @change="updateStyle('strokeWidth', $event)"
        />
      </el-form-item>

      <!-- 连接信息 -->
      <el-divider content-position="left">连接信息</el-divider>
      
      <el-form-item label="源节点">
        <el-input :value="edge.source" disabled />
      </el-form-item>
      
      <el-form-item label="目标节点">
        <el-input :value="edge.target" disabled />
      </el-form-item>
      
      <el-form-item label="源连接点">
        <el-input :value="edge.sourceHandle || '默认'" disabled />
      </el-form-item>
      
      <el-form-item label="目标连接点">
        <el-input :value="edge.targetHandle || '默认'" disabled />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  edge: {
    type: Object,
    required: true
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update'])

// 响应式数据
const form = reactive({
  label: '',
  description: '',
  type: 'default',
  conditionConfig: {
    expression: '',
    description: ''
  },
  style: {
    stroke: '#b1b1b7',
    strokeWidth: 2,
    strokeDasharray: ''
  }
})

// 监听边变化
watch(() => props.edge, (newEdge) => {
  if (newEdge) {
    const data = newEdge.data || {}
    form.label = data.label || ''
    form.description = data.description || ''
    form.type = data.type || 'default'
    form.conditionConfig = { ...data.conditionConfig } || { expression: '', description: '' }
    form.style = { ...data.style } || { stroke: '#b1b1b7', strokeWidth: 2, strokeDasharray: '' }
  }
}, { immediate: true, deep: true })

// 方法
const updateProperty = (key, value) => {
  const newData = { ...props.edge.data, [key]: value }
  emit('update', props.edge.id, newData)
}

const updateConditionConfig = (key, value) => {
  const newConditionConfig = { ...form.conditionConfig, [key]: value }
  form.conditionConfig = newConditionConfig
  updateProperty('conditionConfig', newConditionConfig)
}

const updateStyle = (key, value) => {
  const newStyle = { ...form.style, [key]: value }
  form.style = newStyle
  updateProperty('style', newStyle)
}
</script>

<style scoped>
.edge-property-form {
  padding: 16px;
}
</style>
