<template>
  <div class="condition-node" :class="{ selected }">
    <div class="node-header">
      <el-icon class="node-icon"><QuestionFilled /></el-icon>
      <span class="node-title">{{ data.name || '条件判断' }}</span>
    </div>
    
    <div class="node-body">
      <div class="node-description">{{ data.description || '根据条件分支执行' }}</div>
      
      <!-- 条件配置预览 -->
      <div v-if="conditions.length" class="conditions-preview">
        <div class="field-label">条件: {{ conditions.length }} 个</div>
        <div class="condition-list">
          <div
            v-for="(condition, index) in conditions.slice(0, 2)"
            :key="index"
            class="condition-item"
          >
            <span class="condition-text">{{ formatCondition(condition) }}</span>
          </div>
          <div v-if="conditions.length > 2" class="more-conditions">
            还有 {{ conditions.length - 2 }} 个条件...
          </div>
        </div>
      </div>
      
      <!-- 默认分支提示 -->
      <div v-if="hasDefaultBranch" class="default-branch-preview">
        <div class="field-label">包含默认分支</div>
      </div>
    </div>
    
    <div class="node-footer">
      <!-- 输入连接点 -->
      <Handle
        id="condition-input"
        type="target"
        position="left"
        :style="{ background: '#fa8c16' }"
        :connectable="true"
      />

      <!-- 输出连接点 - True分支 -->
      <Handle
        id="true"
        type="source"
        position="right"
        :style="{ background: '#52c41a', top: '30%' }"
        :connectable="true"
      />

      <!-- 输出连接点 - False分支 -->
      <Handle
        id="false"
        type="source"
        position="right"
        :style="{ background: '#ff4d4f', top: '70%' }"
        :connectable="true"
      />
    </div>
    
    <!-- 分支标签 -->
    <div class="branch-labels">
      <div class="branch-label true-label">True</div>
      <div class="branch-label false-label">False</div>
    </div>
    
    <!-- 执行状态指示器 -->
    <div v-if="executionStatus" class="execution-status" :class="executionStatus">
      <el-icon>
        <component :is="getStatusIcon(executionStatus)" />
      </el-icon>
    </div>
    
    <!-- 配置不完整警告 -->
    <div v-if="!isConfigComplete" class="config-warning">
      <el-icon><Warning /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import { QuestionFilled, Timer, Loading, Check, Close, Warning } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  executionStatus: {
    type: String,
    default: null
  }
})

// 计算属性
const conditions = computed(() => {
  return props.data.config?.conditions || []
})

const hasDefaultBranch = computed(() => {
  return props.data.config?.hasDefaultBranch || false
})

const isConfigComplete = computed(() => {
  return conditions.value.length > 0
})

// 方法
const formatCondition = (condition) => {
  if (!condition) return ''
  
  const { variable, operator, value } = condition
  const operatorNames = {
    '==': '等于',
    '!=': '不等于',
    '>': '大于',
    '<': '小于',
    '>=': '大于等于',
    '<=': '小于等于',
    'contains': '包含',
    'not_contains': '不包含',
    'starts_with': '开头是',
    'ends_with': '结尾是',
    'is_empty': '为空',
    'is_not_empty': '不为空'
  }
  
  const operatorName = operatorNames[operator] || operator
  
  if (operator === 'is_empty' || operator === 'is_not_empty') {
    return `${variable} ${operatorName}`
  }
  
  return `${variable} ${operatorName} ${value}`
}

const getStatusIcon = (status) => {
  const icons = {
    pending: Timer,
    running: Loading,
    completed: Check,
    failed: Close
  }
  return icons[status] || Timer
}
</script>

<style scoped>
.condition-node {
  min-width: 200px;
  background: white;
  border: 2px solid #fa8c16;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.condition-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: #fff7e6;
  border-radius: 6px 6px 0 0;
}

.node-icon {
  color: #fa8c16;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.node-body {
  padding: 8px 16px 12px;
}

.node-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.conditions-preview {
  margin-top: 8px;
}

.field-label {
  font-size: 11px;
  color: #999;
  margin-bottom: 4px;
}

.condition-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.condition-item {
  font-size: 11px;
  color: #262626;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
}

.condition-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.more-conditions {
  font-size: 11px;
  color: #999;
  font-style: italic;
}

.default-branch-preview {
  margin-top: 8px;
}

.node-footer {
  position: relative;
  height: 8px;
}

.branch-labels {
  position: absolute;
  right: -45px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.branch-label {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-weight: 500;
}

.true-label {
  background: #52c41a;
}

.false-label {
  background: #ff4d4f;
}

.execution-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.execution-status.pending {
  background: #faad14;
}

.execution-status.running {
  background: #1890ff;
  animation: pulse 1.5s infinite;
}

.execution-status.completed {
  background: #52c41a;
}

.execution-status.failed {
  background: #ff4d4f;
}

.config-warning {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #faad14;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Handle 样式 */
:deep(.vue-flow__handle) {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.vue-flow__handle-left) {
  left: -6px;
}

:deep(.vue-flow__handle-right) {
  right: -6px;
}
</style>
