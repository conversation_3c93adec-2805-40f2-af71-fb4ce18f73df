<template>
  <div class="end-node" :class="{ selected }">
    <div class="node-header">
      <el-icon class="node-icon"><Flag /></el-icon>
      <span class="node-title">{{ data.name || '结束' }}</span>
    </div>
    
    <div class="node-body">
      <div class="node-description">{{ data.description || '流程结束节点' }}</div>
      
      <!-- 输出配置预览 -->
      <div v-if="outputFields.length" class="output-fields-preview">
        <div class="field-label">输出字段：</div>
        <div class="field-list">
          <el-tag
            v-for="field in outputFields.slice(0, 2)"
            :key="field.name"
            size="small"
            effect="plain"
            type="success"
          >
            {{ field.label }}
          </el-tag>
          <span v-if="outputFields.length > 2" class="more-fields">
            +{{ outputFields.length - 2 }}
          </span>
        </div>
      </div>
      
      <!-- 结果处理配置 -->
      <div v-if="hasResultProcessing" class="result-processing-preview">
        <div class="field-label">包含结果处理</div>
      </div>
    </div>
    
    <div class="node-footer">
      <!-- 输入连接点 -->
      <Handle
        id="end-input"
        type="target"
        position="left"
        :style="{ background: '#ff4d4f' }"
        :connectable="true"
      />
    </div>
    
    <!-- 执行状态指示器 -->
    <div v-if="executionStatus" class="execution-status" :class="executionStatus">
      <el-icon>
        <component :is="getStatusIcon(executionStatus)" />
      </el-icon>
    </div>
    
    <!-- 完成指示器 -->
    <div v-if="executionStatus === 'completed'" class="completion-indicator">
      <el-icon><Check /></el-icon>
      <span>已完成</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import { Flag, Timer, Loading, Check, Close } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  executionStatus: {
    type: String,
    default: null
  }
})

// 计算属性
const outputFields = computed(() => {
  return props.data.config?.outputFields || []
})

const hasResultProcessing = computed(() => {
  return props.data.config?.resultProcessing || false
})

// 方法
const getStatusIcon = (status) => {
  const icons = {
    pending: Timer,
    running: Loading,
    completed: Check,
    failed: Close
  }
  return icons[status] || Timer
}
</script>

<style scoped>
.end-node {
  min-width: 180px;
  background: white;
  border: 2px solid #ff4d4f;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.end-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: #fff1f0;
  border-radius: 6px 6px 0 0;
}

.node-icon {
  color: #ff4d4f;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.node-body {
  padding: 8px 16px 12px;
}

.node-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.output-fields-preview {
  margin-top: 8px;
}

.field-label {
  font-size: 11px;
  color: #999;
  margin-bottom: 4px;
}

.field-list {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-wrap: wrap;
}

.more-fields {
  font-size: 11px;
  color: #999;
}

.result-processing-preview {
  margin-top: 8px;
}

.node-footer {
  position: relative;
  height: 8px;
}

.execution-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.execution-status.pending {
  background: #faad14;
}

.execution-status.running {
  background: #1890ff;
  animation: pulse 1.5s infinite;
}

.execution-status.completed {
  background: #52c41a;
}

.execution-status.failed {
  background: #ff4d4f;
}

.completion-indicator {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #52c41a;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  animation: celebration 3s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes celebration {
  0% {
    transform: translateX(-50%) scale(0.8);
    opacity: 0;
  }
  20% {
    transform: translateX(-50%) scale(1.1);
    opacity: 1;
  }
  40% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
}

/* Handle 样式 */
:deep(.vue-flow__handle) {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.vue-flow__handle-left) {
  left: -6px;
}
</style>
