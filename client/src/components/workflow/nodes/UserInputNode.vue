<template>
  <div class="user-input-node" :class="{ selected }">
    <div class="node-header">
      <el-icon class="node-icon"><User /></el-icon>
      <span class="node-title">{{ data.name || '用户输入' }}</span>
    </div>
    
    <div class="node-body">
      <div class="node-description">{{ data.description || '等待用户输入' }}</div>
      
      <!-- 输入字段配置预览 -->
      <div v-if="inputFields.length" class="input-fields-preview">
        <div class="field-label">输入字段: {{ inputFields.length }} 个</div>
        <div class="field-list">
          <div
            v-for="field in inputFields.slice(0, 2)"
            :key="field.name"
            class="field-item"
          >
            <span class="field-name">{{ field.label }}</span>
            <el-tag size="small" effect="plain" :type="getFieldTypeColor(field.type)">
              {{ getFieldTypeName(field.type) }}
            </el-tag>
          </div>
          <div v-if="inputFields.length > 2" class="more-fields">
            还有 {{ inputFields.length - 2 }} 个字段...
          </div>
        </div>
      </div>
      
      <!-- 验证规则预览 -->
      <div v-if="hasValidationRules" class="validation-preview">
        <div class="field-label">包含验证规则</div>
      </div>
    </div>
    
    <div class="node-footer">
      <!-- 输入连接点 -->
      <Handle
        id="user-input"
        type="target"
        position="left"
        :style="{ background: '#1890ff' }"
        :connectable="true"
      />

      <!-- 输出连接点 -->
      <Handle
        id="user-output"
        type="source"
        position="right"
        :style="{ background: '#1890ff' }"
        :connectable="true"
      />
    </div>
    
    <!-- 执行状态指示器 -->
    <div v-if="executionStatus" class="execution-status" :class="executionStatus">
      <el-icon>
        <component :is="getStatusIcon(executionStatus)" />
      </el-icon>
    </div>
    
    <!-- 等待用户输入指示器 -->
    <div v-if="executionStatus === 'waiting'" class="waiting-indicator">
      <el-icon><Timer /></el-icon>
      <span>等待输入</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import { User, Timer, Loading, Check, Close } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  executionStatus: {
    type: String,
    default: null // pending, running, completed, failed, waiting
  }
})

// 计算属性
const inputFields = computed(() => {
  return props.data.config?.inputFields || []
})

const hasValidationRules = computed(() => {
  return inputFields.value.some(field => field.required || field.validation)
})

// 方法
const getFieldTypeName = (type) => {
  const typeNames = {
    text: '文本',
    textarea: '多行文本',
    number: '数字',
    select: '选择',
    checkbox: '复选框',
    radio: '单选框',
    date: '日期',
    file: '文件'
  }
  return typeNames[type] || '文本'
}

const getFieldTypeColor = (type) => {
  const typeColors = {
    text: '',
    textarea: 'info',
    number: 'warning',
    select: 'success',
    checkbox: 'info',
    radio: 'info',
    date: 'warning',
    file: 'danger'
  }
  return typeColors[type] || ''
}

const getStatusIcon = (status) => {
  const icons = {
    pending: Timer,
    running: Loading,
    completed: Check,
    failed: Close,
    waiting: Timer
  }
  return icons[status] || Timer
}
</script>

<style scoped>
.user-input-node {
  min-width: 200px;
  background: white;
  border: 2px solid #1890ff;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.user-input-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: #e6f7ff;
  border-radius: 6px 6px 0 0;
}

.node-icon {
  color: #1890ff;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.node-body {
  padding: 8px 16px 12px;
}

.node-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.input-fields-preview {
  margin-top: 8px;
}

.field-label {
  font-size: 11px;
  color: #999;
  margin-bottom: 4px;
}

.field-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.field-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.field-name {
  color: #262626;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.more-fields {
  font-size: 11px;
  color: #999;
  font-style: italic;
}

.validation-preview {
  margin-top: 8px;
}

.node-footer {
  position: relative;
  height: 8px;
}

.execution-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.execution-status.pending {
  background: #faad14;
}

.execution-status.running {
  background: #1890ff;
  animation: pulse 1.5s infinite;
}

.execution-status.completed {
  background: #52c41a;
}

.execution-status.failed {
  background: #ff4d4f;
}

.execution-status.waiting {
  background: #faad14;
  animation: blink 2s infinite;
}

.waiting-indicator {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: #faad14;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  animation: blink 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.6;
  }
}

/* Handle 样式 */
:deep(.vue-flow__handle) {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.vue-flow__handle-left) {
  left: -6px;
}

:deep(.vue-flow__handle-right) {
  right: -6px;
}
</style>
