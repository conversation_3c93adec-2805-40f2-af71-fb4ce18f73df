<template>
  <div class="ai-generation-node" :class="{ selected }">
    <div class="node-header">
      <el-icon class="node-icon"><MagicStick /></el-icon>
      <span class="node-title">{{ data.name || 'AI生成' }}</span>
    </div>
    
    <div class="node-body">
      <div class="node-description">{{ data.description || '使用AI生成内容' }}</div>
      
      <!-- 配置信息预览 -->
      <div class="config-preview">
        <div v-if="aiConfigName" class="config-item">
          <span class="config-label">AI模型:</span>
          <span class="config-value">{{ aiConfigName }}</span>
        </div>
        <div v-if="templateName" class="config-item">
          <span class="config-label">模板:</span>
          <span class="config-value">{{ templateName }}</span>
        </div>
        <div v-if="outputVariable" class="config-item">
          <span class="config-label">输出:</span>
          <span class="config-value">{{ outputVariable }}</span>
        </div>
      </div>
      
      <!-- 输入映射预览 -->
      <div v-if="inputMappingCount > 0" class="input-mapping-preview">
        <div class="field-label">输入映射: {{ inputMappingCount }} 个参数</div>
      </div>
    </div>
    
    <div class="node-footer">
      <!-- 输入连接点 -->
      <Handle
        id="ai-input"
        type="target"
        position="left"
        :style="{ background: '#722ed1' }"
        :connectable="true"
      />

      <!-- 输出连接点 -->
      <Handle
        id="ai-output"
        type="source"
        position="right"
        :style="{ background: '#722ed1' }"
        :connectable="true"
      />
    </div>
    
    <!-- 执行状态指示器 -->
    <div v-if="executionStatus" class="execution-status" :class="executionStatus">
      <el-icon>
        <component :is="getStatusIcon(executionStatus)" />
      </el-icon>
    </div>
    
    <!-- 配置不完整警告 -->
    <div v-if="!isConfigComplete" class="config-warning">
      <el-icon><Warning /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import { MagicStick, Timer, Loading, Check, Close, Warning } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  executionStatus: {
    type: String,
    default: null
  },
  aiConfigs: {
    type: Array,
    default: () => []
  },
  promptTemplates: {
    type: Array,
    default: () => []
  }
})

// 计算属性
const config = computed(() => props.data.config || {})

const aiConfigName = computed(() => {
  const aiConfig = props.aiConfigs.find(config => config.id === props.data.config?.aiConfigId)
  return aiConfig ? `${aiConfig.provider} - ${aiConfig.model}` : null
})

const templateName = computed(() => {
  const template = props.promptTemplates.find(t => t.id === props.data.config?.promptTemplateId)
  return template?.name || null
})

const outputVariable = computed(() => {
  return props.data.config?.outputVariable || null
})

const inputMappingCount = computed(() => {
  const mapping = props.data.config?.inputMapping || {}
  return Object.keys(mapping).length
})

const isConfigComplete = computed(() => {
  const config = props.data.config || {}
  return config.aiConfigId && config.promptTemplateId
})

// 方法
const getStatusIcon = (status) => {
  const icons = {
    pending: Timer,
    running: Loading,
    completed: Check,
    failed: Close
  }
  return icons[status] || Timer
}
</script>

<style scoped>
.ai-generation-node {
  min-width: 200px;
  background: white;
  border: 2px solid #722ed1;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.ai-generation-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: #f9f0ff;
  border-radius: 6px 6px 0 0;
}

.node-icon {
  color: #722ed1;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.node-body {
  padding: 8px 16px 12px;
}

.node-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.config-preview {
  margin-top: 8px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 11px;
}

.config-label {
  color: #999;
  flex-shrink: 0;
}

.config-value {
  color: #262626;
  font-weight: 500;
  text-align: right;
  flex: 1;
  margin-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.input-mapping-preview {
  margin-top: 8px;
}

.field-label {
  font-size: 11px;
  color: #999;
}

.node-footer {
  position: relative;
  height: 8px;
}

.execution-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.execution-status.pending {
  background: #faad14;
}

.execution-status.running {
  background: #1890ff;
  animation: pulse 1.5s infinite;
}

.execution-status.completed {
  background: #52c41a;
}

.execution-status.failed {
  background: #ff4d4f;
}

.config-warning {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #faad14;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Handle 样式 */
:deep(.vue-flow__handle) {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.vue-flow__handle-left) {
  left: -6px;
}

:deep(.vue-flow__handle-right) {
  right: -6px;
}
</style>
