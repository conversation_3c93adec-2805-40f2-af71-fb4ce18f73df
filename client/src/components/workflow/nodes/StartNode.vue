<template>
  <div class="start-node" :class="{ selected }">
    <div class="node-header">
      <el-icon class="node-icon"><VideoPlay /></el-icon>
      <span class="node-title">{{ data.name || '开始' }}</span>
    </div>
    
    <div class="node-body">
      <div class="node-description">{{ data.description || '流程开始节点' }}</div>
      
      <!-- 输入字段配置预览 -->
      <div v-if="inputFields.length" class="input-fields-preview">
        <div class="field-label">输入字段：</div>
        <div class="field-list">
          <el-tag
            v-for="field in inputFields.slice(0, 2)"
            :key="field.name"
            size="small"
            effect="plain"
          >
            {{ field.label }}
          </el-tag>
          <span v-if="inputFields.length > 2" class="more-fields">
            +{{ inputFields.length - 2 }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="node-footer">
      <!-- 输出连接点 -->
      <Handle
        id="start-output"
        type="source"
        position="right"
        :style="{ background: '#52c41a' }"
        :connectable="true"
      />
    </div>
    
    <!-- 执行状态指示器 -->
    <div v-if="executionStatus" class="execution-status" :class="executionStatus">
      <el-icon>
        <component :is="getStatusIcon(executionStatus)" />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import { VideoPlay, Timer, Loading, Check, Close } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  executionStatus: {
    type: String,
    default: null // pending, running, completed, failed
  }
})

// 计算属性
const inputFields = computed(() => {
  return props.data.config?.inputFields || []
})

// 方法
const getStatusIcon = (status) => {
  const icons = {
    pending: Timer,
    running: Loading,
    completed: Check,
    failed: Close
  }
  return icons[status] || Timer
}
</script>

<style scoped>
.start-node {
  min-width: 180px;
  background: white;
  border: 2px solid #52c41a;
  border-radius: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.start-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: #f6ffed;
  border-radius: 6px 6px 0 0;
}

.node-icon {
  color: #52c41a;
  font-size: 16px;
}

.node-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.node-body {
  padding: 8px 16px 12px;
}

.node-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.input-fields-preview {
  margin-top: 8px;
}

.field-label {
  font-size: 11px;
  color: #999;
  margin-bottom: 4px;
}

.field-list {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-wrap: wrap;
}

.more-fields {
  font-size: 11px;
  color: #999;
}

.node-footer {
  position: relative;
  height: 8px;
}

.execution-status {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.execution-status.pending {
  background: #faad14;
}

.execution-status.running {
  background: #1890ff;
  animation: pulse 1.5s infinite;
}

.execution-status.completed {
  background: #52c41a;
}

.execution-status.failed {
  background: #ff4d4f;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Handle 样式 */
:deep(.vue-flow__handle) {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

:deep(.vue-flow__handle-right) {
  right: -6px;
}
</style>
