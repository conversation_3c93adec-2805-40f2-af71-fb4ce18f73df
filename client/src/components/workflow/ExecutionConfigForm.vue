<template>
  <div class="execution-config-form">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="关联项目" prop="projectId">
        <el-select 
          v-model="form.projectId" 
          placeholder="选择关联项目（可选）"
          clearable
          filterable
        >
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
        <div class="form-tip">选择项目后，流程执行时可以访问项目相关数据</div>
      </el-form-item>

      <el-divider content-position="left">输入数据</el-divider>

      <!-- 动态输入字段 -->
      <div v-if="startNodeFields.length > 0" class="start-node-inputs">
        <div 
          v-for="field in startNodeFields"
          :key="field.name"
          class="input-field"
        >
          <el-form-item 
            :label="field.label"
            :prop="`inputData.${field.name}`"
            :rules="field.required ? [{ required: true, message: `请输入${field.label}` }] : []"
          >
            <el-input
              v-if="field.type === 'text'"
              v-model="form.inputData[field.name]"
              :placeholder="`请输入${field.label}`"
            />
            <el-input
              v-else-if="field.type === 'textarea'"
              v-model="form.inputData[field.name]"
              type="textarea"
              :rows="3"
              :placeholder="`请输入${field.label}`"
            />
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="form.inputData[field.name]"
              :placeholder="`请输入${field.label}`"
              style="width: 100%"
            />
            <el-select
              v-else-if="field.type === 'select'"
              v-model="form.inputData[field.name]"
              :placeholder="`请选择${field.label}`"
            >
              <el-option
                v-for="option in field.options || []"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 手动输入数据 -->
      <div v-else class="manual-input">
        <el-form-item label="输入数据">
          <el-input
            v-model="inputDataText"
            type="textarea"
            :rows="6"
            placeholder="请输入JSON格式的数据，例如：&#10;{&#10;  &quot;novelIdea&quot;: &quot;一个关于时间旅行的科幻小说&quot;,&#10;  &quot;genre&quot;: &quot;科幻&quot;&#10;}"
            @input="parseInputData"
          />
          <div class="form-tip">
            请输入JSON格式的数据作为流程的初始输入
          </div>
        </el-form-item>
      </div>

      <el-divider content-position="left">执行选项</el-divider>

      <el-form-item label="执行模式">
        <el-radio-group v-model="form.executionMode">
          <el-radio value="auto">自动执行</el-radio>
          <el-radio value="step">逐步执行</el-radio>
        </el-radio-group>
        <div class="form-tip">
          自动执行：流程将自动运行到需要用户输入的节点<br>
          逐步执行：每个节点执行前都会暂停等待确认
        </div>
      </el-form-item>

      <el-form-item label="超时设置">
        <el-input-number
          v-model="form.timeout"
          :min="30"
          :max="3600"
          :step="30"
          style="width: 200px"
        />
        <span style="margin-left: 8px">秒</span>
        <div class="form-tip">单个节点的最大执行时间</div>
      </el-form-item>

      <el-form-item label="错误处理">
        <el-select v-model="form.errorHandling">
          <el-option label="停止执行" value="stop" />
          <el-option label="跳过错误节点" value="skip" />
          <el-option label="重试执行" value="retry" />
        </el-select>
      </el-form-item>

      <!-- 预览输入数据 -->
      <el-form-item label="数据预览">
        <el-input
          :value="JSON.stringify(form.inputData, null, 2)"
          type="textarea"
          :rows="4"
          readonly
          placeholder="输入数据预览"
        />
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleExecute" :loading="executing">
        开始执行
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/services/api'

const props = defineProps({
  workflow: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['execute', 'cancel'])

// 响应式数据
const formRef = ref()
const executing = ref(false)
const projects = ref([])
const inputDataText = ref('')

const form = reactive({
  projectId: null,
  inputData: {},
  executionMode: 'auto',
  timeout: 300,
  errorHandling: 'stop'
})

const rules = {
  // 动态规则将在组件挂载时生成
}

// 计算属性
const startNodeFields = computed(() => {
  if (!props.workflow?.nodes) return []
  
  const startNode = props.workflow.nodes.find(node => node.nodeType === 'start')
  return startNode?.config?.inputFields || []
})

// 方法
const loadProjects = async () => {
  try {
    const response = await api.get('/projects')
    projects.value = response
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

const parseInputData = () => {
  try {
    if (inputDataText.value.trim()) {
      form.inputData = JSON.parse(inputDataText.value)
    } else {
      form.inputData = {}
    }
  } catch (error) {
    ElMessage.warning('输入数据格式不正确，请检查JSON格式')
  }
}

const handleExecute = async () => {
  try {
    await formRef.value.validate()
    executing.value = true
    
    const config = {
      projectId: form.projectId,
      inputData: form.inputData,
      options: {
        executionMode: form.executionMode,
        timeout: form.timeout,
        errorHandling: form.errorHandling
      }
    }
    
    emit('execute', config)
  } catch (error) {
    if (error.errors) return // 表单验证错误
    ElMessage.error('执行配置验证失败')
    console.error(error)
  } finally {
    executing.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 初始化输入数据
const initializeInputData = () => {
  const initialData = {}
  startNodeFields.value.forEach(field => {
    initialData[field.name] = field.type === 'number' ? 0 : ''
  })
  form.inputData = initialData
}

// 生命周期
onMounted(() => {
  loadProjects()
  initializeInputData()
})
</script>

<style scoped>
.execution-config-form {
  padding: 20px;
}

.input-field {
  margin-bottom: 16px;
}

.manual-input {
  margin-bottom: 20px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.start-node-inputs {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}
</style>
