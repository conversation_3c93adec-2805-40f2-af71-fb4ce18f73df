<template>
  <div class="node-property-form">
    <el-form :model="form" label-width="100px" @submit.prevent>
      <!-- 基本信息 -->
      <el-form-item label="节点名称">
        <el-input 
          v-model="form.name" 
          placeholder="请输入节点名称"
          :disabled="!isEditing"
          @input="updateProperty('name', $event)"
        />
      </el-form-item>
      
      <el-form-item label="节点描述">
        <el-input 
          v-model="form.description" 
          type="textarea"
          :rows="2"
          placeholder="请输入节点描述"
          :disabled="!isEditing"
          @input="updateProperty('description', $event)"
        />
      </el-form-item>

      <!-- AI生成节点特有配置 -->
      <template v-if="node.type === 'ai_generation'">
        <el-divider content-position="left">AI配置</el-divider>
        
        <el-form-item label="AI模型">
          <el-select 
            v-model="form.config.aiConfigId" 
            placeholder="选择AI模型"
            :disabled="!isEditing"
            @change="updateConfig('aiConfigId', $event)"
          >
            <el-option
              v-for="config in aiConfigs"
              :key="config.id"
              :label="`${config.provider} - ${config.model}`"
              :value="config.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="提示词模板">
          <div style="display: flex; gap: 8px;">
            <el-select
              v-model="form.config.promptTemplateId"
              placeholder="选择提示词模板"
              :disabled="!isEditing"
              @change="updateConfig('promptTemplateId', $event)"
              style="flex: 1;"
            >
              <el-option
                v-for="template in promptTemplates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
            <el-button @click="debugTemplates" size="small" type="info">
              调试({{ promptTemplates.length }})
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="输出变量">
          <el-input 
            v-model="form.config.outputVariable" 
            placeholder="输出变量名称"
            :disabled="!isEditing"
            @input="updateConfig('outputVariable', $event)"
          />
        </el-form-item>
      </template>

      <!-- 用户输入节点特有配置 -->
      <template v-if="node.type === 'user_input'">
        <el-divider content-position="left">输入字段配置</el-divider>
        
        <div class="input-fields-config">
          <div 
            v-for="(field, index) in form.config.inputFields || []"
            :key="index"
            class="field-item"
          >
            <el-card>
              <div class="field-header">
                <span>字段 {{ index + 1 }}</span>
                <el-button 
                  v-if="isEditing"
                  type="danger" 
                  size="small" 
                  @click="removeInputField(index)"
                >
                  删除
                </el-button>
              </div>
              
              <el-form :model="field" label-width="80px">
                <el-form-item label="字段名">
                  <el-input 
                    v-model="field.name" 
                    :disabled="!isEditing"
                    @input="updateInputFields"
                  />
                </el-form-item>
                <el-form-item label="显示名">
                  <el-input 
                    v-model="field.label" 
                    :disabled="!isEditing"
                    @input="updateInputFields"
                  />
                </el-form-item>
                <el-form-item label="字段类型">
                  <el-select 
                    v-model="field.type" 
                    :disabled="!isEditing"
                    @change="updateInputFields"
                  >
                    <el-option label="文本" value="text" />
                    <el-option label="多行文本" value="textarea" />
                    <el-option label="数字" value="number" />
                    <el-option label="选择" value="select" />
                  </el-select>
                </el-form-item>
                <el-form-item label="必填">
                  <el-switch 
                    v-model="field.required" 
                    :disabled="!isEditing"
                    @change="updateInputFields"
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </div>
          
          <el-button 
            v-if="isEditing"
            type="dashed" 
            @click="addInputField"
            style="width: 100%; margin-top: 10px;"
          >
            添加输入字段
          </el-button>
        </div>
      </template>

      <!-- 条件判断节点特有配置 -->
      <template v-if="node.type === 'condition'">
        <el-divider content-position="left">条件配置</el-divider>
        
        <div class="conditions-config">
          <div 
            v-for="(condition, index) in form.config.conditions || []"
            :key="index"
            class="condition-item"
          >
            <el-card>
              <div class="condition-header">
                <span>条件 {{ index + 1 }}</span>
                <el-button 
                  v-if="isEditing"
                  type="danger" 
                  size="small" 
                  @click="removeCondition(index)"
                >
                  删除
                </el-button>
              </div>
              
              <el-form :model="condition" label-width="80px">
                <el-form-item label="变量">
                  <el-input 
                    v-model="condition.variable" 
                    :disabled="!isEditing"
                    @input="updateConditions"
                  />
                </el-form-item>
                <el-form-item label="操作符">
                  <el-select 
                    v-model="condition.operator" 
                    :disabled="!isEditing"
                    @change="updateConditions"
                  >
                    <el-option label="等于" value="==" />
                    <el-option label="不等于" value="!=" />
                    <el-option label="大于" value=">" />
                    <el-option label="小于" value="<" />
                    <el-option label="包含" value="contains" />
                  </el-select>
                </el-form-item>
                <el-form-item label="值">
                  <el-input 
                    v-model="condition.value" 
                    :disabled="!isEditing"
                    @input="updateConditions"
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </div>
          
          <el-button 
            v-if="isEditing"
            type="dashed" 
            @click="addCondition"
            style="width: 100%; margin-top: 10px;"
          >
            添加条件
          </el-button>
        </div>
      </template>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/services/api'

const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  isEditing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update'])

// 响应式数据
const form = reactive({
  name: '',
  description: '',
  config: {}
})

const aiConfigs = ref([])
const promptTemplates = ref([])

// 监听节点变化
watch(() => props.node, (newNode) => {
  if (newNode) {
    form.name = newNode.data?.name || ''
    form.description = newNode.data?.description || ''
    form.config = { ...newNode.data?.config } || {}
  }
}, { immediate: true, deep: true })

// 方法
const updateProperty = (key, value) => {
  emit('update', props.node.id, { [key]: value })
}

const updateConfig = (key, value) => {
  const newConfig = { ...form.config, [key]: value }
  form.config = newConfig
  emit('update', props.node.id, { config: newConfig })
}

const updateInputFields = () => {
  updateConfig('inputFields', form.config.inputFields)
}

const addInputField = () => {
  if (!form.config.inputFields) {
    form.config.inputFields = []
  }
  form.config.inputFields.push({
    name: '',
    label: '',
    type: 'text',
    required: false
  })
  updateInputFields()
}

const removeInputField = (index) => {
  form.config.inputFields.splice(index, 1)
  updateInputFields()
}

const updateConditions = () => {
  updateConfig('conditions', form.config.conditions)
}

const addCondition = () => {
  if (!form.config.conditions) {
    form.config.conditions = []
  }
  form.config.conditions.push({
    variable: '',
    operator: '==',
    value: ''
  })
  updateConditions()
}

const removeCondition = (index) => {
  form.config.conditions.splice(index, 1)
  updateConditions()
}

// 加载AI配置和提示词模板
const loadAIConfigs = async () => {
  try {
    console.log('开始加载AI配置...')
    const response = await api.get('/ai-configs')
    console.log('AI配置API响应:', response)

    // 检查响应数据结构
    if (Array.isArray(response)) {
      console.log('响应是数组，长度:', response.length)
      // 不过滤 isActive，显示所有配置
      aiConfigs.value = response
      console.log('设置的AI配置:', aiConfigs.value)
    } else if (response && Array.isArray(response.data)) {
      console.log('响应有data字段，长度:', response.data.length)
      aiConfigs.value = response.data
      console.log('设置的AI配置:', aiConfigs.value)
    } else {
      console.warn('AI配置响应格式不正确:', response)
      aiConfigs.value = []
    }
  } catch (error) {
    console.error('加载AI配置失败:', error)
    aiConfigs.value = []
  }
}

const loadPromptTemplates = async () => {
  try {
    console.log('开始加载提示词模板...')
    const response = await api.get('/prompt-templates')
    console.log('提示词模板API响应:', response)

    // 检查响应数据结构
    if (Array.isArray(response)) {
      console.log('响应是数组，长度:', response.length)
      // 不过滤 isActive，显示所有模板
      promptTemplates.value = response
      console.log('设置的提示词模板:', promptTemplates.value)
    } else if (response && Array.isArray(response.data)) {
      console.log('响应有data字段，长度:', response.data.length)
      promptTemplates.value = response.data
      console.log('设置的提示词模板:', promptTemplates.value)
    } else {
      console.warn('提示词模板响应格式不正确:', response)
      promptTemplates.value = []
    }
  } catch (error) {
    console.error('加载提示词模板失败:', error)
    promptTemplates.value = []
  }
}

// 调试函数
const debugTemplates = () => {
  console.log('=== 提示词模板调试信息 ===')
  console.log('promptTemplates.value:', promptTemplates.value)
  console.log('promptTemplates 长度:', promptTemplates.value.length)
  console.log('当前选中的模板ID:', form.config.promptTemplateId)
  console.log('========================')

  // 重新加载数据
  loadPromptTemplates()
}

onMounted(() => {
  loadAIConfigs()
  loadPromptTemplates()
})
</script>

<style scoped>
.node-property-form {
  padding: 16px;
}

.field-item,
.condition-item {
  margin-bottom: 16px;
}

.field-header,
.condition-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
}

.input-fields-config,
.conditions-config {
  max-height: 400px;
  overflow-y: auto;
}
</style>
