<template>
  <el-dialog
    :title="`AI生成${contentType}`"
    :model-value="visible"
    @update:model-value="updateVisible"
    width="800px"
    @close="handleClose"
  >
    <el-form :model="form" label-width="120px">
      <el-form-item label="AI配置">
        <el-select 
          v-model="form.aiConfigId" 
          placeholder="请选择AI配置"
          style="width: 100%"
        >
          <el-option
            v-for="config in availableAIConfigs"
            :key="config.id"
            :label="`${config.provider} - ${config.model}${config.isDefault ? ' (默认)' : ''}`"
            :value="config.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="提示词模板">
        <el-select 
          v-model="form.promptTemplateId" 
          placeholder="请选择提示词模板（可选）"
          style="width: 100%"
          clearable
          @change="handleTemplateChange"
        >
          <el-option
            v-for="template in filteredTemplates"
            :key="template.id"
            :label="template.name"
            :value="template.id"
          />
        </el-select>
      </el-form-item>

      <!-- 动态参数配置 -->
      <template v-if="selectedTemplate && selectedTemplate.parameters && Array.isArray(selectedTemplate.parameters) && selectedTemplate.parameters.length > 0">
        <el-divider content-position="left">模板参数配置</el-divider>

        <el-form-item
          v-for="param in selectedTemplate.parameters"
          :key="param.key"
          :label="param.name"
          :required="param.required !== false"
        >
          <!-- 系统参数 -->
          <template v-if="param.type === 'system'">
            <el-select
              v-model="form.params[param.key]"
              :placeholder="`请选择${param.name}`"
              style="width: 100%"
              :multiple="param.multiple"
              :clearable="param.required === false"
            >
              <el-option
                v-for="item in getSystemParamOptions(param.systemType)"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </template>
          
          <!-- 普通文本参数 -->
          <template v-else-if="param.type === 'text'">
            <el-input
              v-model="form.params[param.key]"
              :placeholder="param.description || `请输入${param.name}`"
            />
          </template>
          
          <!-- 多行文本参数 -->
          <template v-else-if="param.type === 'textarea'">
            <el-input
              type="textarea"
              v-model="form.params[param.key]"
              :placeholder="param.description || `请输入${param.name}`"
              :rows="3"
            />
          </template>
          
          <!-- 选择参数 -->
          <template v-else-if="param.type === 'select'">
            <el-select
              v-model="form.params[param.key]"
              :placeholder="`请选择${param.name}`"
              style="width: 100%"
              :clearable="param.required === false"
            >
              <el-option
                v-for="option in param.options || []"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
        </el-form-item>
      </template>

      <!-- 如果没有选择模板，显示自定义要求输入 -->
      <template v-if="!form.promptTemplateId">
        <el-form-item label="生成要求">
          <el-input 
            type="textarea" 
            v-model="form.requirements"
            :placeholder="`请描述您希望生成的${contentType}内容要求...`"
            :rows="4"
          />
        </el-form-item>

        <el-form-item label="现有内容" v-if="existingContent">
          <el-input 
            type="textarea" 
            v-model="form.existingContent"
            placeholder="如果有现有内容，AI将基于此进行扩展..."
            :rows="3"
            readonly
          />
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="handleGenerate"
        :loading="generating"
        :disabled="!form.aiConfigId"
      >
        生成内容
      </el-button>
    </template>
  </el-dialog>

  <!-- AI生成结果对话框 -->
  <el-dialog
    title="AI生成结果"
    :model-value="resultDialogVisible"
    @update:model-value="updateResultDialogVisible"
    width="900px"
  >
    <div class="ai-result-content">
      <el-alert
        :title="streamingMode ? '正在生成内容...' : '生成成功'"
        :type="streamingMode ? 'info' : 'success'"
        :closable="false"
        style="margin-bottom: 20px;"
      />

      <!-- 流式生成进度 -->
      <div v-if="streamingMode" class="streaming-progress">
        <el-progress
          :percentage="streamingProgress"
          :show-text="false"
          style="margin-bottom: 10px;"
        />
        <div class="streaming-status">
          <el-icon class="rotating"><Loading /></el-icon>
          <span>AI正在思考和生成内容，请稍候...</span>
        </div>
      </div>

      <el-form label-width="100px">
        <el-form-item label="生成内容">
          <div class="content-container">
            <TypewriterEffect
              v-if="useTypewriterEffect && !streamingMode && !isStreamingComplete"
              ref="typewriterRef"
              :content="generatedContent"
              :speed="typewriterSpeed"
              :auto-start="true"
              :preserve-formatting="true"
              @typing-complete="onTypingComplete"
              @typing-progress="onTypingProgress"
            />
            <div
              v-else
              class="generated-content"
              :class="{ 'streaming': streamingMode }"
            >
              {{ generatedContent }}
              <span v-if="streamingMode" class="streaming-cursor">|</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <el-button @click="resultDialogVisible = false">关闭</el-button>
      <el-button
        type="primary"
        @click="handleUseContent"
        :disabled="streamingMode"
      >
        使用此内容
      </el-button>
      <el-button
        type="success"
        @click="handleAppendContent"
        v-if="existingContent"
        :disabled="streamingMode"
      >
        追加到现有内容
      </el-button>
      <el-button
        v-if="useTypewriterEffect && !streamingMode && !isStreamingComplete && typewriterRef?.isTyping()"
        @click="completeTyping"
      >
        立即完成
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { aiGenerationAPI, promptTemplateAPI } from '../services/api'
import api from '../services/api'
import TypewriterEffect from './TypewriterEffect.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  contentType: {
    type: String,
    required: true
  },
  projectId: {
    type: [String, Number],
    required: true
  },
  existingContent: {
    type: String,
    default: ''
  },
  contextData: {
    type: Object,
    default: () => ({})
  },
  roleFilter: {
    type: String,
    default: ''
  },
  // 新增流式生成相关props
  enableStreaming: {
    type: Boolean,
    default: true // 重新启用流式生成
  },
  useTypewriterEffect: {
    type: Boolean,
    default: true // 默认启用打字机效果
  },
  typewriterSpeed: {
    type: Number,
    default: 50 // 打字机速度（字符/秒）
  }
})

// Emits
const emit = defineEmits(['update:visible', 'content-generated'])

// 响应式数据
const generating = ref(false)
const resultDialogVisible = ref(false)
const generatedContent = ref('')
const availableAIConfigs = ref([])
const promptTemplates = ref([])
const selectedTemplate = ref(null)

// 流式生成相关状态
const streamingMode = ref(false)
const streamingProgress = ref(0)
const typewriterRef = ref(null)
const isStreamingComplete = ref(false) // 标记流式生成是否完成

// 项目相关数据（用于系统参数）
const projectData = reactive({
  characters: [],
  worldSettings: [],
  outlines: [],
  chapters: [],
  volumes: [],
  clues: []
})

// 表单数据
const form = reactive({
  aiConfigId: null,
  promptTemplateId: null,
  requirements: '',
  existingContent: '',
  params: {}
})

// 计算属性
const filteredTemplates = computed(() => {
  if (!props.roleFilter) return promptTemplates.value
  return promptTemplates.value.filter(template => 
    template.role === props.roleFilter ||
    template.name.includes(props.contentType)
  )
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initializeDialog()
  }
})

// 监听existingContent变化
watch(() => props.existingContent, (newVal) => {
  form.existingContent = newVal
})

// 处理visible更新
const updateVisible = (value) => {
  emit('update:visible', value)
}

// 处理结果对话框visible更新
const updateResultDialogVisible = (value) => {
  resultDialogVisible.value = value
}

// 初始化对话框
const initializeDialog = async () => {
  form.existingContent = props.existingContent

  // 获取AI配置和提示词模板
  await Promise.all([
    fetchAvailableAIConfigs(),
    fetchPromptTemplates(),
    fetchProjectData()
  ])

  if (availableAIConfigs.value.length === 0) {
    ElMessage.warning('请先配置AI模型才能使用生成功能')
    return
  }
}

// 获取可用AI配置
const fetchAvailableAIConfigs = async () => {
  try {
    const response = await aiGenerationAPI.getAvailableConfigs()
    availableAIConfigs.value = response
    
    // 自动选择默认配置
    const defaultConfig = response.find(config => config.isDefault)
    if (defaultConfig) {
      form.aiConfigId = defaultConfig.id
    }
  } catch (error) {
    console.error('获取AI配置失败:', error)
    ElMessage.error('获取AI配置失败')
  }
}

// 获取提示词模板
const fetchPromptTemplates = async () => {
  try {
    const response = await promptTemplateAPI.getAllTemplates()
    promptTemplates.value = response
  } catch (error) {
    console.error('获取提示词模板失败:', error)
    ElMessage.error('获取提示词模板失败')
  }
}

// 获取项目相关数据
const fetchProjectData = async () => {
  if (!props.projectId) {
    return
  }

  try {
    const [charactersRes, worldSettingsRes, outlinesRes, chaptersRes, volumesRes, cluesRes] = await Promise.all([
      api.get(`/projects/${props.projectId}/characters`),
      api.get(`/projects/${props.projectId}/world-settings`),
      api.get(`/projects/${props.projectId}/outlines`),
      api.get(`/projects/${props.projectId}/chapters`),
      api.get(`/projects/${props.projectId}/volumes`),
      api.get(`/projects/${props.projectId}/clues`)
    ])

    projectData.characters = charactersRes || []
    projectData.worldSettings = worldSettingsRes || []
    projectData.outlines = outlinesRes || []
    projectData.chapters = chaptersRes || []
    projectData.volumes = volumesRes || []
    projectData.clues = cluesRes || []
  } catch (error) {
    console.error('获取项目数据失败:', error)
  }
}

// 处理模板变化
const handleTemplateChange = (templateId) => {
  if (templateId) {
    selectedTemplate.value = promptTemplates.value.find(t => t.id === templateId)
    // 重置参数
    form.params = {}

    // 为模板参数设置默认值
    if (selectedTemplate.value && selectedTemplate.value.parameters && Array.isArray(selectedTemplate.value.parameters)) {
      selectedTemplate.value.parameters.forEach(param => {
        if (param.defaultValue) {
          form.params[param.key] = param.defaultValue
        }
      })
    } else {
      console.warn('模板参数不是数组或为空:', selectedTemplate.value?.parameters)
    }
  } else {
    selectedTemplate.value = null
    form.params = {}
  }
}

// 获取系统参数选项
const getSystemParamOptions = (systemType) => {
  switch (systemType) {
    case 'worldSetting':
      return projectData.worldSettings.map(item => ({
        id: item.id,
        label: item.title || item.name,
        value: item
      }))
    case 'character':
      return projectData.characters.map(item => ({
        id: item.id,
        label: item.name,
        value: item
      }))
    case 'outline':
      return projectData.outlines.map(item => ({
        id: item.id,
        label: item.title,
        value: item
      }))
    case 'chapter':
      return projectData.chapters.map(item => ({
        id: item.id,
        label: item.title,
        value: item
      }))
    case 'volume':
      return projectData.volumes.map(item => ({
        id: item.id,
        label: item.title,
        value: item
      }))
    case 'clue':
      return projectData.clues.map(item => ({
        id: item.id,
        label: item.name,
        value: item
      }))
    default:
      return []
  }
}

// 处理生成
const handleGenerate = async () => {
  if (!form.aiConfigId) {
    ElMessage.warning('请选择AI配置')
    return
  }

  // 验证必填参数
  if (selectedTemplate.value && selectedTemplate.value.parameters && Array.isArray(selectedTemplate.value.parameters)) {
    for (const param of selectedTemplate.value.parameters) {
      if (param.required !== false && param.key) {
        const paramValue = form.params[param.key]
        if (paramValue === undefined || paramValue === null || paramValue === '') {
          ElMessage.warning(`请填写必填参数: ${param.name}`)
          return
        }
      }
    }
  }

  generating.value = true
  try {
    let requestData

    if (form.promptTemplateId) {
      // 使用模板生成
      requestData = {
        aiConfigId: form.aiConfigId,
        promptTemplateId: form.promptTemplateId,
        projectId: props.projectId,
        params: {
          ...form.params,
          ...props.contextData
        }
      }
    } else {
      // 使用自定义要求生成
      requestData = {
        aiConfigId: form.aiConfigId,
        customPrompt: form.requirements,
        projectId: props.projectId,
        contextData: {
          ...props.contextData,
          existingContent: form.existingContent
        }
      }
    }

    // 根据配置选择生成方式
    if (props.enableStreaming) {
      await handleStreamGenerate(requestData)
    } else {
      await handleNormalGenerate(requestData)
    }
  } catch (error) {
    console.error('AI生成失败:', error)
    ElMessage.error(error.response?.data?.message || 'AI生成失败')
  } finally {
    generating.value = false
    streamingMode.value = false
  }
}

// 普通生成方式
const handleNormalGenerate = async (requestData) => {
  isStreamingComplete.value = false // 重置流式生成完成标记
  const response = await aiGenerationAPI.generateContent(requestData)

  if (response.success) {
    generatedContent.value = response.content
    resultDialogVisible.value = true
  } else {
    ElMessage.error(response.message || 'AI生成失败')
  }
}

// 流式生成方式
const handleStreamGenerate = async (requestData) => {
  streamingMode.value = true
  streamingProgress.value = 0
  generatedContent.value = ''
  isStreamingComplete.value = false // 重置流式生成完成标记
  resultDialogVisible.value = true

  try {
    await aiGenerationAPI.generateContentStream(
      requestData,
      // onChunk - 处理每个数据块
      (chunk, fullContent) => {
        generatedContent.value = fullContent
        // 更新进度（这里是一个简单的估算）
        streamingProgress.value = Math.min(95, streamingProgress.value + 2)
      },
      // onComplete - 生成完成
      (finalContent) => {
        if (finalContent) {
          generatedContent.value = finalContent
        }
        streamingMode.value = false
        streamingProgress.value = 100
        isStreamingComplete.value = true // 标记流式生成已完成

        // 流式生成完成后不需要再次启动打字机效果，因为内容已经逐步显示了
      },
      // onError - 处理错误
      (error) => {
        console.error('流式生成失败:', error)
        ElMessage.error(error.message || '流式生成失败')
        streamingMode.value = false
      }
    )
  } catch (error) {
    streamingMode.value = false
    throw error
  }
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.aiConfigId = null
  form.promptTemplateId = null
  form.requirements = ''
  form.existingContent = ''
  form.params = {}
  selectedTemplate.value = null
}

// 使用生成的内容
const handleUseContent = () => {
  emit('content-generated', {
    content: generatedContent.value,
    action: 'replace'
  })
  resultDialogVisible.value = false
  handleClose()
}

// 追加生成的内容
const handleAppendContent = () => {
  emit('content-generated', {
    content: generatedContent.value,
    action: 'append'
  })
  resultDialogVisible.value = false
  handleClose()
}

// 打字机效果相关方法
const completeTyping = () => {
  if (typewriterRef.value) {
    typewriterRef.value.completeTyping()
  }
}

const onTypingComplete = () => {
  console.log('打字机效果完成')
}

const onTypingProgress = (progress) => {
  // 可以在这里处理打字机进度
  console.log('打字机进度:', progress)
}
</script>

<style scoped>
.ai-result-content {
  max-height: 70vh;
  overflow-y: auto;
}

.streaming-progress {
  margin-bottom: 20px;
}

.streaming-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 14px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.content-container {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

.generated-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 100%;
}

.generated-content.streaming {
  position: relative;
}

.streaming-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  font-weight: bold;
  color: #409eff;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.param-form {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.system-param-item {
  margin-bottom: 15px;
}

.system-param-item:last-child {
  margin-bottom: 0;
}

.param-label {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  font-weight: 500;
}

.required-mark {
  color: #f56c6c;
}

.param-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

/* 额外的样式 */
.ai-result-content .el-textarea__inner {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}
</style>
