<template>
  <div class="page-loading-container">
    <!-- 页面内容 -->
    <div class="page-content" :class="{ 'page-content-loading': pageState.loading }">
      <slot />
    </div>
    
    <!-- 页面加载蒙层 -->
    <div
      v-if="pageState.loading"
      class="page-loading-overlay"
      :class="{ 'page-loading-visible': pageState.loading }"
    >
      <div class="page-loading-content">
        <div class="loading-spinner">
          <el-icon class="is-loading" size="32">
            <Loading />
          </el-icon>
        </div>
        <div class="loading-text">{{ pageState.text }}</div>
      </div>
    </div>
    
    <!-- 错误状态 -->
    <div
      v-if="pageState.error && !pageState.loading"
      class="page-error-overlay"
    >
      <div class="page-error-content">
        <div class="error-icon">
          <el-icon size="48" color="#f56c6c">
            <Warning />
          </el-icon>
        </div>
        <div class="error-title">加载失败</div>
        <div class="error-message">{{ getErrorMessage(pageState.error) }}</div>
        <div class="error-actions">
          <el-button type="primary" @click="handleRetry">重试</el-button>
          <el-button @click="handleClearError">关闭</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Loading, Warning } from '@element-plus/icons-vue'
import { usePageLoading } from '../composables/useLoading'

const props = defineProps({
  pageKey: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['retry'])

const { pageState, setPageError } = usePageLoading(props.pageKey)

const getErrorMessage = (error) => {
  if (typeof error === 'string') {
    return error
  }
  if (error?.response?.data?.message) {
    return error.response.data.message
  }
  if (error?.message) {
    return error.message
  }
  return '未知错误，请稍后重试'
}

const handleRetry = () => {
  setPageError(null)
  emit('retry')
}

const handleClearError = () => {
  setPageError(null)
}
</script>

<style scoped>
.page-loading-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.page-content {
  width: 100%;
  height: 100%;
  transition: filter 0.3s ease;
}

.page-content-loading {
  filter: blur(1px);
  pointer-events: none;
}

.page-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(2px);
}

.page-loading-visible {
  opacity: 1;
  visibility: visible;
}

.page-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  color: #409eff;
}

.loading-text {
  font-size: 14px;
  color: #606266;
  text-align: center;
  min-width: 100px;
}

.page-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.page-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #f5f5f5;
  max-width: 400px;
  text-align: center;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.error-message {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
