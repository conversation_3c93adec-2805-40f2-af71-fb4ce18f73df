/**
 * 为页面添加加载状态的工具函数
 */

// 需要添加加载状态的页面配置
export const pageConfigs = [
  {
    file: 'Characters.vue',
    pageKey: 'characters',
    loadingText: '正在加载角色列表...',
    fetchFunction: 'fetchCharacters'
  },
  {
    file: 'Outlines.vue',
    pageKey: 'outlines',
    loadingText: '正在加载大纲列表...',
    fetchFunction: 'fetchOutlines'
  },
  {
    file: 'Chapters.vue',
    pageKey: 'chapters',
    loadingText: '正在加载章节列表...',
    fetchFunction: 'fetchChapters'
  },
  {
    file: 'Volumes.vue',
    pageKey: 'volumes',
    loadingText: '正在加载分卷列表...',
    fetchFunction: 'fetchVolumes'
  },
  {
    file: 'Clues.vue',
    pageKey: 'clues',
    loadingText: '正在加载线索列表...',
    fetchFunction: 'fetchClues'
  },
  {
    file: 'AIConfig.vue',
    pageKey: 'ai-config',
    loadingText: '正在加载AI配置...',
    fetchFunction: 'fetchConfigs'
  },
  {
    file: 'PromptGenerator.vue',
    pageKey: 'prompt-generator',
    loadingText: '正在加载提示词模板...',
    fetchFunction: 'fetchTemplates'
  }
];

/**
 * 生成页面包装器代码
 */
export function generatePageWrapper(pageKey, retryFunction) {
  return `<PageLoading page-key="${pageKey}" @retry="${retryFunction}">`;
}

/**
 * 生成导入语句
 */
export function generateImports() {
  return `import PageLoading from '../components/PageLoading.vue';
import { usePageLoading, useOperationLoading } from '../composables/useLoading';`;
}

/**
 * 生成加载状态管理代码
 */
export function generateLoadingManagement(pageKey) {
  return `// 加载状态管理
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('${pageKey}');
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading();`;
}

/**
 * 生成fetch函数包装器
 */
export function generateFetchWrapper(functionName, loadingText) {
  return `
// 原始函数名: ${functionName}
// 在函数开始处添加: showPageLoading('${loadingText}');
// 在成功后添加: hidePageLoading();
// 在catch中添加: setPageError(error);
`;
}

/**
 * 为按钮添加加载状态的模板
 */
export function generateButtonLoading(operationKey) {
  return `:loading="getOperationState('${operationKey}').loading"`;
}

/**
 * 操作函数包装器模板
 */
export function generateOperationWrapper(operationKey, operationText) {
  return `
// 在操作开始处添加: showOperationLoading('${operationKey}', '${operationText}');
// 在finally中添加: hideOperationLoading('${operationKey}');
`;
}

// 常用操作配置
export const commonOperations = {
  create: { key: 'create', text: '正在创建...' },
  update: { key: 'update', text: '正在更新...' },
  delete: { key: 'delete', text: '正在删除...' },
  save: { key: 'save', text: '正在保存...' },
  generate: { key: 'generate', text: '正在生成...' },
  test: { key: 'test', text: '正在测试...' }
};
