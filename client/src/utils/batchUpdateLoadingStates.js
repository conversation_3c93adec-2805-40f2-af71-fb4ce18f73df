/**
 * 批量更新页面加载状态的脚本
 * 这个文件包含了为各个页面添加加载状态的代码片段
 */

// 需要为以下页面添加的代码片段

// 1. Characters.vue - 添加加载状态管理
export const charactersLoadingCode = `
// 在setup函数中添加
const { showPageLoading, hidePageLoading, setPageError } = usePageLoading('characters')
const { showOperationLoading, hideOperationLoading, getOperationState } = useOperationLoading()

// 更新fetchCharacters函数
const fetchCharacters = async () => {
  try {
    showPageLoading('正在加载角色列表...')
    const response = await characterAPI.getAllCharacters(projectId)
    characters.value = response
    hidePageLoading()
  } catch (error) {
    console.error('获取角色列表失败:', error)
    setPageError(error)
    ElMessage.error('获取角色列表失败')
  }
}

// 更新操作函数，添加操作加载状态
const handleSubmit = async () => {
  try {
    showOperationLoading('save', '正在保存角色...')
    // 原有的保存逻辑
    hideOperationLoading('save')
  } catch (error) {
    hideOperationLoading('save')
    throw error
  }
}
`

// 2. Chapters.vue - 添加数据获取加载状态
export const chaptersLoadingCode = `
// 更新fetchChapters函数
const fetchChapters = async () => {
  try {
    showPageLoading('正在加载章节列表...')
    const response = await chapterAPI.getAllChapters(projectId)
    chapters.value = response
    hidePageLoading()
  } catch (error) {
    console.error('获取章节列表失败:', error)
    setPageError(error)
    ElMessage.error('获取章节列表失败')
  }
}
`

// 3. Volumes.vue - 添加数据获取加载状态
export const volumesLoadingCode = `
// 更新fetchVolumes函数
const fetchVolumes = async () => {
  try {
    showPageLoading('正在加载分卷列表...')
    const response = await volumeAPI.getAllVolumes(projectId)
    volumes.value = response
    hidePageLoading()
  } catch (error) {
    console.error('获取分卷列表失败:', error)
    setPageError(error)
    ElMessage.error('获取分卷列表失败')
  }
}
`

// 4. Clues.vue - 添加数据获取加载状态
export const cluesLoadingCode = `
// 更新fetchClues函数
const fetchClues = async () => {
  try {
    showPageLoading('正在加载线索列表...')
    const response = await api.get(\`/projects/\${projectId}/clues\`)
    clues.value = response
    hidePageLoading()
  } catch (error) {
    console.error('获取线索列表失败:', error)
    setPageError(error)
    ElMessage.error('获取线索列表失败')
  }
}
`

// 5. AIConfig.vue - 添加数据获取加载状态
export const aiConfigLoadingCode = `
// 更新fetchConfigs函数
const fetchConfigs = async () => {
  try {
    showPageLoading('正在加载AI配置...')
    const response = await api.get('/ai-configs')
    configs.value = response
    hidePageLoading()
  } catch (error) {
    setPageError(error)
    ElMessage.error('获取AI配置失败')
    console.error(error)
  }
}
`

// 6. PromptGenerator.vue - 添加数据获取加载状态
export const promptGeneratorLoadingCode = `
// 更新fetchTemplates函数
const fetchTemplates = async () => {
  try {
    showPageLoading('正在加载提示词模板...')
    const response = await promptTemplateAPI.getAllPromptTemplates()
    templates.value = response
    hidePageLoading()
  } catch (error) {
    console.error('获取模板列表失败:', error)
    setPageError(error)
    ElMessage.error('获取模板列表失败')
  }
}
`

// 通用的按钮加载状态代码
export const buttonLoadingCode = `
// 为按钮添加加载状态
<el-button 
  type="primary" 
  @click="handleSave"
  :loading="getOperationState('save').loading"
>
  保存
</el-button>

<el-button 
  type="danger" 
  @click="handleDelete"
  :loading="getOperationState('delete').loading"
>
  删除
</el-button>

<el-button 
  type="success" 
  @click="handleCreate"
  :loading="getOperationState('create').loading"
>
  创建
</el-button>
`

// 通用的操作函数包装代码
export const operationWrapperCode = `
// 包装操作函数
const handleSave = async () => {
  try {
    showOperationLoading('save', '正在保存...')
    // 原有的保存逻辑
    await saveData()
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error(error)
  } finally {
    hideOperationLoading('save')
  }
}

const handleDelete = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除吗？', '确认删除', {
      type: 'warning'
    })
    
    showOperationLoading('delete', '正在删除...')
    await deleteData(id)
    ElMessage.success('删除成功')
    await fetchData() // 重新获取数据
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  } finally {
    hideOperationLoading('delete')
  }
}

const handleCreate = async () => {
  try {
    showOperationLoading('create', '正在创建...')
    await createData()
    ElMessage.success('创建成功')
    await fetchData() // 重新获取数据
  } catch (error) {
    ElMessage.error('创建失败')
    console.error(error)
  } finally {
    hideOperationLoading('create')
  }
}
`

// 页面组件注册代码
export const componentRegistrationCode = `
// 在组件的components选项中添加
export default {
  name: 'YourComponent',
  components: {
    PageLoading,
    // 其他组件...
  },
  setup() {
    // setup代码...
  }
}
`

// 使用说明
export const usageInstructions = `
使用说明：

1. 页面包装器：
   - 在模板的最外层添加 <PageLoading page-key="page-name" @retry="fetchData">
   - 在模板结束前添加 </PageLoading>

2. 导入依赖：
   - import PageLoading from '@/components/PageLoading.vue'
   - import { usePageLoading, useOperationLoading } from '@/composables/useLoading'

3. 组件注册：
   - 在components选项中添加 PageLoading

4. 加载状态管理：
   - 在setup函数中添加加载状态管理代码
   - 在数据获取函数中添加页面加载状态
   - 在操作函数中添加操作加载状态

5. 按钮加载状态：
   - 为按钮添加 :loading="getOperationState('operation-key').loading"

6. 错误处理：
   - 在catch块中使用 setPageError(error) 显示错误状态
   - 提供重试功能
`

export default {
  charactersLoadingCode,
  chaptersLoadingCode,
  volumesLoadingCode,
  cluesLoadingCode,
  aiConfigLoadingCode,
  promptGeneratorLoadingCode,
  buttonLoadingCode,
  operationWrapperCode,
  componentRegistrationCode,
  usageInstructions
}
