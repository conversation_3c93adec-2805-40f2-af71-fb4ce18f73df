# AI小说创作助手

这是一个基于Vue3前端和Node.js后端的AI小说创作应用。

## 功能特点

- 提示词生成功能
- SQLite数据存储
- 项目管理
- 世界观设定
- 角色管理
- 大纲管理
- 章节管理
- 分卷剧情管理

## 安装和设置

### 前提条件

- Node.js (v14+)
- npm 或 yarn

### 安装依赖

1. 安装后端依赖

```bash
cd server
npm install
```

2. 安装前端依赖

```bash
cd client
npm install
```

### 设置数据库和默认用户

```bash
cd server
npm run setup
```

这将初始化数据库并创建默认用户：
- 用户名: admin
- 密码: Yinhai23

## 运行应用

1. 启动后端服务器

```bash
cd server
npm run dev
```

2. 启动前端开发服务器

```bash
cd client
npm run dev
```

3. 访问应用

打开浏览器，访问 http://localhost:3000

## 登录

使用以下默认凭据登录：
- 用户名: admin
- 密码: Yinhai23

## 项目结构

- `client/`: 前端Vue3应用
- `server/`: 后端Node.js/Express应用
  - `controllers/`: API控制器
  - `models/`: 数据库模型
  - `routes/`: API路由
  - `middleware/`: 中间件
  - `scripts/`: 脚本文件

## 许可证

[MIT](LICENSE)
