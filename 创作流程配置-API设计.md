# 创作流程配置 API 设计

## 1. 流程模板管理 API

### 1.1 获取流程模板列表
```
GET /api/workflows
Query参数:
- page: 页码 (默认1)
- limit: 每页数量 (默认20)
- category: 分类筛选
- search: 搜索关键词
- isPublic: 是否公开模板 (admin可查看所有)
- userId: 用户ID筛选 (admin可用)

响应:
{
  "data": [
    {
      "id": 1,
      "name": "小说创作标准流程",
      "description": "从构思到完成的完整创作流程",
      "category": "小说创作",
      "version": "1.0.0",
      "isActive": true,
      "isPublic": false,
      "thumbnail": "base64_image_data",
      "tags": ["小说", "创作", "标准"],
      "usageCount": 15,
      "userId": 1,
      "userName": "admin",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "limit": 20
}
```

### 1.2 获取流程模板详情
```
GET /api/workflows/:id

响应:
{
  "id": 1,
  "name": "小说创作标准流程",
  "description": "从构思到完成的完整创作流程",
  "category": "小说创作",
  "version": "1.0.0",
  "config": {
    "nodes": [
      {
        "id": "start_1",
        "type": "start",
        "name": "开始",
        "position": { "x": 100, "y": 100 },
        "config": {
          "inputFields": [
            {
              "name": "novelIdea",
              "label": "小说创意",
              "type": "textarea",
              "required": true
            }
          ]
        }
      },
      {
        "id": "ai_gen_1",
        "type": "ai_generation",
        "name": "生成大纲",
        "position": { "x": 300, "y": 100 },
        "config": {
          "aiConfigId": null,
          "promptTemplateId": 5,
          "inputMapping": {
            "novelIdea": "{{start_1.novelIdea}}"
          },
          "outputVariable": "outline"
        }
      }
    ],
    "connections": [
      {
        "id": "conn_1",
        "source": "start_1",
        "target": "ai_gen_1",
        "sourceHandle": "output",
        "targetHandle": "input"
      }
    ]
  },
  "nodes": [...], // 详细节点信息
  "connections": [...], // 详细连接信息
  "versions": [...], // 版本历史
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

### 1.3 创建流程模板
```
POST /api/workflows

请求体:
{
  "name": "我的创作流程",
  "description": "个人定制的创作流程",
  "category": "自定义",
  "config": {
    "nodes": [...],
    "connections": [...]
  },
  "tags": ["个人", "定制"],
  "isPublic": false
}

响应: 201 Created
{
  "id": 10,
  "message": "流程模板创建成功"
}
```

### 1.4 更新流程模板
```
PUT /api/workflows/:id

请求体: (同创建)

响应: 200 OK
{
  "message": "流程模板更新成功",
  "version": "1.1.0"
}
```

### 1.5 删除流程模板
```
DELETE /api/workflows/:id

响应: 200 OK
{
  "message": "流程模板删除成功"
}
```

## 2. 流程执行 API

### 2.1 启动流程执行
```
POST /api/workflows/:id/execute

请求体:
{
  "projectId": 1, // 可选，关联项目
  "inputData": {
    "novelIdea": "一个关于时间旅行的科幻小说..."
  }
}

响应: 201 Created
{
  "executionId": "exec_20240101_001",
  "status": "running",
  "currentNodeId": "start_1",
  "message": "流程执行已启动"
}
```

### 2.2 获取执行状态
```
GET /api/workflow-executions/:executionId

响应:
{
  "id": 1,
  "workflowId": 1,
  "executionId": "exec_20240101_001",
  "status": "running",
  "currentNodeId": "ai_gen_1",
  "inputData": {...},
  "outputData": {...},
  "executionLog": [
    {
      "nodeId": "start_1",
      "status": "completed",
      "timestamp": "2024-01-01T10:00:00Z",
      "message": "用户输入已收集"
    },
    {
      "nodeId": "ai_gen_1",
      "status": "running",
      "timestamp": "2024-01-01T10:01:00Z",
      "message": "正在生成大纲..."
    }
  ],
  "progress": 30,
  "startedAt": "2024-01-01T10:00:00Z",
  "estimatedCompletionAt": "2024-01-01T10:15:00Z"
}
```

### 2.3 继续执行（用户输入节点）
```
POST /api/workflow-executions/:executionId/continue

请求体:
{
  "nodeId": "user_input_1",
  "inputData": {
    "userFeedback": "请增加更多科幻元素..."
  }
}

响应: 200 OK
{
  "message": "执行已继续",
  "nextNodeId": "ai_gen_2"
}
```

### 2.4 暂停/取消执行
```
POST /api/workflow-executions/:executionId/pause
POST /api/workflow-executions/:executionId/cancel

响应: 200 OK
{
  "message": "执行已暂停/取消"
}
```

### 2.5 获取执行历史
```
GET /api/workflow-executions
Query参数:
- workflowId: 流程ID筛选
- status: 状态筛选
- projectId: 项目ID筛选
- page, limit: 分页

响应:
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 20
}
```

## 3. 流程分类管理 API

### 3.1 获取分类列表
```
GET /api/workflow-categories

响应:
{
  "data": [
    {
      "id": 1,
      "name": "小说创作",
      "description": "完整的小说创作流程",
      "icon": "book",
      "sortOrder": 1,
      "workflowCount": 15
    }
  ]
}
```

### 3.2 创建/更新/删除分类 (仅admin)
```
POST /api/workflow-categories
PUT /api/workflow-categories/:id
DELETE /api/workflow-categories/:id
```

## 4. 流程版本管理 API

### 4.1 获取版本历史
```
GET /api/workflows/:id/versions

响应:
{
  "data": [
    {
      "id": 1,
      "version": "1.2.0",
      "changeLog": "添加了角色发展节点",
      "isCurrent": true,
      "createdBy": "admin",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 4.2 创建新版本
```
POST /api/workflows/:id/versions

请求体:
{
  "changeLog": "优化了AI生成节点配置"
}

响应: 201 Created
{
  "version": "1.3.0",
  "message": "新版本创建成功"
}
```

### 4.3 回滚到指定版本
```
POST /api/workflows/:id/versions/:versionId/restore

响应: 200 OK
{
  "message": "已回滚到版本 1.1.0"
}
```

## 5. 流程导入导出 API

### 5.1 导出流程
```
GET /api/workflows/:id/export

响应: 200 OK
{
  "workflow": {...}, // 完整流程配置
  "exportedAt": "2024-01-01T00:00:00Z",
  "version": "1.0"
}
```

### 5.2 导入流程
```
POST /api/workflows/import

请求体:
{
  "workflow": {...}, // 流程配置
  "name": "导入的流程", // 可选，覆盖原名称
  "makePublic": false // 是否设为公开
}

响应: 201 Created
{
  "id": 20,
  "message": "流程导入成功"
}
```

## 6. 统计分析 API

### 6.1 获取流程使用统计
```
GET /api/workflows/statistics
Query参数:
- period: 统计周期 (day/week/month/year)
- workflowId: 特定流程ID

响应:
{
  "totalExecutions": 1500,
  "successRate": 85.5,
  "averageExecutionTime": 180000, // 毫秒
  "popularWorkflows": [...],
  "executionTrends": [...],
  "nodePerformance": [...]
}
```
