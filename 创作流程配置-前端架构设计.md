# 创作流程配置 - 前端架构设计

## 1. 技术栈选择

### 1.1 核心技术
- **Vue 3**: 主框架
- **Element Plus**: UI组件库
- **Vue Flow**: 流程图可视化组件 (替代方案: @vue-flow/core)
- **Pinia**: 状态管理
- **Vue Router**: 路由管理

### 1.2 可视化流程图库选择
推荐使用 **Vue Flow** (https://vueflow.dev/):
- 基于Vue 3开发，与项目技术栈匹配
- 支持拖拽、缩放、连接等交互
- 可自定义节点和连接样式
- 支持数据绑定和事件处理
- 活跃的社区和文档

## 2. 页面结构设计

### 2.1 主要页面
```
/workflow-designer          # 流程设计器主页
├── /templates              # 流程模板管理
├── /designer/:id           # 流程设计器
├── /executions             # 执行历史
├── /execution/:id          # 执行详情/监控
└── /categories             # 分类管理 (admin)
```

### 2.2 组件层次结构
```
WorkflowDesigner/
├── WorkflowTemplateList.vue      # 模板列表页
├── WorkflowDesigner.vue          # 流程设计器主页面
├── WorkflowExecution.vue         # 流程执行页面
├── WorkflowExecutionList.vue     # 执行历史列表
├── components/
│   ├── FlowCanvas/               # 流程画布组件
│   │   ├── FlowCanvas.vue        # 主画布
│   │   ├── NodePalette.vue       # 节点面板
│   │   ├── PropertyPanel.vue     # 属性配置面板
│   │   └── ToolBar.vue           # 工具栏
│   ├── Nodes/                    # 自定义节点组件
│   │   ├── StartNode.vue         # 开始节点
│   │   ├── AIGenerationNode.vue  # AI生成节点
│   │   ├── UserInputNode.vue     # 用户输入节点
│   │   ├── ConditionNode.vue     # 条件判断节点
│   │   └── EndNode.vue           # 结束节点
│   ├── Dialogs/                  # 对话框组件
│   │   ├── NodeConfigDialog.vue  # 节点配置对话框
│   │   ├── WorkflowSaveDialog.vue # 流程保存对话框
│   │   ├── ExecutionDialog.vue   # 执行配置对话框
│   │   └── ImportExportDialog.vue # 导入导出对话框
│   └── Execution/                # 执行相关组件
│       ├── ExecutionMonitor.vue  # 执行监控
│       ├── NodeExecutionStatus.vue # 节点执行状态
│       └── ExecutionLog.vue      # 执行日志
```

## 3. 状态管理设计

### 3.1 Pinia Store 结构
```javascript
// stores/workflow.js
export const useWorkflowStore = defineStore('workflow', {
  state: () => ({
    // 流程模板
    templates: [],
    currentTemplate: null,
    templateCategories: [],
    
    // 流程设计
    currentWorkflow: {
      id: null,
      name: '',
      description: '',
      category: '',
      nodes: [],
      connections: [],
      config: {}
    },
    
    // 执行状态
    executions: [],
    currentExecution: null,
    executionStatus: 'idle', // idle, running, completed, failed
    
    // UI状态
    selectedNode: null,
    selectedConnection: null,
    isDesigning: false,
    showPropertyPanel: true,
    showNodePalette: true
  }),
  
  actions: {
    // 模板管理
    async fetchTemplates(params = {}) {},
    async createTemplate(template) {},
    async updateTemplate(id, template) {},
    async deleteTemplate(id) {},
    
    // 流程设计
    addNode(node) {},
    updateNode(nodeId, updates) {},
    deleteNode(nodeId) {},
    addConnection(connection) {},
    deleteConnection(connectionId) {},
    
    // 流程执行
    async executeWorkflow(workflowId, inputData) {},
    async getExecutionStatus(executionId) {},
    async continueExecution(executionId, nodeId, inputData) {},
    async cancelExecution(executionId) {}
  }
})
```

## 4. 核心组件设计

### 4.1 流程设计器主组件
```vue
<!-- WorkflowDesigner.vue -->
<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <ToolBar 
      @save="saveWorkflow"
      @execute="executeWorkflow"
      @import="importWorkflow"
      @export="exportWorkflow"
    />
    
    <div class="designer-content">
      <!-- 左侧节点面板 -->
      <NodePalette 
        v-show="showNodePalette"
        @add-node="addNode"
      />
      
      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <FlowCanvas
          :nodes="currentWorkflow.nodes"
          :connections="currentWorkflow.connections"
          @node-select="selectNode"
          @connection-select="selectConnection"
          @node-update="updateNode"
          @connection-create="createConnection"
          @connection-delete="deleteConnection"
        />
      </div>
      
      <!-- 右侧属性面板 -->
      <PropertyPanel
        v-show="showPropertyPanel"
        :selected-node="selectedNode"
        :selected-connection="selectedConnection"
        @update-node="updateNode"
        @update-connection="updateConnection"
      />
    </div>
    
    <!-- 各种对话框 -->
    <NodeConfigDialog
      v-model="showNodeConfigDialog"
      :node="editingNode"
      @save="saveNodeConfig"
    />
  </div>
</template>
```

### 4.2 自定义节点组件
```vue
<!-- AIGenerationNode.vue -->
<template>
  <div class="ai-generation-node" :class="{ selected: isSelected }">
    <div class="node-header">
      <Icon name="robot" />
      <span>{{ node.name || 'AI生成' }}</span>
    </div>
    
    <div class="node-body">
      <div class="config-summary">
        <div v-if="node.config.aiConfigId">
          模型: {{ getAIConfigName(node.config.aiConfigId) }}
        </div>
        <div v-if="node.config.promptTemplateId">
          模板: {{ getTemplateName(node.config.promptTemplateId) }}
        </div>
      </div>
    </div>
    
    <div class="node-footer">
      <div class="node-handles">
        <Handle type="target" position="left" />
        <Handle type="source" position="right" />
      </div>
    </div>
    
    <!-- 执行状态指示器 -->
    <div v-if="executionStatus" class="execution-status" :class="executionStatus">
      <Icon :name="getStatusIcon(executionStatus)" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'

const props = defineProps({
  node: Object,
  isSelected: Boolean,
  executionStatus: String // pending, running, completed, failed
})

const getStatusIcon = (status) => {
  const icons = {
    pending: 'clock',
    running: 'loading',
    completed: 'check',
    failed: 'error'
  }
  return icons[status] || 'circle'
}
</script>
```

### 4.3 节点配置对话框
```vue
<!-- NodeConfigDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="`配置${nodeTypeNames[node?.type]}节点`"
    width="800px"
    @close="handleClose"
  >
    <div class="node-config-form">
      <!-- 基本信息 -->
      <el-form :model="form" label-width="120px">
        <el-form-item label="节点名称">
          <el-input v-model="form.name" placeholder="请输入节点名称" />
        </el-form-item>
        
        <el-form-item label="节点描述">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            placeholder="请输入节点描述"
          />
        </el-form-item>
      </el-form>
      
      <!-- AI生成节点特有配置 -->
      <div v-if="node?.type === 'ai_generation'" class="ai-config">
        <h4>AI配置</h4>
        
        <el-form :model="form.config" label-width="120px">
          <el-form-item label="AI模型">
            <el-select v-model="form.config.aiConfigId" placeholder="选择AI模型">
              <el-option
                v-for="config in aiConfigs"
                :key="config.id"
                :label="`${config.provider} - ${config.model}`"
                :value="config.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="提示词模板">
            <el-select v-model="form.config.promptTemplateId" placeholder="选择提示词模板">
              <el-option
                v-for="template in promptTemplates"
                :key="template.id"
                :label="template.name"
                :value="template.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="输入映射">
            <ParameterMapping
              v-model="form.config.inputMapping"
              :available-variables="availableVariables"
            />
          </el-form-item>
          
          <el-form-item label="输出变量">
            <el-input 
              v-model="form.config.outputVariable" 
              placeholder="输出变量名称"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 条件判断节点配置 -->
      <div v-if="node?.type === 'condition'" class="condition-config">
        <h4>条件配置</h4>
        <ConditionBuilder v-model="form.config.conditions" />
      </div>
      
      <!-- 用户输入节点配置 -->
      <div v-if="node?.type === 'user_input'" class="user-input-config">
        <h4>输入字段配置</h4>
        <InputFieldBuilder v-model="form.config.inputFields" />
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>
```

## 5. 流程执行界面设计

### 5.1 执行监控组件
```vue
<!-- ExecutionMonitor.vue -->
<template>
  <div class="execution-monitor">
    <div class="execution-header">
      <h3>{{ workflow.name }} - 执行监控</h3>
      <div class="execution-controls">
        <el-button 
          v-if="execution.status === 'running'" 
          @click="pauseExecution"
        >
          暂停
        </el-button>
        <el-button 
          v-if="execution.status === 'running'" 
          type="danger" 
          @click="cancelExecution"
        >
          取消
        </el-button>
      </div>
    </div>
    
    <!-- 执行进度 -->
    <div class="execution-progress">
      <el-progress 
        :percentage="executionProgress" 
        :status="getProgressStatus(execution.status)"
      />
      <div class="progress-info">
        <span>当前节点: {{ getCurrentNodeName() }}</span>
        <span>已用时间: {{ formatDuration(executionDuration) }}</span>
        <span v-if="estimatedCompletion">
          预计完成: {{ formatTime(estimatedCompletion) }}
        </span>
      </div>
    </div>
    
    <!-- 流程图显示（只读模式） -->
    <div class="execution-flow">
      <FlowCanvas
        :nodes="workflowNodes"
        :connections="workflowConnections"
        :readonly="true"
        :execution-status="nodeExecutionStatus"
      />
    </div>
    
    <!-- 执行日志 -->
    <div class="execution-log">
      <h4>执行日志</h4>
      <ExecutionLog :logs="execution.executionLog" />
    </div>
    
    <!-- 用户输入对话框 -->
    <UserInputDialog
      v-model="showUserInputDialog"
      :node="currentUserInputNode"
      @submit="continueExecution"
    />
  </div>
</template>
```

## 6. 数据流设计

### 6.1 流程设计数据流
```
用户操作 → 组件事件 → Store Action → API调用 → 状态更新 → 视图更新
```

### 6.2 流程执行数据流
```
启动执行 → WebSocket连接 → 实时状态更新 → 用户交互 → 继续执行 → 完成
```

### 6.3 WebSocket事件设计
```javascript
// WebSocket事件类型
const WS_EVENTS = {
  EXECUTION_STARTED: 'execution_started',
  NODE_STARTED: 'node_started',
  NODE_COMPLETED: 'node_completed',
  NODE_FAILED: 'node_failed',
  USER_INPUT_REQUIRED: 'user_input_required',
  EXECUTION_COMPLETED: 'execution_completed',
  EXECUTION_FAILED: 'execution_failed'
}
```

## 7. 性能优化策略

### 7.1 组件优化
- 使用 `v-memo` 优化大型流程图渲染
- 节点组件懒加载
- 虚拟滚动处理大量执行历史

### 7.2 数据优化
- 流程配置数据压缩存储
- 执行日志分页加载
- 图片资源懒加载

### 7.3 交互优化
- 防抖处理用户输入
- 乐观更新提升响应速度
- 离线状态处理

## 8. 可访问性设计

### 8.1 键盘导航
- 支持Tab键在节点间导航
- 支持方向键移动选中节点
- 支持快捷键操作（Ctrl+S保存等）

### 8.2 屏幕阅读器支持
- 为节点添加适当的ARIA标签
- 提供流程结构的文本描述
- 执行状态的语音提示

## 9. 国际化支持

### 9.1 多语言文件结构
```
locales/
├── zh-CN/
│   ├── workflow.json
│   └── execution.json
└── en-US/
    ├── workflow.json
    └── execution.json
```

### 9.2 动态内容国际化
- 节点类型名称
- 执行状态描述
- 错误信息提示
