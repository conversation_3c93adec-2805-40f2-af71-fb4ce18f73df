#!/bin/bash

# 定义路径
PROJECT_DIR="/mnt/data/xiaoshuo/xiaoshuozhushou-web"
LOG_DIR="$PROJECT_DIR/logs"
FRONTEND_LOG="$LOG_DIR/frontend.log"
BACKEND_LOG="$LOG_DIR/backend.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 进入项目目录
cd "$PROJECT_DIR" || { echo "无法进入项目目录: $PROJECT_DIR"; exit 1; }

# 执行 git pull 更新代码
echo "正在更新代码..."
git pull || { echo "git pull 失败"; exit 1; }

# 安装依赖
echo "安装项目依赖..."
npm install

# 杀死可能已经运行的进程
echo "停止已运行的进程..."
pkill -f "node.*client/node_modules/.bin/vite" || true
pkill -f "node.*server/app.js" || true

# 修改前端配置，确保监听0.0.0.0
echo "配置前端监听0.0.0.0..."
cd "$PROJECT_DIR/client" || { echo "无法进入前端目录"; exit 1; }
npm install

# 启动前端（后台运行并记录日志）
echo "启动前端..."
# 使用--host 0.0.0.0参数让Vite监听所有网络接口
nohup npm run dev -- --host 0.0.0.0 > "$FRONTEND_LOG" 2>&1 &
FRONTEND_PID=$!
echo "前端已启动，PID: $FRONTEND_PID，日志: $FRONTEND_LOG"

# 启动后端（后台运行并记录日志）
echo "启动后端..."
cd "$PROJECT_DIR/server" || { echo "无法进入后端目录"; exit 1; }
npm install

# 设置环境变量，让后端监听0.0.0.0
export HOST="0.0.0.0"
nohup npm run dev > "$BACKEND_LOG" 2>&1 &
BACKEND_PID=$!
echo "后端已启动，PID: $BACKEND_PID，日志: $BACKEND_LOG"

# 保存PID到文件，方便后续管理
echo "$FRONTEND_PID" > "$PROJECT_DIR/frontend.pid"
echo "$BACKEND_PID" > "$PROJECT_DIR/backend.pid"

echo "应用已成功启动！"
echo "前端日志: $FRONTEND_LOG"
echo "后端日志: $BACKEND_LOG"
echo "使用 'tail -f $FRONTEND_LOG' 或 'tail -f $BACKEND_LOG' 查看实时日志"

# 显示IP地址信息，方便用户访问
echo -e "\n可通过以下地址访问应用："
echo "本机IP地址："
ip addr | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | cut -d/ -f1 | while read -r ip; do
  echo "前端: http://$ip:3000"
  echo "后端: http://$ip:5001"
done