# AI大模型集成功能说明

## 功能概述

已成功为AI小说创作应用添加了大模型接口功能，支持在世界观设定等页面直接调用大模型API生成文本内容。

## 主要功能

### 1. 大模型配置管理
- **访问路径**: 主菜单 → 大模型配置
- **功能**: 
  - 添加、编辑、删除AI配置
  - 支持多个大模型配置
  - 设置默认配置
  - 测试连接功能
  - 配置参数：API密钥、模型名称、最大Token数、温度参数等

### 2. 支持的大模型
- **DeepSeek**: 已完整集成，支持deepseek-chat模型
- **扩展性**: 架构支持轻松添加其他大模型（OpenAI、Claude等）

### 3. AI生成功能
- **世界观设定页面**: 在编辑世界观设定时可以使用AI生成内容
- **提示词模板**: 支持选择预设的提示词模板
- **自定义要求**: 可以输入具体的生成要求
- **内容处理**: 支持替换现有内容或追加到现有内容

## 使用步骤

### 第一步：配置AI模型
1. 登录系统后，点击左侧菜单的"大模型配置"
2. 点击"添加配置"按钮
3. 填写配置信息：
   - **提供商**: 选择DeepSeek
   - **模型名称**: deepseek-chat（自动填充）
   - **API密钥**: 输入您的DeepSeek API密钥
   - **基础URL**: 可选，留空使用默认
   - **最大Token数**: 建议4000
   - **温度参数**: 建议0.7
   - **设为默认**: 勾选此项
4. 点击"保存"
5. 可以点击"测试连接"验证配置是否正确

### 第二步：在世界观设定中使用AI生成
1. 进入项目管理，选择一个项目
2. 点击"世界观设定"
3. 在右侧表单中：
   - 填写"标题"和选择"类别"
   - 点击"AI生成内容"按钮
4. 在弹出的对话框中：
   - 选择AI配置（通常自动选择默认配置）
   - **选择提示词模板**（推荐使用"世界观设定生成器"）
   - **配置模板参数**：
     - 必填参数：设定类别、设定标题
     - 可选参数：生成要求、现有世界观设定、项目信息等
     - 系统参数：可以选择项目中已有的世界观设定作为参考
   - 点击"生成内容"
5. 查看生成结果，选择"使用此内容"或"追加到现有内容"

#### 模板参数说明
- **系统参数**：从项目数据中选择，如现有的世界观设定、角色等
- **文本参数**：手动输入的文本内容
- **必填参数**：标记为必填的参数必须填写才能生成
- **多选参数**：可以选择多个项目数据作为参考

## 技术架构

### 后端架构
- **模型层**: `AIConfig`模型存储用户的AI配置
- **服务层**: `DeepSeekService`封装DeepSeek API调用
- **控制器层**: 
  - `aiConfigController`: 管理AI配置
  - `aiGenerationController`: 处理AI生成请求
- **路由**: 新增AI相关API端点

### 前端架构
- **配置页面**: `AIConfig.vue`用于管理AI配置
- **集成功能**: 在`WorldSettings.vue`中集成AI生成功能
- **API服务**: 扩展`api.js`添加AI相关API调用

### 数据库
- **新增表**: `ai_configs`存储用户的AI配置信息
- **字段**: 包括提供商、模型、API密钥、参数配置等

## API端点

### AI配置管理
- `GET /api/ai-configs` - 获取用户AI配置列表
- `POST /api/ai-configs` - 创建AI配置
- `PUT /api/ai-configs/:id` - 更新AI配置
- `DELETE /api/ai-configs/:id` - 删除AI配置
- `POST /api/ai-configs/:id/set-default` - 设置默认配置
- `POST /api/ai-configs/:id/test` - 测试配置连接

### AI生成
- `GET /api/ai-generation/configs` - 获取可用AI配置
- `POST /api/ai-generation/generate` - 通用内容生成
- `POST /api/ai-generation/world-setting` - 世界观设定生成

## 安全考虑

1. **API密钥保护**: 
   - 数据库中存储完整密钥
   - 前端显示时隐藏敏感信息
   - 仅用户本人可访问自己的配置

2. **权限控制**: 
   - 所有AI功能需要登录认证
   - 用户只能管理自己的AI配置

3. **参数验证**: 
   - 后端验证所有输入参数
   - API密钥有效性验证

## 扩展指南

### 添加新的大模型提供商
1. 在`services`目录下创建新的服务类（参考`DeepSeekService`）
2. 在`aiGenerationController`中添加对新提供商的支持
3. 在前端`AIConfig.vue`中添加新的提供商选项

### 在其他页面集成AI生成
1. 导入AI生成相关的API和组件
2. 添加AI生成按钮和对话框
3. 实现生成逻辑，调用相应的API端点

## 注意事项

1. **API密钥**: 请确保使用有效的DeepSeek API密钥
2. **网络连接**: AI生成功能需要稳定的网络连接
3. **Token限制**: 注意API的Token使用限制和费用
4. **生成质量**: AI生成的内容仅供参考，建议人工审核和编辑

## 故障排除

### 常见问题
1. **连接测试失败**: 检查API密钥是否正确，网络是否正常
2. **生成失败**: 检查Token数设置，确保提示词不会超出限制
3. **配置保存失败**: 检查必填字段是否完整

### 日志查看
- 后端日志会显示详细的API调用信息
- 前端控制台会显示错误信息

## 更新日志

### v1.1.0 (当前版本)
- ✅ 添加AI配置管理功能
- ✅ 集成DeepSeek大模型
- ✅ 在世界观设定页面添加AI生成功能
- ✅ **支持提示词模板参数化配置**
- ✅ **动态参数界面生成**
- ✅ **系统参数支持（从项目数据选择）**
- ✅ **与提示词生成器相同的参数替换逻辑**
- ✅ 完整的错误处理和用户反馈
- ✅ 专用的世界观设定生成模板

### v1.0.0
- ✅ 基础AI生成功能
- ✅ 支持自定义要求生成

### 计划功能
- 🔄 支持更多大模型（OpenAI、Claude等）
- 🔄 在其他页面（角色管理、章节管理等）集成AI生成
- 🔄 AI生成历史记录
- 🔄 批量生成功能
- 🔄 模板参数预设和保存
