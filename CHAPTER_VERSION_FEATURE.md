# 章节版本切换功能

## 功能概述

在小说预览模式下，用户现在可以查看和切换每个章节的不同版本，实现版本对比和内容演进的查看功能。

## 主要特性

### 🔄 版本切换
- **版本选择器**：在章节标题下方显示版本下拉选择器
- **实时切换**：选择不同版本后内容立即更新
- **版本信息**：显示版本号和创建时间
- **智能默认**：自动选择最新版本作为默认显示

### 📋 版本标识
- **目录徽章**：在章节目录中显示版本数量徽章
- **版本计数**：清晰显示每个章节有多少个版本
- **视觉区分**：有多版本的章节会有特殊标识

### 🎯 用户体验
- **无缝切换**：版本切换时自动滚动到顶部
- **状态保持**：切换章节时重置为最新版本
- **响应式设计**：适配桌面和移动设备

## 技术实现

### 后端改进

#### 1. API增强
```javascript
// 修改项目预览API，包含版本信息
const chapters = await Chapter.findAll({
  where: { projectId },
  order: [['order', 'ASC']],
  attributes: ['id', 'title', 'content', 'order', 'status'],
  include: [{
    model: ChapterVersion,
    as: 'versions',
    attributes: ['id', 'versionNumber', 'content', 'createdAt'],
    order: [['versionNumber', 'DESC']]
  }]
});
```

#### 2. 数据结构
```json
{
  "project": { "id": 1, "name": "项目名称" },
  "chapters": [
    {
      "id": 1,
      "title": "第一章",
      "content": "当前内容",
      "versions": [
        {
          "id": 1,
          "versionNumber": 3,
          "content": "版本3内容",
          "createdAt": "2025-01-26T10:00:00Z"
        },
        {
          "id": 2,
          "versionNumber": 2,
          "content": "版本2内容",
          "createdAt": "2025-01-25T10:00:00Z"
        }
      ]
    }
  ]
}
```

### 前端实现

#### 1. 响应式数据
```javascript
const currentVersionId = ref(null)
const currentChapterVersions = computed(() => {
  if (!currentChapter.value || !currentChapter.value.versions) {
    return []
  }
  return currentChapter.value.versions.sort((a, b) => b.versionNumber - a.versionNumber)
})
```

#### 2. 版本内容计算
```javascript
const currentVersionContent = computed(() => {
  if (!currentChapter.value) return ''
  
  // 如果选择了特定版本，使用版本内容
  if (currentVersionId.value && currentChapterVersions.value.length > 0) {
    const selectedVersion = currentChapterVersions.value.find(v => v.id === currentVersionId.value)
    if (selectedVersion) {
      return selectedVersion.content || ''
    }
  }
  
  // 否则使用章节的当前内容
  return currentChapter.value.content || ''
})
```

#### 3. 版本切换逻辑
```javascript
const handleVersionChange = (versionId) => {
  currentVersionId.value = versionId
  
  // 滚动到顶部以便查看新版本内容
  nextTick(() => {
    const contentEl = document.querySelector('.reader-content')
    if (contentEl) {
      contentEl.scrollTop = 0
    }
  })
}
```

## 界面设计

### 版本选择器
```vue
<div class="version-selector" v-if="currentChapterVersions.length > 1">
  <el-select 
    v-model="currentVersionId" 
    @change="handleVersionChange"
    size="small"
    style="width: 150px;"
  >
    <el-option
      v-for="version in currentChapterVersions"
      :key="version.id"
      :label="`版本 ${version.versionNumber}`"
      :value="version.id"
    >
      <span>版本 {{ version.versionNumber }}</span>
      <span style="float: right; color: #8492a6; font-size: 12px;">
        {{ formatVersionDate(version.createdAt) }}
      </span>
    </el-option>
  </el-select>
</div>
```

### 版本徽章
```vue
<span v-if="chapter.versions && chapter.versions.length > 1" class="version-badge">
  {{ chapter.versions.length }}版本
</span>
```

### CSS样式
```css
.chapter-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.version-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-badge {
  font-size: 10px;
  background: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  white-space: nowrap;
}
```

## 用户操作流程

### 1. 查看版本
1. 进入小说预览模式
2. 在章节目录中查看版本徽章
3. 点击有多版本的章节

### 2. 切换版本
1. 在章节标题下方找到版本选择器
2. 点击下拉菜单查看所有版本
3. 选择想要查看的版本
4. 内容自动更新并滚动到顶部

### 3. 版本信息
- **版本号**：显示版本的序号
- **创建时间**：显示版本的创建日期和时间
- **内容预览**：在选择器中可以看到版本的基本信息

## 响应式设计

### 桌面端
- 版本选择器在章节标题右侧
- 下拉菜单宽度固定为150px
- 版本徽章在章节标题右侧显示

### 移动端
- 版本选择器在章节标题下方
- 下拉菜单宽度100%
- 章节元信息垂直排列

```css
@media (max-width: 768px) {
  .chapter-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .version-selector {
    width: 100%;
  }

  .version-selector .el-select {
    width: 100%;
  }
}
```

## 功能特点

### 智能化
- **自动选择**：切换章节时自动选择最新版本
- **状态管理**：正确维护版本选择状态
- **内容同步**：版本切换时内容立即更新

### 用户友好
- **视觉提示**：清晰的版本徽章和选择器
- **操作便捷**：简单的下拉选择操作
- **信息丰富**：显示版本号和创建时间

### 性能优化
- **按需加载**：只有多版本章节才显示选择器
- **计算缓存**：使用computed属性优化性能
- **状态复用**：合理的状态管理避免重复请求

## 扩展功能建议

### 版本对比
- [ ] 并排显示两个版本的内容
- [ ] 高亮显示版本间的差异
- [ ] 提供版本差异统计

### 版本管理
- [ ] 版本备注和说明
- [ ] 版本标签和分类
- [ ] 版本恢复功能

### 协作功能
- [ ] 版本作者信息
- [ ] 版本评论和讨论
- [ ] 版本审核流程

## 测试验证

### 功能测试
- ✅ 版本选择器正确显示
- ✅ 版本切换内容更新
- ✅ 版本徽章正确计数
- ✅ 默认选择最新版本

### 界面测试
- ✅ 桌面端布局正确
- ✅ 移动端响应式适配
- ✅ 不同主题下样式正常
- ✅ 版本信息格式化正确

### 交互测试
- ✅ 切换章节重置版本选择
- ✅ 版本切换自动滚动到顶部
- ✅ 单版本章节不显示选择器
- ✅ 无版本章节正常显示

## 版本信息

- **功能版本**: v1.5
- **发布日期**: 2025-01-26
- **主要特性**: 章节版本切换、版本徽章、响应式设计
- **兼容性**: 向后兼容，支持无版本章节
