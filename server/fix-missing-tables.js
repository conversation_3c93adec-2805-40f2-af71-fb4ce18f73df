const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
const config = require('./config/config.json');

// 获取数据库配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];
const dbPath = path.resolve(__dirname, dbConfig.storage);

console.log(`正在修复数据库: ${dbPath}`);

// 读取SQL脚本
const sql = fs.readFileSync(path.join(__dirname, 'fix-database.sql'), 'utf8');

// 连接数据库并执行SQL
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    return console.error('无法连接到数据库:', err.message);
  }
  console.log('已连接到SQLite数据库');
  
  // 执行SQL脚本
  db.exec(sql, (err) => {
    if (err) {
      console.error('执行SQL失败:', err.message);
    } else {
      console.log('成功创建缺少的表！');
    }
    
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库连接失败:', err.message);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  });
});
