/**
 * 权限检查辅助函数
 * 用于简化控制器中的权限检查逻辑
 */

/**
 * 构建查询条件
 * @param {Object} user - 用户对象，包含id和role
 * @param {Object} baseCondition - 基础查询条件
 * @returns {Object} 完整的查询条件
 */
function buildWhereCondition(user, baseCondition = {}) {
  const { id: userId, role: userRole } = user;
  
  if (userRole === 'admin') {
    // admin用户可以访问所有数据
    return baseCondition;
  } else {
    // 普通用户只能访问自己创建的数据
    return { ...baseCondition, userId };
  }
}

/**
 * 检查项目访问权限
 * @param {Object} Project - 项目模型
 * @param {number} projectId - 项目ID
 * @param {Object} user - 用户对象
 * @returns {Promise<Object|null>} 项目对象或null
 */
async function checkProjectAccess(Project, projectId, user) {
  const whereCondition = buildWhereCondition(user, { id: projectId });
  return await Project.findOne({ where: whereCondition });
}

/**
 * 检查数据访问权限
 * @param {Object} Model - 数据模型
 * @param {number} id - 数据ID
 * @param {Object} user - 用户对象
 * @param {Object} includeOptions - 关联查询选项
 * @returns {Promise<Object|null>} 数据对象或null
 */
async function checkDataAccess(Model, id, user, includeOptions = []) {
  const whereCondition = buildWhereCondition(user, { id });
  return await Model.findOne({ 
    where: whereCondition,
    include: includeOptions
  });
}

/**
 * 获取项目相关数据列表
 * @param {Object} Model - 数据模型
 * @param {Object} Project - 项目模型
 * @param {number} projectId - 项目ID
 * @param {Object} user - 用户对象
 * @param {Object} options - 查询选项（order, include等）
 * @returns {Promise<{project: Object, data: Array}>} 项目和数据列表
 */
async function getProjectRelatedData(Model, Project, projectId, user, options = {}) {
  // 检查项目访问权限
  const project = await checkProjectAccess(Project, projectId, user);
  if (!project) {
    throw new Error('项目不存在或无权访问');
  }

  // 构建数据查询条件
  const dataWhereCondition = buildWhereCondition(user, { projectId });
  
  // 查询数据
  const data = await Model.findAll({
    where: dataWhereCondition,
    order: options.order || [['createdAt', 'ASC']],
    include: options.include || []
  });

  return { project, data };
}

/**
 * 创建项目相关数据
 * @param {Object} Model - 数据模型
 * @param {Object} Project - 项目模型
 * @param {Object} createData - 创建数据
 * @param {Object} user - 用户对象
 * @returns {Promise<Object>} 创建的数据对象
 */
async function createProjectRelatedData(Model, Project, createData, user) {
  const { projectId } = createData;
  
  // 检查项目访问权限
  const project = await checkProjectAccess(Project, projectId, user);
  if (!project) {
    throw new Error('项目不存在或无权访问');
  }

  // 创建数据，自动关联当前用户
  return await Model.create({
    ...createData,
    userId: user.id
  });
}

/**
 * 更新数据
 * @param {Object} Model - 数据模型
 * @param {number} id - 数据ID
 * @param {Object} updateData - 更新数据
 * @param {Object} user - 用户对象
 * @returns {Promise<Object>} 更新后的数据对象
 */
async function updateData(Model, id, updateData, user) {
  // 检查数据访问权限
  const data = await checkDataAccess(Model, id, user);
  if (!data) {
    throw new Error('数据不存在或无权修改');
  }

  // 更新数据
  await data.update(updateData);
  return data;
}

/**
 * 删除数据
 * @param {Object} Model - 数据模型
 * @param {number} id - 数据ID
 * @param {Object} user - 用户对象
 * @returns {Promise<boolean>} 删除成功返回true
 */
async function deleteData(Model, id, user) {
  // 检查数据访问权限
  const data = await checkDataAccess(Model, id, user);
  if (!data) {
    throw new Error('数据不存在或无权删除');
  }

  // 删除数据
  await data.destroy();
  return true;
}

module.exports = {
  buildWhereCondition,
  checkProjectAccess,
  checkDataAccess,
  getProjectRelatedData,
  createProjectRelatedData,
  updateData,
  deleteData
};
