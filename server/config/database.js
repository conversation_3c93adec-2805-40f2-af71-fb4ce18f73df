const path = require('path');

const dbDialect = process.env.DB_DIALECT || 'mysql'; // 默认使用 sqlite

const sqliteConfig = {
  dialect: 'sqlite',
  storage: path.join(__dirname, '../database/xiaoshuozhushou.sqlite'),
  logging: console.log, // 或者在生产环境中设置为 false
};

const mysqlConfig = {
  dialect: 'mysql',
  host: process.env.DB_HOST || 'cd-cdb-8yjrt2ao.sql.tencentcdb.com',
  port: process.env.DB_PORT || 27134,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'Zjy@2025',
  database: process.env.DB_NAME || 'xiaoshuo2',
  logging: console.log, // 或者在生产环境中设置为 false
  dialectOptions: {
    // MySQL 8.0+ 认证插件问题
    // authPlugins: {
    //  mysql_clear_password: () => () => Buffer.from(process.env.DB_PASSWORD + '\0')
    // }
  }
};

const config = {
  development: dbDialect === 'mysql' ? mysqlConfig : sqliteConfig,
  production: dbDialect === 'mysql' ? {
    ...mysqlConfig,
    logging: false, // 生产环境通常关闭日志
  } : {
    ...sqliteConfig,
    logging: false, // 生产环境通常关闭日志
  }
};

module.exports = config;
