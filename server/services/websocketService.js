/**
 * WebSocket服务
 * 用于实时推送流程执行状态更新
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

// JWT密钥，应该存储在环境变量中
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // 存储客户端连接，key为userId，value为WebSocket连接数组
    this.executionSubscriptions = new Map(); // 存储执行订阅，key为executionId，value为userId数组
  }

  // 初始化WebSocket服务器
  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws',
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log('WebSocket服务已启动');
  }

  // 验证客户端连接
  verifyClient(info) {
    try {
      const url = new URL(info.req.url, 'http://localhost');
      const token = url.searchParams.get('token');
      
      if (!token) {
        return false;
      }

      const decoded = jwt.verify(token, JWT_SECRET);
      info.req.user = decoded;
      return true;
    } catch (error) {
      console.error('WebSocket认证失败:', error.message);
      return false;
    }
  }

  // 处理新的WebSocket连接
  handleConnection(ws, req) {
    const userId = req.user.id;
    const username = req.user.username;
    
    console.log(`用户 ${username} (ID: ${userId}) 建立WebSocket连接`);

    // 存储客户端连接
    if (!this.clients.has(userId)) {
      this.clients.set(userId, []);
    }
    this.clients.get(userId).push(ws);

    // 设置连接属性
    ws.userId = userId;
    ws.username = username;
    ws.isAlive = true;

    // 发送连接成功消息
    this.sendToClient(ws, {
      type: 'connection',
      status: 'connected',
      message: '连接成功',
      timestamp: new Date().toISOString()
    });

    // 处理消息
    ws.on('message', (data) => {
      this.handleMessage(ws, data);
    });

    // 处理心跳
    ws.on('pong', () => {
      ws.isAlive = true;
    });

    // 处理连接关闭
    ws.on('close', () => {
      this.handleDisconnection(ws);
    });

    // 处理连接错误
    ws.on('error', (error) => {
      console.error(`WebSocket错误 (用户: ${username}):`, error.message);
      this.handleDisconnection(ws);
    });
  }

  // 处理客户端消息
  handleMessage(ws, data) {
    try {
      const message = JSON.parse(data);
      
      switch (message.type) {
        case 'subscribe_execution':
          this.subscribeExecution(ws, message.executionId);
          break;
          
        case 'unsubscribe_execution':
          this.unsubscribeExecution(ws, message.executionId);
          break;
          
        case 'ping':
          this.sendToClient(ws, { type: 'pong', timestamp: new Date().toISOString() });
          break;
          
        default:
          console.log(`未知消息类型: ${message.type}`);
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error.message);
      this.sendToClient(ws, {
        type: 'error',
        message: '消息格式错误',
        timestamp: new Date().toISOString()
      });
    }
  }

  // 处理连接断开
  handleDisconnection(ws) {
    const userId = ws.userId;
    const username = ws.username;
    
    console.log(`用户 ${username} (ID: ${userId}) 断开WebSocket连接`);

    // 从客户端列表中移除
    if (this.clients.has(userId)) {
      const connections = this.clients.get(userId);
      const index = connections.indexOf(ws);
      if (index > -1) {
        connections.splice(index, 1);
      }
      
      if (connections.length === 0) {
        this.clients.delete(userId);
      }
    }

    // 从执行订阅中移除
    for (const [executionId, subscribers] of this.executionSubscriptions.entries()) {
      const index = subscribers.indexOf(userId);
      if (index > -1) {
        subscribers.splice(index, 1);
        if (subscribers.length === 0) {
          this.executionSubscriptions.delete(executionId);
        }
      }
    }
  }

  // 订阅执行状态更新
  subscribeExecution(ws, executionId) {
    const userId = ws.userId;
    
    if (!this.executionSubscriptions.has(executionId)) {
      this.executionSubscriptions.set(executionId, []);
    }
    
    const subscribers = this.executionSubscriptions.get(executionId);
    if (!subscribers.includes(userId)) {
      subscribers.push(userId);
    }
    
    this.sendToClient(ws, {
      type: 'subscription_confirmed',
      executionId,
      message: `已订阅执行 ${executionId} 的状态更新`,
      timestamp: new Date().toISOString()
    });
    
    console.log(`用户 ${ws.username} 订阅了执行 ${executionId}`);
  }

  // 取消订阅执行状态更新
  unsubscribeExecution(ws, executionId) {
    const userId = ws.userId;
    
    if (this.executionSubscriptions.has(executionId)) {
      const subscribers = this.executionSubscriptions.get(executionId);
      const index = subscribers.indexOf(userId);
      if (index > -1) {
        subscribers.splice(index, 1);
        if (subscribers.length === 0) {
          this.executionSubscriptions.delete(executionId);
        }
      }
    }
    
    this.sendToClient(ws, {
      type: 'subscription_cancelled',
      executionId,
      message: `已取消订阅执行 ${executionId} 的状态更新`,
      timestamp: new Date().toISOString()
    });
    
    console.log(`用户 ${ws.username} 取消订阅了执行 ${executionId}`);
  }

  // 广播执行状态更新
  broadcastExecutionUpdate(executionId, updateData) {
    if (!this.executionSubscriptions.has(executionId)) {
      return;
    }
    
    const subscribers = this.executionSubscriptions.get(executionId);
    const message = {
      type: 'execution_update',
      executionId,
      data: updateData,
      timestamp: new Date().toISOString()
    };
    
    subscribers.forEach(userId => {
      this.sendToUser(userId, message);
    });
    
    console.log(`广播执行 ${executionId} 的状态更新给 ${subscribers.length} 个订阅者`);
  }

  // 发送消息给特定用户
  sendToUser(userId, message) {
    if (!this.clients.has(userId)) {
      return;
    }
    
    const connections = this.clients.get(userId);
    connections.forEach(ws => {
      this.sendToClient(ws, message);
    });
  }

  // 发送消息给特定客户端
  sendToClient(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('发送WebSocket消息失败:', error.message);
      }
    }
  }

  // 广播消息给所有连接的客户端
  broadcast(message) {
    this.wss.clients.forEach(ws => {
      this.sendToClient(ws, message);
    });
  }

  // 启动心跳检测
  startHeartbeat() {
    setInterval(() => {
      this.wss.clients.forEach(ws => {
        if (!ws.isAlive) {
          console.log(`移除无响应的WebSocket连接 (用户: ${ws.username})`);
          ws.terminate();
          return;
        }
        
        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // 每30秒检查一次
  }

  // 获取连接统计信息
  getStats() {
    return {
      totalConnections: this.wss ? this.wss.clients.size : 0,
      connectedUsers: this.clients.size,
      activeSubscriptions: this.executionSubscriptions.size,
      subscriptionDetails: Array.from(this.executionSubscriptions.entries()).map(([executionId, subscribers]) => ({
        executionId,
        subscriberCount: subscribers.length
      }))
    };
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

module.exports = websocketService;
