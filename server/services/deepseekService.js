const axios = require('axios');

class DeepSeekService {
  constructor(apiKey, baseUrl = 'https://api.deepseek.com/v1') {
    this.apiKey = apiKey;
    // 确保 baseUrl 有效
    this.baseUrl = baseUrl || 'https://api.deepseek.com/v1';

    console.log('DeepSeekService 初始化:', {
      apiKey: apiKey ? apiKey.substring(0, 10) + '...' : 'null',
      baseUrl: this.baseUrl
    });

    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });
  }

  /**
   * 调用DeepSeek聊天完成API
   * @param {Object} options - 请求选项
   * @param {string} options.model - 模型名称，默认为 'deepseek-chat'
   * @param {Array} options.messages - 消息数组
   * @param {number} options.max_tokens - 最大token数
   * @param {number} options.temperature - 温度参数
   * @param {boolean} options.stream - 是否流式输出
   * @returns {Promise<Object>} API响应
   */
  async chatCompletion(options = {}) {
    const {
      model = 'deepseek-chat',
      messages,
      max_tokens = 4000,
      temperature = 0.7,
      stream = false,
      ...otherOptions
    } = options;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('messages参数是必需的，且必须是非空数组');
    }

    try {
      const requestData = {
        model,
        messages,
        max_tokens,
        temperature,
        stream,
        ...otherOptions
      };

      console.log('发送DeepSeek API请求:', {
        model,
        messagesCount: messages.length,
        max_tokens,
        temperature
      });

      const response = await this.client.post('/chat/completions', requestData);
      
      console.log('DeepSeek API响应状态:', response.status);
      
      return response.data;
    } catch (error) {
      console.error('DeepSeek API调用失败:', error.response?.data || error.message);
      
      if (error.response) {
        // API返回了错误响应
        const { status, data } = error.response;
        throw new Error(`DeepSeek API错误 (${status}): ${data.error?.message || data.message || '未知错误'}`);
      } else if (error.request) {
        // 请求发送失败
        throw new Error('无法连接到DeepSeek API服务');
      } else {
        // 其他错误
        throw new Error(`DeepSeek API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 生成文本内容
   * @param {string} prompt - 提示词
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 生成的文本内容
   */
  async generateText(prompt, options = {}) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    const response = await this.chatCompletion({
      messages,
      ...options
    });

    if (response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    } else {
      throw new Error('DeepSeek API返回了空的响应');
    }
  }

  /**
   * 基于系统提示词和用户输入生成内容
   * @param {string} systemPrompt - 系统提示词
   * @param {string} userPrompt - 用户提示词
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 生成的文本内容
   */
  async generateWithSystemPrompt(systemPrompt, userPrompt, options = {}) {
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    const response = await this.chatCompletion({
      messages,
      ...options
    });

    if (response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    } else {
      throw new Error('DeepSeek API返回了空的响应');
    }
  }

  /**
   * 流式生成文本内容
   * @param {string} prompt - 提示词
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 处理每个数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async generateTextStream(prompt, options = {}, onChunk) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    return await this.chatCompletionStream({
      messages,
      ...options
    }, onChunk);
  }

  /**
   * 基于系统提示词和用户输入流式生成内容
   * @param {string} systemPrompt - 系统提示词
   * @param {string} userPrompt - 用户提示词
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 处理每个数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async generateWithSystemPromptStream(systemPrompt, userPrompt, options = {}, onChunk) {
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    return await this.chatCompletionStream({
      messages,
      ...options
    }, onChunk);
  }

  /**
   * 流式聊天完成API调用
   * @param {Object} options - 请求选项
   * @param {Function} onChunk - 处理每个数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async chatCompletionStream(options = {}, onChunk) {
    const {
      model = 'deepseek-chat',
      messages,
      max_tokens = 4000,
      temperature = 0.7,
      ...otherOptions
    } = options;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('messages参数是必需的，且必须是非空数组');
    }

    if (typeof onChunk !== 'function') {
      throw new Error('onChunk必须是一个函数');
    }

    try {
      const requestData = {
        model,
        messages,
        max_tokens,
        temperature,
        stream: true, // 启用流式输出
        ...otherOptions
      };

      console.log('发送DeepSeek流式API请求:', {
        model,
        messagesCount: messages.length,
        max_tokens,
        temperature
      });

      const response = await this.client.post('/chat/completions', requestData, {
        responseType: 'stream'
      });

      let fullContent = '';

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');

          for (const line of lines) {
            if (line.trim() === '') continue;
            if (line.trim() === 'data: [DONE]') {
              resolve(fullContent);
              return;
            }

            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.slice(6); // 移除 'data: ' 前缀
                const data = JSON.parse(jsonStr);

                if (data.choices && data.choices[0] && data.choices[0].delta) {
                  const content = data.choices[0].delta.content;
                  if (content) {
                    fullContent += content;
                    onChunk(content, fullContent);
                  }
                }
              } catch (parseError) {
                console.warn('解析流式数据失败:', parseError.message, 'Line:', line);
              }
            }
          }
        });

        response.data.on('end', () => {
          resolve(fullContent);
        });

        response.data.on('error', (error) => {
          console.error('DeepSeek流式API调用失败:', error);
          reject(new Error(`DeepSeek流式API调用失败: ${error.message}`));
        });
      });

    } catch (error) {
      console.error('DeepSeek流式API调用失败:', error.response?.data || error.message);

      if (error.response) {
        const { status, data } = error.response;
        throw new Error(`DeepSeek API错误 (${status}): ${data.error?.message || data.message || '未知错误'}`);
      } else if (error.request) {
        throw new Error('无法连接到DeepSeek API服务');
      } else {
        throw new Error(`DeepSeek API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 检查API密钥是否有效
   * @returns {Promise<boolean>} 是否有效
   */
  async validateApiKey() {
    try {
      const response = await this.chatCompletion({
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10
      });
      return true;
    } catch (error) {
      console.error('API密钥验证失败:', error.message);
      return false;
    }
  }
}

module.exports = DeepSeekService;
