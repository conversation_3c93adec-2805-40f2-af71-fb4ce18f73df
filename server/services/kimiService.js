const axios = require('axios');

class KimiService {
  constructor(apiKey, baseUrl = 'https://api.moonshot.cn/v1') {
    this.apiKey = apiKey;
    // 确保 baseUrl 有效
    this.baseUrl = baseUrl || 'https://api.moonshot.cn/v1';

    console.log('KimiService 初始化:', {
      apiKey: apiKey ? apiKey.substring(0, 10) + '...' : 'null',
      baseUrl: this.baseUrl
    });

    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });
  }

  /**
   * 调用Kimi聊天完成API
   * @param {Object} options - 请求选项
   * @param {string} options.model - 模型名称，默认为 'moonshot-v1-8k'
   * @param {Array} options.messages - 消息数组
   * @param {number} options.max_tokens - 最大token数
   * @param {number} options.temperature - 温度参数
   * @param {boolean} options.stream - 是否流式输出
   * @returns {Promise<Object>} API响应
   */
  async chatCompletion(options = {}) {
    const {
      model = 'moonshot-v1-8k',
      messages,
      max_tokens = 4000,
      temperature = 0.7,
      stream = false,
      ...otherOptions
    } = options;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('messages参数是必需的，且必须是非空数组');
    }

    try {
      // 处理消息内容长度限制
      const processedMessages = this.truncateMessages(messages, model);

      const requestData = {
        model,
        messages: processedMessages,
        max_tokens,
        temperature,
        stream,
        ...otherOptions
      };

      console.log('发送Kimi API请求:', {
        model,
        messagesCount: processedMessages.length,
        max_tokens,
        temperature,
        originalContentLength: messages.reduce((total, msg) => total + (msg.content?.length || 0), 0),
        processedContentLength: processedMessages.reduce((total, msg) => total + (msg.content?.length || 0), 0)
      });

      // 估算请求数据大小（避免JSON序列化循环引用问题）
      const estimatedLength = this.estimateRequestSize(processedMessages, model, max_tokens, temperature);
      console.log('估算请求数据长度:', estimatedLength);

      if (estimatedLength > 30000) { // 30KB限制，更保守
        throw new Error(`请求数据过大 (${Math.round(estimatedLength/1024)}KB)，请减少提示词内容长度`);
      }

      const response = await this.client.post('/chat/completions', requestData);
      
      console.log('Kimi API响应状态:', response.status);
      
      return response.data;
    } catch (error) {
      console.error('Kimi API调用失败:', error.response?.data || error.message);

      if (error.response) {
        // API返回了错误响应
        const { status, data } = error.response;
        console.error('API响应状态:', status);
        console.error('API响应数据:', data);

        let errorMessage = '未知错误';
        if (data) {
          if (typeof data === 'string') {
            errorMessage = data;
          } else if (data.error) {
            errorMessage = data.error.message || data.error.type || this.safeStringify(data.error);
          } else if (data.message) {
            errorMessage = data.message;
          } else {
            errorMessage = this.safeStringify(data);
          }
        }

        throw new Error(`Kimi API错误 (${status}): ${errorMessage}`);
      } else if (error.request) {
        // 请求发送失败
        throw new Error('无法连接到Kimi API服务');
      } else {
        // 其他错误
        throw new Error(`Kimi API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 生成文本内容
   * @param {string} prompt - 提示词
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 生成的文本内容
   */
  async generateText(prompt, options = {}) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    const response = await this.chatCompletion({
      messages,
      ...options
    });

    if (response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    } else {
      throw new Error('Kimi API返回了空的响应');
    }
  }

  /**
   * 基于系统提示词和用户输入生成内容
   * @param {string} systemPrompt - 系统提示词
   * @param {string} userPrompt - 用户提示词
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 生成的文本内容
   */
  async generateWithSystemPrompt(systemPrompt, userPrompt, options = {}) {
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    const response = await this.chatCompletion({
      messages,
      ...options
    });

    if (response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    } else {
      throw new Error('Kimi API返回了空的响应');
    }
  }

  /**
   * 验证API密钥是否有效
   * @returns {Promise<boolean>} 是否有效
   */
  async validateApiKey() {
    try {
      const response = await this.generateText('测试', {
        max_tokens: 10,
        temperature: 0.1
      });
      return !!response;
    } catch (error) {
      console.error('Kimi API密钥验证失败:', error.message);
      return false;
    }
  }

  /**
   * 安全地将对象转换为JSON字符串，避免循环引用错误
   * @param {any} obj - 要序列化的对象
   * @returns {string} JSON字符串
   */
  safeStringify(obj) {
    try {
      const seen = new WeakSet();
      return JSON.stringify(obj, (key, val) => {
        if (val != null && typeof val === 'object') {
          if (seen.has(val)) {
            return '[Circular]';
          }
          seen.add(val);
        }
        return val;
      });
    } catch (error) {
      return `[无法序列化: ${error.message}]`;
    }
  }

  /**
   * 估算请求数据大小，避免JSON序列化问题
   * @param {Array} messages - 消息数组
   * @param {string} model - 模型名称
   * @param {number} max_tokens - 最大token数
   * @param {number} temperature - 温度参数
   * @returns {number} 估算的字节数
   */
  estimateRequestSize(messages, model, max_tokens, temperature) {
    let totalSize = 0;

    // 基础字段大小估算
    totalSize += JSON.stringify({ model, max_tokens, temperature, stream: true }).length;

    // 消息内容大小估算
    for (const message of messages) {
      if (message.role) {
        totalSize += message.role.length + 20; // 包括JSON结构开销
      }
      if (message.content) {
        totalSize += message.content.length + 20; // 包括JSON结构开销
      }
    }

    // 添加JSON结构开销
    totalSize += 200; // 额外的JSON结构、引号、逗号等

    return totalSize;
  }

  /**
   * 截断消息内容以适应API限制
   * @param {Array} messages - 原始消息数组
   * @param {string} model - 模型名称
   * @returns {Array} 处理后的消息数组
   */
  truncateMessages(messages, model = 'moonshot-v1-8k') {
    // 根据模型设置不同的上下文长度限制，更保守的设置
    const contextLimits = {
      'moonshot-v1-8k': 4000,    // 8K模型，更保守的限制
      'moonshot-v1-32k': 20000,  // 32K模型，更保守的限制
      'moonshot-v1-128k': 80000  // 128K模型，更保守的限制
    };

    const maxContextLength = contextLimits[model] || 4000;

    // 计算当前总长度（粗略估算：1个中文字符≈1.5个token）
    let totalLength = 0;
    const processedMessages = [];

    // 从后往前处理消息，保留最新的对话
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const messageLength = Math.ceil((message.content?.length || 0) * 1.5);

      if (totalLength + messageLength > maxContextLength) {
        // 如果是第一条消息且过长，进行截断
        if (processedMessages.length === 0) {
          const maxLength = Math.floor(maxContextLength / 1.5);
          const truncatedContent = message.content.substring(0, maxLength) + '\n\n[内容过长已截断...]';
          processedMessages.unshift({
            ...message,
            content: truncatedContent
          });
          console.warn(`消息内容过长，已截断至${maxLength}字符`);
        }
        break;
      }

      processedMessages.unshift(message);
      totalLength += messageLength;
    }

    return processedMessages.length > 0 ? processedMessages : messages.slice(-1);
  }

  /**
   * 流式生成文本内容
   * @param {string} prompt - 提示词
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 接收数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async generateTextStream(prompt, options = {}, onChunk) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.chatCompletionStream(messages, options, onChunk);
  }

  /**
   * 基于系统提示词流式生成内容
   * @param {string} systemPrompt - 系统提示词
   * @param {string} userPrompt - 用户提示词
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 接收数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async generateWithSystemPromptStream(systemPrompt, userPrompt, options = {}, onChunk) {
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    return this.chatCompletionStream(messages, options, onChunk);
  }

  /**
   * 流式聊天完成
   * @param {Array} messages - 消息数组
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 接收数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async chatCompletionStream(messages, options = {}, onChunk) {
    const {
      model = 'moonshot-v1-8k',
      max_tokens = 4000,
      temperature = 0.7,
      ...otherOptions
    } = options;

    try {
      // 处理消息内容长度限制
      const processedMessages = this.truncateMessages(messages, model);

      const requestData = {
        model,
        messages: processedMessages,
        max_tokens,
        temperature,
        stream: true, // 启用流式输出
        ...otherOptions
      };

      console.log('发送Kimi流式API请求:', {
        model,
        messagesCount: processedMessages.length,
        max_tokens,
        temperature,
        originalContentLength: messages.reduce((total, msg) => total + (msg.content?.length || 0), 0),
        processedContentLength: processedMessages.reduce((total, msg) => total + (msg.content?.length || 0), 0)
      });

      // 估算请求数据大小（避免JSON序列化循环引用问题）
      const estimatedLength = this.estimateRequestSize(processedMessages, model, max_tokens, temperature);
      console.log('估算请求数据长度:', estimatedLength);

      if (estimatedLength > 30000) { // 30KB限制，更保守
        throw new Error(`请求数据过大 (${Math.round(estimatedLength/1024)}KB)，请减少提示词内容长度`);
      }

      const response = await this.client.post('/chat/completions', requestData, {
        responseType: 'stream'
      });

      let fullContent = '';

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();
              
              if (data === '[DONE]') {
                resolve(fullContent);
                return;
              }
              
              try {
                const parsed = JSON.parse(data);
                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                  const content = parsed.choices[0].delta.content;
                  if (content) {
                    fullContent += content;
                    if (onChunk) {
                      onChunk(content, fullContent);
                    }
                  }
                }
              } catch (parseError) {
                console.warn('解析Kimi流式响应失败:', parseError.message);
              }
            }
          }
        });

        response.data.on('end', () => {
          resolve(fullContent);
        });

        response.data.on('error', (error) => {
          console.error('Kimi流式API错误:', error.message || '未知错误');
          reject(new Error(`Kimi流式API调用失败: ${error.message || '未知错误'}`));
        });
      });
    } catch (error) {
      // 安全地记录错误信息，避免循环引用
      console.error('Kimi流式API调用失败:', error.message || '未知错误');

      if (error.response) {
        const { status, data } = error.response;
        console.error('API响应状态:', status);

        // 安全地记录响应数据
        if (data && typeof data === 'object') {
          console.error('API响应数据:', data.error?.message || data.message || `[对象数据，状态码: ${status}]`);
        } else {
          console.error('API响应数据:', data);
        }

        let errorMessage = '未知错误';
        if (data) {
          if (typeof data === 'string') {
            errorMessage = data;
          } else if (data && data.error) {
            errorMessage = data.error.message || data.error.type || '服务器错误';
          } else if (data && data.message) {
            errorMessage = data.message;
          } else {
            errorMessage = `HTTP ${status} 错误`;
          }
        }

        throw new Error(`Kimi API错误 (${status}): ${errorMessage}`);
      } else if (error.request) {
        throw new Error('无法连接到Kimi API服务');
      } else {
        throw new Error(`Kimi API调用失败: ${error.message || '未知错误'}`);
      }
    }
  }
}

module.exports = KimiService;
