const jwt = require('jsonwebtoken');
const { User } = require('../models');

// JWT密钥，应该存储在环境变量中
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 验证JWT令牌的中间件
exports.authenticate = async (req, res, next) => {
  try {
    // 从请求头获取令牌
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: '未提供认证令牌' });
    }

    const token = authHeader.split(' ')[1];

    // 验证令牌
    const decoded = jwt.verify(token, JWT_SECRET);

    // 查找用户
    const user = await User.findByPk(decoded.userId || decoded.id, {
      attributes: { exclude: ['password'] } // 排除密码字段
    });

    if (!user) {
      return res.status(401).json({ message: '用户不存在' });
    }

    // 将用户信息添加到请求对象
    req.user = user;

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: '令牌已过期' });
    }
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: '无效的令牌' });
    }
    console.error('认证错误:', error);
    res.status(500).json({ message: '认证失败', error: error.message });
  }
};

// 检查用户角色的中间件
exports.checkRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: '未授权' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: '没有权限执行此操作' });
    }

    next();
  };
};
