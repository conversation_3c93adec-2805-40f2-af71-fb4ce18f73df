const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
const config = require('./config/config.json');

// 获取当前环境的配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 数据库文件路径
const dbPath = path.resolve(__dirname, dbConfig.storage);

// 读取 SQL 文件内容
const sqlFilePath = path.join(__dirname, 'create-tables.sql');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

// 连接到数据库并执行 SQL
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('无法连接到数据库:', err.message);
    return;
  }
  console.log('已连接到 SQLite 数据库');
  
  // 执行 SQL 语句
  db.exec(sqlContent, (err) => {
    if (err) {
      console.error('执行 SQL 失败:', err.message);
    } else {
      console.log('成功创建表');
    }
    
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库连接失败:', err.message);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  });
});
