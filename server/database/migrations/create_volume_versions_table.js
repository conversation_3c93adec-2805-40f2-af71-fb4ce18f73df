const { Sequelize } = require('sequelize');
const config = require('../../config/database');
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(dbConfig);

async function up() {
  try {
    // 创建 volume_versions 表
    if (dbConfig.dialect === 'mysql') {
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS volume_versions (
          id INT AUTO_INCREMENT PRIMARY KEY,
          volumeId INT NOT NULL,
          versionNumber INT NOT NULL,
          title VARCHAR(255) NOT NULL,
          summary TEXT NOT NULL,
          wordCount INT DEFAULT 0,
          chapterCount INT DEFAULT 0,
          createdAt DATETIME NOT NULL,
          updatedAt DATETIME NOT NULL,
          FOREIGN KEY (volumeId) REFERENCES Volumes(id) ON DELETE CASCADE
        );
      `);
    } else {
      // SQLite
      await sequelize.query(`
        CREATE TABLE IF NOT EXISTS "volume_versions" (
          "id" INTEGER PRIMARY KEY AUTOINCREMENT,
          "volumeId" INTEGER NOT NULL,
          "versionNumber" INTEGER NOT NULL,
          "title" VARCHAR(255) NOT NULL,
          "summary" TEXT NOT NULL,
          "wordCount" INTEGER DEFAULT 0,
          "chapterCount" INTEGER DEFAULT 0,
          "createdAt" DATETIME NOT NULL,
          "updatedAt" DATETIME NOT NULL,
          FOREIGN KEY ("volumeId") REFERENCES "Volumes" ("id") ON DELETE CASCADE
        );
      `);
    }

    console.log('volume_versions 表创建成功');
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
up().then(() => {
  console.log('迁移完成');
  process.exit(0);
}).catch(error => {
  console.error('迁移失败:', error);
  process.exit(1);
});
