const { Sequelize } = require('sequelize');
const config = require('../../config/database');
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(dbConfig);

async function up() {
  try {
    // 检查 userId 字段是否已存在 (MySQL语法)
    const [results] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'VolumeChapterGroups' 
      AND COLUMN_NAME = 'userId';
    `);
    
    const hasUserIdField = results.length > 0;
    
    if (!hasUserIdField) {
      // 添加 userId 字段到 VolumeChapterGroups 表
      await sequelize.query(`
        ALTER TABLE \`VolumeChapterGroups\` ADD COLUMN \`userId\` INTEGER NOT NULL DEFAULT 1;
      `);
      
      console.log('userId 字段已添加到 VolumeChapterGroups 表');
      
      // 为现有记录设置默认的 userId (假设用户ID为1)
      await sequelize.query(`
        UPDATE \`VolumeChapterGroups\` SET \`userId\` = 1 WHERE \`userId\` IS NULL;
      `);
      
      console.log('现有章节分组记录已设置默认 userId');
    } else {
      console.log('userId 字段已存在于 VolumeChapterGroups 表中');
    }

  } catch (error) {
    console.error('添加 userId 字段失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
up().then(() => {
  console.log('迁移完成');
  process.exit(0);
}).catch(error => {
  console.error('迁移失败:', error);
  process.exit(1);
});
