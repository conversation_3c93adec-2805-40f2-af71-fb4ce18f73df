const { Sequelize } = require('sequelize');
const config = require('../../config/database');
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(dbConfig);

async function up() {
  try {
    // 创建 Volumes 表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS "Volumes" (
        "id" INTEGER PRIMARY KEY AUTOINCREMENT,
        "projectId" INTEGER NOT NULL,
        "title" VARCHAR(255) NOT NULL,
        "summary" TEXT NOT NULL,
        "wordCount" INTEGER DEFAULT 0,
        "chapterCount" INTEGER DEFAULT 0,
        "order" INTEGER DEFAULT 0,
        "createdAt" DATETIME NOT NULL,
        "updatedAt" DATETIME NOT NULL,
        FOREIGN KEY ("projectId") REFERENCES "Projects" ("id") ON DELETE CASCADE
      );
    `);

    // 创建 VolumeChapterGroups 表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS "VolumeChapterGroups" (
        "id" INTEGER PRIMARY KEY AUTOINCREMENT,
        "volumeId" INTEGER NOT NULL,
        "title" VARCHAR(255) NOT NULL,
        "chapterRange" VARCHAR(255) NOT NULL,
        "summary" TEXT NOT NULL,
        "progressRate" INTEGER DEFAULT 0,
        "order" INTEGER DEFAULT 0,
        "createdAt" DATETIME NOT NULL,
        "updatedAt" DATETIME NOT NULL,
        FOREIGN KEY ("volumeId") REFERENCES "Volumes" ("id") ON DELETE CASCADE
      );
    `);

    console.log('Volumes 和 VolumeChapterGroups 表创建成功');
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
up().then(() => {
  console.log('迁移完成');
  process.exit(0);
}).catch(error => {
  console.error('迁移失败:', error);
  process.exit(1);
});
