const { Sequelize } = require('sequelize');
const config = require('../../config/database');
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(dbConfig);

async function up() {
  try {
    // 添加能力设定字段
    await sequelize.query(`
      ALTER TABLE "Characters" ADD COLUMN "abilities_detail" TEXT;
    `);

    // 添加是否主角团字段
    await sequelize.query(`
      ALTER TABLE "Characters" ADD COLUMN "isMainCharacter" BOOLEAN DEFAULT false;
    `);

    // 添加重要程度字段
    await sequelize.query(`
      ALTER TABLE "Characters" ADD COLUMN "importance" INTEGER DEFAULT 50 CHECK("importance" BETWEEN 1 AND 100);
    `);

    console.log('Characters 表字段添加成功');
  } catch (error) {
    console.error('迁移失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 执行迁移
up().then(() => {
  console.log('迁移完成');
  process.exit(0);
}).catch(error => {
  console.error('迁移失败:', error);
  process.exit(1);
});
