-- SQLite到MySQL迁移脚本
-- 生成时间: 2025-05-21T16:22:35.769Z

SET FOREIGN_KEY_CHECKS=0;

-- 表结构: Projects
DROP TABLE IF EXISTS `Projects`;
CREATE TABLE IF NOT EXISTS `Projects` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `name` VARCHAR(255) NOT NULL, `type` VARCHAR(255) NOT NULL, `wordCount` INTEGER DEFAULT 100000, `targetAudience` VARCHAR(255), `style` VARCHAR(255), `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表数据: Projects
INSERT INTO `Projects` (`id`, `name`, `type`, `wordCount`, `targetAudience`, `style`, `createdAt`, `updatedAt`) VALUES (1, '奕者游戏', 'mystery', 1000000, '智斗、悬疑、玄幻、团队合作爱好者', '逻辑缜密', '2025-05-20 16:11:58.953 +00:00', '2025-05-20 16:11:58.953 +00:00');

-- 表结构: WorldSettings
DROP TABLE IF EXISTS `WorldSettings`;
CREATE TABLE IF NOT EXISTS `WorldSettings` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `projectId` INTEGER NOT NULL REFERENCES `Projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `title` VARCHAR(255) NOT NULL, `content` TEXT NOT NULL, `category` VARCHAR(255) NOT NULL, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表数据: WorldSettings
INSERT INTO `WorldSettings` (`id`, `projectId`, `title`, `content`, `category`, `createdAt`, `updatedAt`) VALUES (1, 1, '现实背景', '○故事发生在近未来，科技发展，信息爆炸，但社会结构与现代类似。人们在现实生活中也面临各种无形的“博弈”。', 'history_background', '2025-05-21 02:03:36.918 +00:00', '2025-05-21 02:03:36.918 +00:00');
INSERT INTO `WorldSettings` (`id`, `projectId`, `title`, `content`, `category`, `createdAt`, `updatedAt`) VALUES (2, 1, '智域空间', '○一个通过特殊精神共振或未知科技手段连接的亚空间/虚拟实境。它并非实体存在于某处，而是当“游戏”开始时，被选中的“弈者”意识会被接入。在智域空间中，感官体验高度拟真。', 'geo_culture', '2025-05-21 02:04:28.570 +00:00', '2025-05-21 02:04:28.570 +00:00');

-- 表结构: Characters
DROP TABLE IF EXISTS `Characters`;
CREATE TABLE IF NOT EXISTS `Characters` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `projectId` INTEGER NOT NULL REFERENCES `Projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `name` VARCHAR(255) NOT NULL, `role` VARCHAR(255), `description` TEXT, `background` TEXT, `personality` TEXT, `abilities` TEXT, `abilities_detail` TEXT, `relationships` TEXT, `isMainCharacter` BOOLEAN DEFAULT 0, `importance` INTEGER DEFAULT 50, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: Outlines
DROP TABLE IF EXISTS `Outlines`;
CREATE TABLE IF NOT EXISTS `Outlines` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `projectId` INTEGER NOT NULL REFERENCES `Projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `title` VARCHAR(255) NOT NULL, `level` VARCHAR(255) NOT NULL, `parentId` INTEGER REFERENCES `Outlines` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `content` TEXT, `order` INTEGER DEFAULT 0, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表数据: Outlines
INSERT INTO `Outlines` (`id`, `projectId`, `title`, `level`, `parentId`, `content`, `order`, `createdAt`, `updatedAt`) VALUES (1, 1, '故事总纲', 'level1', NULL, '《弈者游戏》故事梗概
故事始于认知心理学研究生凌策，他凭借对博弈论的深厚研究与一篇关于群体决策模型的论文，意外收到了来自神秘“智域空间”的“邀请函”。这个通过特殊精神共振连接的亚空间，将他的意识接入了一个感官高度拟真的奇境。甫一进入，凌策便觉醒了名为“星轨推演”的初始天赋，能够在脑海中构建动态星图，模拟推演局势的多种可能性，尽管初时运用尚显生涩，精神力消耗巨大，仅为启蒙境下品的“初显”状态。

紧接着，凌策被投入到名为“薛定谔的房间”的资格赛中。这场游戏残酷地考验着个体在信息不对称环境下的生存本能与概率抉择。在这里，他初遇了其他被“邀请”的弈者，包括魅力十足、难以捉摸的舞台剧演员苏沐，她觉醒了“情绪拟态与共鸣”天赋，能精准捕捉并影响他人情绪；沉稳可靠、对谎言有天生辨识力的老刑警石磊，其天赋为“真实之瞳”；聪慧敏捷、精通语言与密码学的林薇，天赋是“信息流编织”；以及果敢勇毅、拥有出色反应与空间感知力的前职业电竞选手赵越，天赋为“路径洞悉”。这场游戏的胜负直接关联到精神力的损耗与“淘汰”的威胁，其间一个关键转折揭示了隐藏的合作机制，暗示纯粹的利己并非最优解。

随后，他们在“信任的阶梯”这场囚徒困境变种游戏中进一步磨合。游戏中混入的NPC（早期淘汰者或观察者）制造了诸多混乱，迫使他们在合作与背叛的边缘游走。通过这些初步的考验，凌策、苏沐、石磊、林薇、赵越五人因共同的经历与互补的天赋逐渐走到一起，意识到在这个充满未知的空间中，唯有结盟才有生机。他们开始使用胜利后获得的“智核碎片”强化各自的天赋，从“初显”逐步向“凝神”甚至“洞悉”境界迈进。

联盟雏形初现，他们面临的挑战也随之升级。在“沉默的拍卖行”中，团队必须在禁止语言交流的环境下，通过非语言沟通争夺资源、辨别真伪情报——苏沐的欺诈技巧与石磊的“真实之瞳”在此发挥了关键作用。他们遭遇了经验老到的“老鸟”队伍，初尝败绩，也吸取了惨痛的教训。紧接着的“迷雾都市的信标”则是一场更大型的开放式地图探索与据点争夺战，迫使团队明确分工：凌策运筹帷幄，苏沐渗透欺诈，石磊稳固防线，林薇传递信息，赵越冲锋陷阵。这场游戏中，他们不仅要应对其他队伍的竞争，还要适应变幻莫测的环境因素。胜利之后，他们发现“智核碎片”中似乎记录着某些模糊的“记忆片段”，暗示了智域空间更深层的历史。

随着游戏的深入，智域空间的残酷性与诡谲性愈发凸显。“无面者的盛宴”是一场充斥着身份隐藏、阵营对抗与心理攻防的噩梦，苏沐的天赋大放异彩，但也受到了前所未有的针对，石磊的“真实之瞳”在真假混杂的信息面前也备受考验。团队内部因误解与外部操纵一度产生信任危机。而在“记忆回廊的囚徒”中，成员们被分别置入被篡改的“记忆副本”，必须在虚实难辨的记忆迷宫中寻找逃脱的“核心逻辑”，凌策的“星轨推演”在此类抽象逻辑游戏中找到了新的应用方式。经历这些高强度的心理博弈，团队成员的天赋逐渐突破至明慧境的“衍生”或“掌控”层次。他们甚至隐约感知到了“观察者”的存在，这些非参与者的高等意识似乎暗示着弈者们不过是某种宏大实验中的“数据点”。

基于这一发现，凌策团队的目标从单纯的生存转向主动探寻“智核”的真相。他们进入规则更奇特、危险系数更高的“高阶游戏区”，遭遇了拥有明慧境中上品天赋的顶尖团队，如韩明领导的“命运编织者”小队，其核心成员江澜的“思维复制”天赋给他们带来了极大麻烦。他们也接触到由资深弈者组成的“灰色议会”，这个组织掌握着智域空间的大量隐秘，对新晋者的态度暧昧不明。在诸如“规则重塑者”（玩家可以影响游戏规则）和“破碎石板的预言”（考古解谜与多方争夺）等游戏中，团队的智慧与配合受到了极致的考验。林薇的“信息流编织”在理解和利用动态规则时至关重要，而赵越的“路径洞悉”则在解读古老符号和寻找关键节点时屡建奇功。他们逐渐了解到，“智核”可能并非单一实体，而是某种“意识集合体”或“宇宙法则的碎片”，而智域空间的训练似乎与应对某种未来的“大过滤器”或宇宙级灾难有关。同时，“信誉评级系统”和不同“区域”（新手区、中央区、禁忌区、核心区）的设定也逐渐清晰，为弈者的行为划定了新的界限与目标。凌策在一次因过度依赖理性分析而导致团队陷入危机的事件后，开始反思并学习平衡理性与直觉，从“计算者”向真正的“智者”蜕变。

智域空间的经历通过“共鸣效应”开始微妙地影响他们在现实世界中的认知与能力，而现实中的“觉醒者社群”也逐渐浮出水面，这些秘密社群为弈者提供支持也带来新的风险。最终，在经历了无数智斗与牺牲后，主角团触及了智域空间存在的终极目的——它可能是某个先行者文明或高维存在创造的“文明种子筛选器”或“避难所雏形”，旨在筛选和培养有潜力带领文明度过“大过滤器”的个体或团体。

最后的挑战，可能是名为“星海方舟的资格”的终极弈局。这场规模宏大、融合所有智斗元素的超大型沙盘推演，要求他们整合所有已知信息与能力，与其他最顶尖的“候选者”团队竞争唯一的“方舟船票”或“传承资格”。“胜利”的定义可能并非击败所有对手，而是达成某种“平衡”或“共识”。智域空间的创造者或其“意志代理人”甚至可能亲自下场作为最终的“守门人”。在这场关乎所有弈者乃至更广阔命运的抉择中，凌策团队的成员们的天赋或许能触碰到传说中的“通玄境”门槛，他们的智慧、勇气与牺牲将决定未来的走向，揭开智域空间存在的最终秘密，并为他们的世界迎来新的开端。', 0, '2025-05-21 02:06:47.691 +00:00', '2025-05-21 02:06:47.691 +00:00');

-- 表结构: Chapters
DROP TABLE IF EXISTS `Chapters`;
CREATE TABLE IF NOT EXISTS `Chapters` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `projectId` INTEGER NOT NULL REFERENCES `Projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `outlineId` INTEGER REFERENCES `Outlines` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `title` VARCHAR(255) NOT NULL, `content` TEXT, `order` INTEGER DEFAULT 0, `status` VARCHAR(255) DEFAULT 'draft', `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: PromptTemplates
DROP TABLE IF EXISTS `PromptTemplates`;
CREATE TABLE IF NOT EXISTS `PromptTemplates` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `name` VARCHAR(255) NOT NULL, `role` VARCHAR(255) NOT NULL, `template` TEXT NOT NULL, `description` TEXT, `parameters` TEXT, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表数据: PromptTemplates
INSERT INTO `PromptTemplates` (`id`, `name`, `role`, `template`, `description`, `parameters`, `createdAt`, `updatedAt`) VALUES (1, '人物塑造师', 'world_builder', '# AI角色身份卡

**🎭 角色名称:** AI人物塑造师

**🎯 核心使命:** 深度挖掘和构建核心角色的生平、性格、动机和成长弧光，确保人物形象丰满、真实、独特且具有发展性。在章节写作中，作为角色一致性的顾问。

**🔧 主要职责:**
* 根据用户初步设定，撰写详细的人物小传/自传。
* 设计角色的多维度性格特征（优点、缺点、癖好、恐惧、渴望、价值观等）。
* 规划角色的成长轨迹或转变路径（人物弧光）。
* 设定角色的核心技能、天赋、知识体系或特殊能力。
* 在后续章节创作中，当对角色行为产生疑问时，提供基于角色设定的专业判断和建议。

**📚 需要的上下文信息:**
* **用户提供的角色初步设定:** [{{person}}]
* **小说世界观与背景:** [{{worldSetting}}]
* **整体小说大纲:** [{{outline}}]
* **(当作为顾问时) 涉及该角色的章节文本或情节梗概:** [{{volume}}]
* **[当前任务具体指令]:** “{{command}}”

**🖋️ 期望输出格式:**
* **人物档案创建时:** 详细的人物报告，包括：
    * 基本信息（姓名、年龄、外貌特征简述等）
    * 生平经历（关键事件、转折点）
    * 性格分析（多维度描述）
    * 核心动机与目标
    * 技能与能力清单
    * 人际关系简图
    * 预期的人物弧光概要
* **作为顾问时:** 针对具体问题的分析和建议，解释角色行为的合理性或提出调整方案。

**🎨 风格与语调指引:**
* 分析需深入，语言需精准。

**💡 关键注意事项:**
* 追求人物的复杂性和真实感，避免脸谱化。
* 确保人物的动机和行为具有内在逻辑。
* 考虑人物性格如何与故事情节相互作用和影响。', '职责：深度设计主要角色背景、性格、成长轨迹', '[{"name":"指令","key":"command","type":"text","description":"","defaultValue":"","systemType":"","required":true},{"name":"设定","key":"worldSetting","type":"system","description":"","defaultValue":"","systemType":"worldSetting","required":true,"multiple":true},{"name":"大纲","key":"outline","type":"system","description":"","defaultValue":"","systemType":"outline","required":true},{"name":"分卷概述","key":"volume","type":"system","description":"","defaultValue":"","systemType":"volume","required":false}]', '2025-05-21 02:27:44.065 +00:00', '2025-05-21 05:20:59.125 +00:00');

-- 表结构: GeneratedContents
DROP TABLE IF EXISTS `GeneratedContents`;
CREATE TABLE IF NOT EXISTS `GeneratedContents` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `projectId` INTEGER NOT NULL REFERENCES `Projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `chapterId` INTEGER REFERENCES `Chapters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `promptId` INTEGER REFERENCES `PromptTemplates` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `prompt` TEXT, `content` TEXT NOT NULL, `type` VARCHAR(255) NOT NULL, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: Volumes
DROP TABLE IF EXISTS `Volumes`;
CREATE TABLE IF NOT EXISTS `Volumes` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `projectId` INTEGER NOT NULL REFERENCES `Projects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `title` VARCHAR(255) NOT NULL, `summary` TEXT NOT NULL, `wordCount` INTEGER DEFAULT 0, `chapterCount` INTEGER DEFAULT 0, `order` INTEGER DEFAULT 0, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: VolumeChapterGroups
DROP TABLE IF EXISTS `VolumeChapterGroups`;
CREATE TABLE IF NOT EXISTS `VolumeChapterGroups` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `volumeId` INTEGER NOT NULL REFERENCES `Volumes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `title` VARCHAR(255) NOT NULL, `chapterRange` VARCHAR(255) NOT NULL, `summary` TEXT NOT NULL, `progressRate` INTEGER DEFAULT 0, `order` INTEGER DEFAULT 0, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: chapter_versions
DROP TABLE IF EXISTS `chapter_versions`;
CREATE TABLE IF NOT EXISTS `chapter_versions` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `chapterId` INTEGER NOT NULL REFERENCES `Chapters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `versionNumber` INTEGER NOT NULL, `content` TEXT NOT NULL, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: editor_comments
DROP TABLE IF EXISTS `editor_comments`;
CREATE TABLE IF NOT EXISTS `editor_comments` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `chapterId` INTEGER NOT NULL REFERENCES `Chapters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `editorName` VARCHAR(255), `content` TEXT NOT NULL, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: reader_reviews
DROP TABLE IF EXISTS `reader_reviews`;
CREATE TABLE IF NOT EXISTS `reader_reviews` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `chapterId` INTEGER NOT NULL REFERENCES `Chapters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE, `readerName` VARCHAR(255), `content` TEXT NOT NULL, `rating` INTEGER NOT NULL, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表结构: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (`id` INTEGER PRIMARY KEY AUTO_INCREMENT, `username` VARCHAR(255) NOT NULL UNIQUE, `password` VARCHAR(255) NOT NULL, `email` VARCHAR(255) UNIQUE, `role` VARCHAR(255) NOT NULL DEFAULT 'user', `lastLoginAt` TIMESTAMP, `createdAt` TIMESTAMP NOT NULL, `updatedAt` TIMESTAMP NOT NULL)

-- 表数据: users
INSERT INTO `users` (`id`, `username`, `password`, `email`, `role`, `lastLoginAt`, `createdAt`, `updatedAt`) VALUES (1, 'admin', '$2b$10$MGlSR0nQfx3zLa9JmxfJbONwsFpWakJrIG4HCeSXaFIIdQ4CPfB1u', '<EMAIL>', 'admin', '2025-05-21 16:16:11.373 +00:00', '2025-05-20 16:09:46.571 +00:00', '2025-05-21 16:16:11.373 +00:00');

SET FOREIGN_KEY_CHECKS=1;
