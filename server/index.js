const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const http = require('http');
const { sequelize } = require('./models');
const routes = require('./routes');
const { authenticate } = require('./middleware/auth');
const websocketService = require('./services/websocketService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5001;

// CORS配置
const corsOptions = {
  origin: '*', // 允许所有来源访问
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true // 允许携带凭证
};

// 中间件
app.use(cors(corsOptions));
app.use(bodyParser.json());

// 添加错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: '服务器内部错误' });
});

// 路由
app.use('/api', routes);

// 添加全局认证中间件
// 注意：这里没有应用全局认证，因为我们在路由级别应用了认证中间件
// 如果想要所有路由都需要认证，可以取消下面的注释
// app.use('/api', authenticate, routes);

// 启动服务器
server.listen(PORT, async () => {
  console.log(`服务器运行在端口 ${PORT}`);

  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步数据库模型
    // 注意：已禁用自动同步，请使用迁移脚本来创建或修改表结构
    // await sequelize.sync({ alter: true });
    console.log('数据库同步已禁用，请使用迁移脚本');

    // 初始化WebSocket服务
    websocketService.initialize(server);
    websocketService.startHeartbeat();

  } catch (error) {
    console.error('数据库错误:', error);
    process.exit(1); // 数据库错误时退出进程
  }
});

// 处理未捕获的异常
process.on('uncaughtException', error => {
  console.error('未捕获的异常:', error);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});
