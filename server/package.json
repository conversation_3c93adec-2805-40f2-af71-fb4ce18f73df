{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "setup": "node scripts/setup.js", "db:to-mysql": "node scripts/switch-to-mysql.js", "db:to-sqlite": "node scripts/switch-to-sqlite.js", "db:init-mysql": "node scripts/init-mysql.js", "db:import-mysql": "node scripts/import-mysql-data.js", "db:sync-mysql": "node scripts/sync-mysql-models.js", "db:set-mysql": "node scripts/set-mysql-config.js", "db:set-sqlite": "node scripts/set-sqlite-config.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "bcrypt": "^5.1.1", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.9.7", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.1.10"}}