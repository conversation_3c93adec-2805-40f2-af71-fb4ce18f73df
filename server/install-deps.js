const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 检查 package.json 中是否已经有 umzug 依赖
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

const dependencies = packageJson.dependencies || {};
const devDependencies = packageJson.devDependencies || {};

// 需要安装的依赖
const requiredDeps = ['umzug'];
const missingDeps = [];

// 检查哪些依赖缺失
for (const dep of requiredDeps) {
  if (!dependencies[dep] && !devDependencies[dep]) {
    missingDeps.push(dep);
  }
}

// 安装缺失的依赖
if (missingDeps.length > 0) {
  console.log(`安装缺失的依赖: ${missingDeps.join(', ')}`);
  try {
    execSync(`npm install ${missingDeps.join(' ')}`, { stdio: 'inherit' });
    console.log('依赖安装成功');
  } catch (error) {
    console.error('安装依赖失败:', error.message);
  }
} else {
  console.log('所有必需的依赖已安装');
}
