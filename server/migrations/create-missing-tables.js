'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查并创建 chapter_versions 表
    try {
      await queryInterface.describeTable('chapter_versions');
      console.log('chapter_versions 表已存在，跳过创建');
    } catch (error) {
      console.log('创建 chapter_versions 表...');
      await queryInterface.createTable('chapter_versions', {
        id: {
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        chapterId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'Chapters',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        versionNumber: {
          type: Sequelize.INTEGER,
          allowNull: false,
        },
        content: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        }
      });
    }

    // 检查并创建 editor_comments 表
    try {
      await queryInterface.describeTable('editor_comments');
      console.log('editor_comments 表已存在，跳过创建');
    } catch (error) {
      console.log('创建 editor_comments 表...');
      await queryInterface.createTable('editor_comments', {
        id: {
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        chapterId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'Chapters',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        editorName: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        content: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        }
      });
    }

    // 检查并创建 reader_reviews 表
    try {
      await queryInterface.describeTable('reader_reviews');
      console.log('reader_reviews 表已存在，跳过创建');
    } catch (error) {
      console.log('创建 reader_reviews 表...');
      await queryInterface.createTable('reader_reviews', {
        id: {
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        chapterId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'Chapters',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        readerName: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        content: {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        rating: {
          type: Sequelize.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 5,
          },
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
        }
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 如果需要回滚，则删除这些表
    await queryInterface.dropTable('reader_reviews', { if_exists: true });
    await queryInterface.dropTable('editor_comments', { if_exists: true });
    await queryInterface.dropTable('chapter_versions', { if_exists: true });
  }
};
