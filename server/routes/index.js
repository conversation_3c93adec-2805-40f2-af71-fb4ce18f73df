const express = require('express');
const router = express.Router();

// 导入控制器
const projectController = require('../controllers/projectController');
const worldSettingController = require('../controllers/worldSettingController');
const characterController = require('../controllers/characterController');
const outlineController = require('../controllers/outlineController');
const chapterController = require('../controllers/chapterController');
const promptTemplateController = require('../controllers/promptTemplateController');
const generatedContentController = require('../controllers/generatedContentController');
const volumeController = require('../controllers/volumeController');
const volumeChapterGroupController = require('../controllers/volumeChapterGroupController');
const aiConfigController = require('../controllers/aiConfigController');
const aiGenerationController = require('../controllers/aiGenerationController');
const chapterVersionController = require('../controllers/chapterVersionController'); // 导入章节版本控制器
const volumeChapterGroupVersionController = require('../controllers/volumeChapterGroupVersionController'); // 导入章节分组版本控制器
const volumeVersionController = require('../controllers/volumeVersionController'); // 导入分卷版本控制器
const editorCommentController = require('../controllers/editorCommentController'); // 导入编辑意见控制器
const readerReviewController = require('../controllers/readerReviewController'); // 导入读者评价控制器
const authController = require('../controllers/authController'); // 导入认证控制器
const clueController = require('../controllers/clueController'); // 导入线索控制器
const userController = require('../controllers/userController'); // 导入用户管理控制器

// 导入工作流路由
const workflowRoutes = require('./workflow');

// 导入中间件
const { authenticate, checkRole } = require('../middleware/auth'); // 导入认证中间件

// 项目路由 - 需要认证
router.get('/projects', authenticate, projectController.getAllProjects); // 保留原接口作为兼容
router.get('/projects-basic', authenticate, projectController.getProjectsBasic); // 新的快速加载接口
router.get('/projects/:id', authenticate, projectController.getProjectById);
router.get('/projects/:id/stats', authenticate, projectController.getProjectStats); // 单个项目统计
router.post('/projects/batch-stats', authenticate, projectController.getBatchProjectStats); // 批量统计
router.get('/projects/:id/preview', authenticate, projectController.getProjectPreview);
router.post('/projects', authenticate, projectController.createProject);
router.put('/projects/:id', authenticate, projectController.updateProject);
router.delete('/projects/:id', authenticate, projectController.deleteProject);

// 世界观设定路由 - 需要认证
router.get('/projects/:projectId/world-settings', authenticate, worldSettingController.getWorldSettingsByProject);
router.get('/world-settings/:id', authenticate, worldSettingController.getWorldSettingById);
router.post('/world-settings', authenticate, worldSettingController.createWorldSetting);
router.put('/world-settings/:id', authenticate, worldSettingController.updateWorldSetting);
router.delete('/world-settings/:id', authenticate, worldSettingController.deleteWorldSetting);

// 角色路由 - 需要认证
router.get('/projects/:projectId/characters', authenticate, characterController.getCharactersByProject);
router.get('/characters/:id', authenticate, characterController.getCharacterById);
router.post('/characters', authenticate, characterController.createCharacter);
router.put('/characters/:id', authenticate, characterController.updateCharacter);
router.delete('/characters/:id', authenticate, characterController.deleteCharacter);

// 大纲路由 - 需要认证
router.get('/projects/:projectId/outlines', authenticate, outlineController.getOutlinesByProject);
router.get('/outlines/:id', authenticate, outlineController.getOutlineById);
router.post('/outlines', authenticate, outlineController.createOutline);
router.put('/outlines/:id', authenticate, outlineController.updateOutline);
router.delete('/outlines/:id', authenticate, outlineController.deleteOutline);

// 章节路由 - 需要认证
router.get('/projects/:projectId/chapters', authenticate, chapterController.getChaptersByProject);
router.get('/chapters/:id', authenticate, chapterController.getChapterById);
router.post('/chapters', authenticate, chapterController.createChapter);
router.put('/chapters/:id', authenticate, chapterController.updateChapter);
router.delete('/chapters/:id', authenticate, chapterController.deleteChapter);

// 提示词模板路由 - 需要认证
router.get('/prompt-templates', authenticate, promptTemplateController.getAllPromptTemplates);
router.get('/prompt-templates/:id', authenticate, promptTemplateController.getPromptTemplateById);
router.post('/prompt-templates', authenticate, promptTemplateController.createPromptTemplate);
router.put('/prompt-templates/:id', authenticate, promptTemplateController.updatePromptTemplate);
router.delete('/prompt-templates/:id', authenticate, promptTemplateController.deletePromptTemplate);

// 生成内容路由 - 需要认证
router.get('/projects/:projectId/generated-contents', authenticate, generatedContentController.getGeneratedContentsByProject);
router.get('/generated-contents/:id', authenticate, generatedContentController.getGeneratedContentById);
router.post('/generated-contents', authenticate, generatedContentController.createGeneratedContent);
router.put('/generated-contents/:id', authenticate, generatedContentController.updateGeneratedContent);
router.delete('/generated-contents/:id', authenticate, generatedContentController.deleteGeneratedContent);

// 提示词生成路由 - 需要认证
router.post('/generate-prompt', authenticate, promptTemplateController.generatePrompt);
router.post('/prompt-templates/:id/generate', authenticate, promptTemplateController.generatePromptById);

// 分卷路由 - 需要认证
router.get('/projects/:projectId/volumes', authenticate, volumeController.getVolumesByProject);
router.get('/volumes/:id', authenticate, volumeController.getVolumeById);
router.post('/volumes', authenticate, volumeController.createVolume);
router.put('/volumes/:id', authenticate, volumeController.updateVolume);
router.delete('/volumes/:id', authenticate, volumeController.deleteVolume);

// 章节分组路由 - 需要认证
router.get('/volumes/:volumeId/chapter-groups', authenticate, volumeChapterGroupController.getChapterGroupsByVolume);
router.get('/chapter-groups/:id', authenticate, volumeChapterGroupController.getChapterGroupById);
router.post('/chapter-groups', authenticate, volumeChapterGroupController.createChapterGroup);
router.put('/chapter-groups/:id', authenticate, volumeChapterGroupController.updateChapterGroup);
router.delete('/chapter-groups/:id', authenticate, volumeChapterGroupController.deleteChapterGroup);

// 章节版本路由 - 需要认证
router.get('/chapters/:chapterId/versions', authenticate, chapterVersionController.getChapterVersions);
router.get('/chapters/:chapterId/versions/:versionId', authenticate, chapterVersionController.getChapterVersion);
router.post('/chapters/:chapterId/versions', authenticate, chapterVersionController.createChapterVersion); // 通常由更新章节时触发，但提供API

// 章节分组版本路由 - 需要认证
router.get('/chapter-groups/:chapterGroupId/versions', authenticate, volumeChapterGroupVersionController.getChapterGroupVersions);
router.get('/chapter-groups/:chapterGroupId/versions/:versionId', authenticate, volumeChapterGroupVersionController.getChapterGroupVersion);
router.post('/chapter-groups/:chapterGroupId/versions', authenticate, volumeChapterGroupVersionController.createChapterGroupVersion); // 通常由更新章节分组时触发，但提供API

// 分卷版本路由 - 需要认证
router.get('/volumes/:volumeId/versions', authenticate, volumeVersionController.getVolumeVersions);
router.get('/volumes/:volumeId/versions/:versionId', authenticate, volumeVersionController.getVolumeVersion);
router.post('/volumes/:volumeId/versions', authenticate, volumeVersionController.createVolumeVersion); // 通常由更新分卷时触发，但提供API

// 编辑意见路由 - 需要认证
router.get('/chapters/:chapterId/editor-comments', authenticate, editorCommentController.getChapterEditorComments);
router.post('/chapters/:chapterId/editor-comments', authenticate, editorCommentController.addEditorComment);

// 读者评价路由 - 需要认证
router.get('/chapters/:chapterId/reader-reviews', authenticate, readerReviewController.getChapterReaderReviews);
router.post('/chapters/:chapterId/reader-reviews', authenticate, readerReviewController.addReaderReview);

// 线索路由 - 需要认证
router.get('/projects/:projectId/clues', authenticate, clueController.getCluesByProject);
router.get('/clues/:id', authenticate, clueController.getClueById);
router.post('/clues', authenticate, clueController.createClue);
router.put('/clues/:id', authenticate, clueController.updateClue);
router.delete('/clues/:id', authenticate, clueController.deleteClue);

// AI配置路由 - 需要认证
router.get('/ai-configs', authenticate, aiConfigController.getUserAIConfigs);
router.post('/ai-configs', authenticate, aiConfigController.createAIConfig);
router.put('/ai-configs/:id', authenticate, aiConfigController.updateAIConfig);
router.delete('/ai-configs/:id', authenticate, aiConfigController.deleteAIConfig);
router.post('/ai-configs/:id/set-default', authenticate, aiConfigController.setDefaultAIConfig);
router.post('/ai-configs/:id/test', authenticate, aiConfigController.testAIConfig);

// AI生成路由 - 需要认证
router.get('/ai-generation/configs', authenticate, aiGenerationController.getAvailableAIConfigs);
router.post('/ai-generation/generate', authenticate, aiGenerationController.generateContent);
router.post('/ai-generation/generate-stream', authenticate, aiGenerationController.generateContentStream);
router.post('/ai-generation/world-setting', authenticate, aiGenerationController.generateWorldSetting);

// 用户管理路由 - 仅admin可访问
router.get('/users', authenticate, checkRole(['admin']), userController.getAllUsers);
router.get('/users/:id', authenticate, checkRole(['admin']), userController.getUserById);
router.post('/users', authenticate, checkRole(['admin']), userController.createUser);
router.put('/users/:id', authenticate, checkRole(['admin']), userController.updateUser);
router.delete('/users/:id', authenticate, checkRole(['admin']), userController.deleteUser);

// 认证路由 - 这些路由不需要认证
router.post('/auth/register', authController.register);
router.post('/auth/login', authController.login);

// 需要认证的用户路由
router.get('/auth/me', authenticate, authController.getCurrentUser);
router.post('/auth/change-password', authenticate, authController.changePassword);

// 工作流路由 - 需要认证
router.use('/workflows', workflowRoutes);

module.exports = router;
