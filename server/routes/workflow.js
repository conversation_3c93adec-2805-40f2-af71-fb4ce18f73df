const express = require('express');
const router = express.Router();
const workflowController = require('../controllers/workflowController');
const workflowExecutionController = require('../controllers/workflowExecutionController');
const { authenticate } = require('../middleware/auth');

// 应用认证中间件到所有路由
router.use(authenticate);

// 流程模板管理路由
router.get('/templates', workflowController.getWorkflowTemplates);
router.get('/templates/:id', workflowController.getWorkflowTemplateById);
router.post('/templates', workflowController.createWorkflowTemplate);
router.put('/templates/:id', workflowController.updateWorkflowTemplate);
router.delete('/templates/:id', workflowController.deleteWorkflowTemplate);

// 流程分类路由
router.get('/categories', workflowController.getWorkflowCategories);

// 流程执行路由
router.post('/templates/:id/execute', workflowExecutionController.executeWorkflow);
router.get('/executions/:executionId', workflowExecutionController.getExecutionStatus);
router.get('/executions', workflowExecutionController.getExecutionHistory);
router.post('/executions/:executionId/continue', workflowExecutionController.continueExecution);
router.post('/executions/:executionId/pause', workflowExecutionController.pauseExecution);
router.post('/executions/:executionId/cancel', workflowExecutionController.cancelExecution);

// 流程版本管理路由
router.get('/templates/:id/versions', workflowController.getWorkflowVersions);
router.post('/templates/:id/versions', workflowController.createWorkflowVersion);
router.post('/templates/:id/versions/:versionId/restore', workflowController.restoreWorkflowVersion);

// 流程导入导出路由
router.get('/templates/:id/export', workflowController.exportWorkflowTemplate);
router.post('/templates/import', workflowController.importWorkflowTemplate);

// 统计分析路由
router.get('/statistics', workflowController.getWorkflowStatistics);

module.exports = router;
