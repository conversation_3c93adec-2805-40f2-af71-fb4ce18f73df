-- 创建 reader_reviews 表
CREATE TABLE IF NOT EXISTS reader_reviews (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  readerName TEXT,
  content TEXT NOT NULL,
  rating INTEGER NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);

-- 创建 editor_comments 表
CREATE TABLE IF NOT EXISTS editor_comments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  editorName TEXT,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);

-- 创建 chapter_versions 表
CREATE TABLE IF NOT EXISTS chapter_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  versionNumber INTEGER NOT NULL,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);
