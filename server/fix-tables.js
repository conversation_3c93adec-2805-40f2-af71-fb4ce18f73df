const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
const config = require('./config/database');

// 获取当前环境的配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 数据库文件路径
const dbPath = path.resolve(__dirname, dbConfig.storage);

console.log(`正在修复数据库: ${dbPath}`);

// 创建 reader_reviews 表的 SQL
const createReaderReviewsSQL = `
CREATE TABLE IF NOT EXISTS reader_reviews (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  readerName TEXT,
  content TEXT NOT NULL,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);
`;

// 创建 editor_comments 表的 SQL
const createEditorCommentsSQL = `
CREATE TABLE IF NOT EXISTS editor_comments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  editorName TEXT,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);
`;

// 创建 chapter_versions 表的 SQL
const createChapterVersionsSQL = `
CREATE TABLE IF NOT EXISTS chapter_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  versionNumber INTEGER NOT NULL,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);
`;

// 连接数据库并执行 SQL
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    return console.error('无法连接到数据库:', err.message);
  }
  console.log('已连接到 SQLite 数据库');
  
  // 开始事务
  db.serialize(() => {
    db.run('BEGIN TRANSACTION');
    
    // 执行创建表的 SQL
    console.log('正在创建 reader_reviews 表...');
    db.run(createReaderReviewsSQL, (err) => {
      if (err) {
        console.error('创建 reader_reviews 表失败:', err.message);
        db.run('ROLLBACK');
        return;
      }
      
      console.log('正在创建 editor_comments 表...');
      db.run(createEditorCommentsSQL, (err) => {
        if (err) {
          console.error('创建 editor_comments 表失败:', err.message);
          db.run('ROLLBACK');
          return;
        }
        
        console.log('正在创建 chapter_versions 表...');
        db.run(createChapterVersionsSQL, (err) => {
          if (err) {
            console.error('创建 chapter_versions 表失败:', err.message);
            db.run('ROLLBACK');
            return;
          }
          
          // 提交事务
          db.run('COMMIT', (err) => {
            if (err) {
              console.error('提交事务失败:', err.message);
              db.run('ROLLBACK');
            } else {
              console.log('成功创建所有缺少的表！');
            }
            
            // 关闭数据库连接
            db.close((err) => {
              if (err) {
                console.error('关闭数据库连接失败:', err.message);
              } else {
                console.log('数据库连接已关闭');
              }
            });
          });
        });
      });
    });
  });
});
