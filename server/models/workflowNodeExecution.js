const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowNodeExecution = sequelize.define('WorkflowNodeExecution', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    executionId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '流程执行ID'
    },
    nodeId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '节点ID'
    },
    status: {
      type: DataTypes.ENUM('pending', 'running', 'completed', 'failed', 'skipped'),
      defaultValue: 'pending',
      comment: '节点执行状态'
    },
    inputData: {
      type: DataTypes.JSON,
      comment: '节点输入数据',
      get() {
        const value = this.getDataValue('inputData');
        return value || {};
      },
      set(value) {
        this.setDataValue('inputData', value || {});
      }
    },
    outputData: {
      type: DataTypes.JSON,
      comment: '节点输出数据',
      get() {
        const value = this.getDataValue('outputData');
        return value || {};
      },
      set(value) {
        this.setDataValue('outputData', value || {});
      }
    },
    aiConfigId: {
      type: DataTypes.INTEGER,
      comment: '使用的AI配置ID',
      references: {
        model: 'ai_configs',
        key: 'id'
      }
    },
    promptTemplateId: {
      type: DataTypes.INTEGER,
      comment: '使用的提示词模板ID',
      references: {
        model: 'PromptTemplates',
        key: 'id'
      }
    },
    executionTimeMs: {
      type: DataTypes.INTEGER,
      comment: '执行耗时（毫秒）'
    },
    errorMessage: {
      type: DataTypes.TEXT,
      comment: '错误信息'
    },
    startedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '开始时间'
    },
    completedAt: {
      type: DataTypes.DATE,
      comment: '完成时间'
    }
  }, {
    tableName: 'workflow_node_executions',
    timestamps: true,
    indexes: [
      {
        fields: ['executionId']
      },
      {
        fields: ['nodeId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['startedAt']
      },
      {
        fields: ['aiConfigId']
      },
      {
        fields: ['promptTemplateId']
      }
    ]
  });

  return WorkflowNodeExecution;
};
