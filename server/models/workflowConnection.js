const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowConnection = sequelize.define('WorkflowConnection', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflowId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '流程ID',
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    sourceNodeId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '源节点ID'
    },
    targetNodeId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '目标节点ID'
    },
    sourceHandle: {
      type: DataTypes.STRING(100),
      comment: '源连接点'
    },
    targetHandle: {
      type: DataTypes.STRING(100),
      comment: '目标连接点'
    },
    conditionConfig: {
      type: DataTypes.JSON,
      comment: '连接条件配置',
      get() {
        const value = this.getDataValue('conditionConfig');
        return value || {};
      },
      set(value) {
        this.setDataValue('conditionConfig', value || {});
      }
    }
  }, {
    tableName: 'workflow_connections',
    timestamps: true,
    indexes: [
      {
        fields: ['workflowId']
      },
      {
        fields: ['sourceNodeId']
      },
      {
        fields: ['targetNodeId']
      }
    ]
  });

  return WorkflowConnection;
};
