const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const EditorComment = sequelize.define('EditorComment', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    chapterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Chapters',
        key: 'id',
      },
    },
    // userId: { // 关联到评论的编辑用户
    //   type: DataTypes.INTEGER,
    //   allowNull: false,
    //   references: {
    //     model: 'Users',
    //     key: 'id',
    //   },
    // },
    editorName: { // 暂时使用名称，未来可以关联用户表
      type: DataTypes.STRING,
      allowNull: true, // 或者 false，取决于是否强制要求
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    }
  }, {
    tableName: 'editor_comments',
    timestamps: true
  });

  return EditorComment;
};