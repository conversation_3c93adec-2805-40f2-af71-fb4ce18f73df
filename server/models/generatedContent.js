const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const GeneratedContent = sequelize.define('GeneratedContent', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    projectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Projects',
        key: 'id'
      }
    },
    chapterId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'Chapters',
        key: 'id'
      }
    },
    promptId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'PromptTemplates',
        key: 'id'
      }
    },
    prompt: {
      type: DataTypes.TEXT
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  });

  return GeneratedContent;
};
