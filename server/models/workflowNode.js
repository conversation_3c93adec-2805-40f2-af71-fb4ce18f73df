const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowNode = sequelize.define('WorkflowNode', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflowId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '流程ID',
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    nodeId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '节点唯一标识'
    },
    nodeType: {
      type: DataTypes.ENUM('start', 'ai_generation', 'user_input', 'condition', 'end'),
      allowNull: false,
      comment: '节点类型'
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '节点名称'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '节点描述'
    },
    positionX: {
      type: DataTypes.FLOAT,
      comment: 'X坐标'
    },
    positionY: {
      type: DataTypes.FLOAT,
      comment: 'Y坐标'
    },
    config: {
      type: DataTypes.JSON,
      comment: '节点配置',
      get() {
        const value = this.getDataValue('config');
        return value || {};
      },
      set(value) {
        this.setDataValue('config', value || {});
      }
    }
  }, {
    tableName: 'workflow_nodes',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['workflowId', 'nodeId'],
        name: 'unique_workflow_node'
      },
      {
        fields: ['workflowId']
      },
      {
        fields: ['nodeType']
      }
    ]
  });

  return WorkflowNode;
};
