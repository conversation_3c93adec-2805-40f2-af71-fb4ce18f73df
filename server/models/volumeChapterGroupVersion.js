const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const VolumeChapterGroupVersion = sequelize.define('VolumeChapterGroupVersion', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    chapterGroupId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'VolumeChapterGroups',
        key: 'id',
      },
    },
    versionNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    chapterRange: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    progressRate: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    }
  }, {
    tableName: 'volume_chapter_group_versions',
    timestamps: true
  });

  return VolumeChapterGroupVersion;
};
