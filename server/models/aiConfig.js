const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const AIConfig = sequelize.define('AIConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    provider: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '大模型提供商，如：deepseek, openai, claude等'
    },
    apiKey: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'API密钥'
    },
    baseUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'API基础URL，用于自定义端点'
    },
    model: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '使用的具体模型名称'
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否为默认配置'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否启用'
    },
    maxTokens: {
      type: DataTypes.INTEGER,
      defaultValue: 4000,
      comment: '最大token数'
    },
    temperature: {
      type: DataTypes.FLOAT,
      defaultValue: 0.7,
      comment: '温度参数'
    },
    config: {
      type: DataTypes.TEXT,
      comment: '其他配置参数，JSON格式',
      get() {
        const value = this.getDataValue('config');
        return value ? JSON.parse(value) : {};
      },
      set(value) {
        this.setDataValue('config', JSON.stringify(value || {}));
      }
    }
  }, {
    tableName: 'ai_configs',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['userId', 'provider', 'model'],
        name: 'unique_user_provider_model'
      }
    ]
  });

  return AIConfig;
};
