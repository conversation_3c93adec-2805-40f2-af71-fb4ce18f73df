const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Clue = sequelize.define('Clue', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    projectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Projects',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('重大事件', '重要道具', '重要伏笔'),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    firstAppearChapterId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'Chapters',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  });

  return Clue;
};
