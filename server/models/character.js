const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Character = sequelize.define('Character', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    projectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Projects',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.STRING
    },
    description: {
      type: DataTypes.TEXT
    },
    background: {
      type: DataTypes.TEXT
    },
    personality: {
      type: DataTypes.TEXT
    },
    abilities: {
      type: DataTypes.TEXT
    },
    abilities_detail: {
      type: DataTypes.TEXT
    },
    relationships: {
      type: DataTypes.TEXT
    },
    isMainCharacter: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    importance: {
      type: DataTypes.INTEGER,
      defaultValue: 50,
      validate: {
        min: 1,
        max: 100
      }
    },
    gender: {
      type: DataTypes.ENUM('男', '女', '其他', '未设定'),
      defaultValue: '未设定'
    },
    characterArc: {
      type: DataTypes.TEXT
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  });

  return Character;
};
