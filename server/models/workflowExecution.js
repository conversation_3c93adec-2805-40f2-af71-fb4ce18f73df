const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowExecution = sequelize.define('WorkflowExecution', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflowId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '流程ID',
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    executionId: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '执行ID'
    },
    status: {
      type: DataTypes.ENUM('running', 'completed', 'failed', 'cancelled', 'paused'),
      defaultValue: 'running',
      comment: '执行状态'
    },
    currentNodeId: {
      type: DataTypes.STRING(100),
      comment: '当前执行节点'
    },
    inputData: {
      type: DataTypes.JSON,
      comment: '输入数据',
      get() {
        const value = this.getDataValue('inputData');
        return value || {};
      },
      set(value) {
        this.setDataValue('inputData', value || {});
      }
    },
    outputData: {
      type: DataTypes.JSON,
      comment: '输出数据',
      get() {
        const value = this.getDataValue('outputData');
        return value || {};
      },
      set(value) {
        this.setDataValue('outputData', value || {});
      }
    },
    executionLog: {
      type: DataTypes.JSON,
      comment: '执行日志',
      get() {
        const value = this.getDataValue('executionLog');
        return value || [];
      },
      set(value) {
        this.setDataValue('executionLog', value || []);
      }
    },
    errorMessage: {
      type: DataTypes.TEXT,
      comment: '错误信息'
    },
    startedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '开始时间'
    },
    completedAt: {
      type: DataTypes.DATE,
      comment: '完成时间'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '执行用户ID',
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    projectId: {
      type: DataTypes.INTEGER,
      comment: '关联项目ID',
      references: {
        model: 'Projects',
        key: 'id'
      }
    }
  }, {
    tableName: 'workflow_executions',
    timestamps: true,
    indexes: [
      {
        fields: ['workflowId']
      },
      {
        fields: ['executionId']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['status']
      },
      {
        fields: ['startedAt']
      },
      {
        fields: ['projectId']
      }
    ]
  });

  return WorkflowExecution;
};
