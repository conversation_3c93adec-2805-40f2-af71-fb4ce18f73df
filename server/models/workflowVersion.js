const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowVersion = sequelize.define('WorkflowVersion', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    workflowId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '流程ID',
      references: {
        model: 'workflow_templates',
        key: 'id'
      }
    },
    version: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '版本号'
    },
    config: {
      type: DataTypes.JSON,
      comment: '版本配置快照',
      get() {
        const value = this.getDataValue('config');
        return value || {};
      },
      set(value) {
        this.setDataValue('config', value || {});
      }
    },
    changeLog: {
      type: DataTypes.TEXT,
      comment: '变更日志'
    },
    isCurrent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否当前版本'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '创建者ID',
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    tableName: 'workflow_versions',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['workflowId', 'version'],
        name: 'unique_workflow_version'
      },
      {
        fields: ['workflowId']
      },
      {
        fields: ['isCurrent']
      },
      {
        fields: ['createdBy']
      }
    ]
  });

  return WorkflowVersion;
};
