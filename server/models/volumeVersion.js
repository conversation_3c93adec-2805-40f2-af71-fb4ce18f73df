const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const VolumeVersion = sequelize.define('VolumeVersion', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    volumeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Volumes',
        key: 'id',
      },
    },
    versionNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    wordCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    chapterCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    writingRequirements: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '分卷写作要求'
    }
  }, {
    tableName: 'volume_versions',
    timestamps: true
  });

  return VolumeVersion;
};
