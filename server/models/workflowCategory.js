const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowCategory = sequelize.define('WorkflowCategory', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '分类名称'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '分类描述'
    },
    icon: {
      type: DataTypes.STRING(100),
      comment: '图标'
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '排序'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否激活'
    }
  }, {
    tableName: 'workflow_categories',
    timestamps: true,
    indexes: [
      {
        fields: ['sortOrder']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  return WorkflowCategory;
};
