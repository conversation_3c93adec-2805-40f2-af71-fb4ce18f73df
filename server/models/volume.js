const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Volume = sequelize.define('Volume', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    projectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Projects',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    wordCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    chapterCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    order: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    writingRequirements: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '分卷写作要求'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  });

  return Volume;
};
