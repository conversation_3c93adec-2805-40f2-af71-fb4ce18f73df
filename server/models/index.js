const { Sequelize } = require('sequelize');
const config = require('../config/database');

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

let sequelize;
if (dbConfig.dialect === 'mysql') {
  sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: 'mysql',
    dialectModule: require('mysql2'), // 明确指定 mysql2
    logging: dbConfig.logging,
    dialectOptions: dbConfig.dialectOptions,
  });
} else {
  sequelize = new Sequelize(dbConfig);
}

// 导入模型
const Project = require('./project')(sequelize);
const WorldSetting = require('./worldSetting')(sequelize);
const Character = require('./character')(sequelize);
const Outline = require('./outline')(sequelize);
const Chapter = require('./chapter')(sequelize);
const PromptTemplate = require('./promptTemplate')(sequelize);
const GeneratedContent = require('./generatedContent')(sequelize);
const Volume = require('./volume')(sequelize);
const VolumeChapterGroup = require('./volumeChapterGroup')(sequelize);
const ChapterVersion = require('./chapterVersion')(sequelize); // 导入 ChapterVersion
const EditorComment = require('./editorComment')(sequelize); // 导入 EditorComment
const ReaderReview = require('./readerReview')(sequelize); // 导入 ReaderReview
const User = require('./user')(sequelize); // 导入 User 模型
const VolumeChapterGroupVersion = require('./volumeChapterGroupVersion')(sequelize); // 导入 VolumeChapterGroupVersion
const VolumeVersion = require('./volumeVersion')(sequelize); // 导入 VolumeVersion
const Clue = require('./clue')(sequelize); // 导入 Clue 模型
const AIConfig = require('./aiConfig')(sequelize); // 导入 AIConfig 模型

// 创作流程相关模型
const WorkflowTemplate = require('./workflowTemplate')(sequelize);
const WorkflowNode = require('./workflowNode')(sequelize);
const WorkflowConnection = require('./workflowConnection')(sequelize);
const WorkflowExecution = require('./workflowExecution')(sequelize);
const WorkflowNodeExecution = require('./workflowNodeExecution')(sequelize);
const WorkflowVersion = require('./workflowVersion')(sequelize);
const WorkflowCategory = require('./workflowCategory')(sequelize);

// 定义关联关系
Project.hasMany(WorldSetting, { foreignKey: 'projectId', onDelete: 'CASCADE' });
WorldSetting.belongsTo(Project, { foreignKey: 'projectId' });

Project.hasMany(Character, { foreignKey: 'projectId', onDelete: 'CASCADE' });
Character.belongsTo(Project, { foreignKey: 'projectId' });

Project.hasMany(Outline, { foreignKey: 'projectId', onDelete: 'CASCADE' });
Outline.belongsTo(Project, { foreignKey: 'projectId' });
Outline.hasMany(Outline, { foreignKey: 'parentId', as: 'children' });
Outline.belongsTo(Outline, { foreignKey: 'parentId', as: 'parent' });

Project.hasMany(Chapter, { foreignKey: 'projectId', onDelete: 'CASCADE' });
Chapter.belongsTo(Project, { foreignKey: 'projectId' });
Outline.hasMany(Chapter, { foreignKey: 'outlineId' });
Chapter.belongsTo(Outline, { foreignKey: 'outlineId' });

Project.hasMany(GeneratedContent, { foreignKey: 'projectId', onDelete: 'CASCADE' });
GeneratedContent.belongsTo(Project, { foreignKey: 'projectId' });
Chapter.hasMany(GeneratedContent, { foreignKey: 'chapterId' });
GeneratedContent.belongsTo(Chapter, { foreignKey: 'chapterId' });
PromptTemplate.hasMany(GeneratedContent, { foreignKey: 'promptId' });
GeneratedContent.belongsTo(PromptTemplate, { foreignKey: 'promptId' });

// 分卷与项目的关联
Project.hasMany(Volume, { foreignKey: 'projectId', onDelete: 'CASCADE' });
Volume.belongsTo(Project, { foreignKey: 'projectId' });

// 章节分组与分卷的关联
Volume.hasMany(VolumeChapterGroup, { foreignKey: 'volumeId', onDelete: 'CASCADE' });
VolumeChapterGroup.belongsTo(Volume, { foreignKey: 'volumeId' });

// 章节分组与版本的关联
VolumeChapterGroup.hasMany(VolumeChapterGroupVersion, { foreignKey: 'chapterGroupId', as: 'versions', onDelete: 'CASCADE' });
VolumeChapterGroupVersion.belongsTo(VolumeChapterGroup, { foreignKey: 'chapterGroupId', as: 'chapterGroup' });

// 分卷与版本的关联
Volume.hasMany(VolumeVersion, { foreignKey: 'volumeId', as: 'versions', onDelete: 'CASCADE' });
VolumeVersion.belongsTo(Volume, { foreignKey: 'volumeId', as: 'volume' });

// 章节与版本、评论、评价的关联
Chapter.hasMany(ChapterVersion, { foreignKey: 'chapterId', as: 'versions', onDelete: 'CASCADE' });
ChapterVersion.belongsTo(Chapter, { foreignKey: 'chapterId', as: 'chapter' });

Chapter.hasMany(EditorComment, { foreignKey: 'chapterId', as: 'editorComments', onDelete: 'CASCADE' });
EditorComment.belongsTo(Chapter, { foreignKey: 'chapterId', as: 'chapter' });

Chapter.hasMany(ReaderReview, { foreignKey: 'chapterId', as: 'readerReviews', onDelete: 'CASCADE' });
ReaderReview.belongsTo(Chapter, { foreignKey: 'chapterId', as: 'chapter' });

// 章节与分卷的关联（可选，如果需要直接关联章节到分卷）
// Volume.hasMany(Chapter, { foreignKey: 'volumeId' });
// Chapter.belongsTo(Volume, { foreignKey: 'volumeId' });

// 线索与项目的关联
Project.hasMany(Clue, { foreignKey: 'projectId', onDelete: 'CASCADE' });
Clue.belongsTo(Project, { foreignKey: 'projectId' });

// 线索与章节的关联（首次出现章节）
Chapter.hasMany(Clue, { foreignKey: 'firstAppearChapterId', as: 'clues' });
Clue.belongsTo(Chapter, { foreignKey: 'firstAppearChapterId', as: 'firstAppearChapter' });

// 用户与各种数据的关联关系
User.hasMany(Project, { foreignKey: 'userId', onDelete: 'CASCADE' });
Project.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(WorldSetting, { foreignKey: 'userId', onDelete: 'CASCADE' });
WorldSetting.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Character, { foreignKey: 'userId', onDelete: 'CASCADE' });
Character.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Outline, { foreignKey: 'userId', onDelete: 'CASCADE' });
Outline.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Chapter, { foreignKey: 'userId', onDelete: 'CASCADE' });
Chapter.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Volume, { foreignKey: 'userId', onDelete: 'CASCADE' });
Volume.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(VolumeChapterGroup, { foreignKey: 'userId', onDelete: 'CASCADE' });
VolumeChapterGroup.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(Clue, { foreignKey: 'userId', onDelete: 'CASCADE' });
Clue.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(PromptTemplate, { foreignKey: 'userId', onDelete: 'CASCADE' });
PromptTemplate.belongsTo(User, { foreignKey: 'userId' });

User.hasMany(GeneratedContent, { foreignKey: 'userId', onDelete: 'CASCADE' });
GeneratedContent.belongsTo(User, { foreignKey: 'userId' });

// 用户与AI配置的关联关系
User.hasMany(AIConfig, { foreignKey: 'userId', onDelete: 'CASCADE' });
AIConfig.belongsTo(User, { foreignKey: 'userId' });

// 创作流程相关关联关系
// 用户与流程模板的关联
User.hasMany(WorkflowTemplate, { foreignKey: 'userId', onDelete: 'CASCADE' });
WorkflowTemplate.belongsTo(User, { foreignKey: 'userId' });

// 流程模板与节点的关联
WorkflowTemplate.hasMany(WorkflowNode, { foreignKey: 'workflowId', as: 'nodes', onDelete: 'CASCADE' });
WorkflowNode.belongsTo(WorkflowTemplate, { foreignKey: 'workflowId' });

// 流程模板与连接的关联
WorkflowTemplate.hasMany(WorkflowConnection, { foreignKey: 'workflowId', as: 'connections', onDelete: 'CASCADE' });
WorkflowConnection.belongsTo(WorkflowTemplate, { foreignKey: 'workflowId' });

// 流程模板与执行记录的关联
WorkflowTemplate.hasMany(WorkflowExecution, { foreignKey: 'workflowId', as: 'executions', onDelete: 'CASCADE' });
WorkflowExecution.belongsTo(WorkflowTemplate, { foreignKey: 'workflowId' });

// 流程模板与版本的关联
WorkflowTemplate.hasMany(WorkflowVersion, { foreignKey: 'workflowId', as: 'versions', onDelete: 'CASCADE' });
WorkflowVersion.belongsTo(WorkflowTemplate, { foreignKey: 'workflowId' });

// 用户与流程执行的关联
User.hasMany(WorkflowExecution, { foreignKey: 'userId', onDelete: 'CASCADE' });
WorkflowExecution.belongsTo(User, { foreignKey: 'userId' });

// 项目与流程执行的关联
Project.hasMany(WorkflowExecution, { foreignKey: 'projectId', onDelete: 'SET NULL' });
WorkflowExecution.belongsTo(Project, { foreignKey: 'projectId' });

// 用户与版本的关联
User.hasMany(WorkflowVersion, { foreignKey: 'createdBy', onDelete: 'CASCADE' });
WorkflowVersion.belongsTo(User, { foreignKey: 'createdBy' });

// AI配置与节点执行的关联
AIConfig.hasMany(WorkflowNodeExecution, { foreignKey: 'aiConfigId', onDelete: 'SET NULL' });
WorkflowNodeExecution.belongsTo(AIConfig, { foreignKey: 'aiConfigId' });

// 提示词模板与节点执行的关联
PromptTemplate.hasMany(WorkflowNodeExecution, { foreignKey: 'promptTemplateId', onDelete: 'SET NULL' });
WorkflowNodeExecution.belongsTo(PromptTemplate, { foreignKey: 'promptTemplateId' });

module.exports = {
  sequelize,
  Project,
  WorldSetting,
  Character,
  Outline,
  Chapter,
  PromptTemplate,
  GeneratedContent,
  Volume,
  VolumeChapterGroup,
  ChapterVersion, // 导出 ChapterVersion
  EditorComment,  // 导出 EditorComment
  ReaderReview,   // 导出 ReaderReview
  User,           // 导出 User 模型
  VolumeChapterGroupVersion, // 导出 VolumeChapterGroupVersion
  VolumeVersion,  // 导出 VolumeVersion
  Clue,           // 导出 Clue 模型
  AIConfig,       // 导出 AIConfig 模型
  // 创作流程相关模型
  WorkflowTemplate,
  WorkflowNode,
  WorkflowConnection,
  WorkflowExecution,
  WorkflowNodeExecution,
  WorkflowVersion,
  WorkflowCategory
};
