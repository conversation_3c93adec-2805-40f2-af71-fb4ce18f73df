const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const PromptTemplate = sequelize.define('PromptTemplate', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.STRING,
      allowNull: false
    },
    template: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT
    },
    parameters: {
      type: DataTypes.TEXT,
      get() {
        const value = this.getDataValue('parameters');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('parameters', JSON.stringify(value || []));
      }
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  });

  return PromptTemplate;
};
