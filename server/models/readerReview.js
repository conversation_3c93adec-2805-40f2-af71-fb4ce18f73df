const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ReaderReview = sequelize.define('ReaderReview', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    chapterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Chapters',
        key: 'id',
      },
    },
    // userId: { // 关联到评价的读者用户
    //   type: DataTypes.INTEGER,
    //   allowNull: false,
    //   references: {
    //     model: 'Users',
    //     key: 'id',
    //   },
    // },
    readerName: { // 暂时使用名称，未来可以关联用户表
      type: DataTypes.STRING,
      allowNull: true, // 或者 false，取决于是否强制要求
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5,
      },
    }
  }, {
    tableName: 'reader_reviews',
    timestamps: true
  });

  return ReaderReview;
};