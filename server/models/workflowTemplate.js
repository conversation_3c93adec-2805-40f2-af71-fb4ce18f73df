const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const WorkflowTemplate = sequelize.define('WorkflowTemplate', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '流程名称'
    },
    description: {
      type: DataTypes.TEXT,
      comment: '流程描述'
    },
    category: {
      type: DataTypes.STRING(100),
      comment: '流程分类'
    },
    version: {
      type: DataTypes.STRING(50),
      defaultValue: '1.0.0',
      comment: '版本号'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: '是否激活'
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否公开（admin可设置公开模板）'
    },
    config: {
      type: DataTypes.JSON,
      comment: '流程配置（节点、连接等）',
      get() {
        const value = this.getDataValue('config');
        return value || {};
      },
      set(value) {
        this.setDataValue('config', value || {});
      }
    },
    thumbnail: {
      type: DataTypes.TEXT,
      comment: '流程缩略图'
    },
    tags: {
      type: DataTypes.JSON,
      comment: '标签',
      get() {
        const value = this.getDataValue('tags');
        return value || [];
      },
      set(value) {
        this.setDataValue('tags', value || []);
      }
    },
    usageCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '使用次数'
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '创建者ID',
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    tableName: 'workflow_templates',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['category']
      },
      {
        fields: ['isPublic']
      },
      {
        fields: ['isActive']
      }
    ]
  });

  return WorkflowTemplate;
};
