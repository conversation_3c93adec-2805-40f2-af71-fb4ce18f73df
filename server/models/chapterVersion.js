const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ChapterVersion = sequelize.define('ChapterVersion', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    chapterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Chapters',
        key: 'id',
      },
    },
    versionNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
    }
  }, {
    tableName: 'chapter_versions',
    timestamps: true
  });

  return ChapterVersion;
};