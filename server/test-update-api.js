/**
 * 测试角色更新API
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

async function testUpdateAPI() {
  try {
    console.log('🔧 测试角色更新API...');

    // 生成JWT token
    const token = jwt.sign({ userId: 1 }, 'your-secret-key');
    console.log('生成的token:', token);

    // 准备更新数据
    const updateData = {
      name: '凌策',
      role: '主角',
      gender: '女',
      personality: '勇敢、聪明、善良',
      background: '来自一个普通家庭，但拥有特殊的能力',
      abilities_detail: '拥有预知未来的能力',
      characterArc: '从一个普通的学生成长为拯救世界的英雄，在这个过程中学会了责任和牺牲的意义',
      isMainCharacter: true,
      importance: 100
    };

    console.log('更新数据:', JSON.stringify(updateData, null, 2));

    // 发送更新请求
    const response = await axios.put('http://localhost:5001/api/characters/1', updateData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 更新成功!');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ 更新失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 执行测试
console.log('🚀 开始测试角色更新API...');
testUpdateAPI().then(() => {
  console.log('测试完成');
  process.exit(0);
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
