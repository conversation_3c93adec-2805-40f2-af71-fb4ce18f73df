# 数据库修复指南

## 问题描述

应用程序尝试访问不存在的数据库表 `reader_reviews`，导致以下错误：

```
Error: SQLITE_ERROR: no such table: reader_reviews
```

## 解决方案

我们提供了一个简单的修复脚本，它将创建所有缺少的表：
- `reader_reviews` - 存储读者评价
- `editor_comments` - 存储编辑意见
- `chapter_versions` - 存储章节版本历史

## 使用方法

1. 确保服务器已停止运行
2. 打开终端，进入服务器目录
3. 运行以下命令：

```bash
cd server
node fix-missing-tables.js
```

4. 如果看到"成功创建缺少的表！"的消息，说明修复成功
5. 重启服务器

## 注意事项

- 这个脚本只会创建表结构，不会添加任何数据
- 如果表已经存在，脚本不会修改它们
- 确保在运行脚本前备份您的数据库文件
