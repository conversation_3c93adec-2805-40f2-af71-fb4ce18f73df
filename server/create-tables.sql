-- 创建 chapter_versions 表（如果不存在）
CREATE TABLE IF NOT EXISTS chapter_versions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  versionNumber INTEGER NOT NULL,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);

-- 创建 editor_comments 表（如果不存在）
CREATE TABLE IF NOT EXISTS editor_comments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  editorName TEXT,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);

-- 创建 reader_reviews 表（如果不存在）
CREATE TABLE IF NOT EXISTS reader_reviews (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chapterId INTEGER NOT NULL,
  readerName TEXT,
  content TEXT NOT NULL,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
);

-- 创建 clues 表（如果不存在）
CREATE TABLE IF NOT EXISTS clues (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  projectId INTEGER NOT NULL,
  name VARCHAR(255) NOT NULL,
  type ENUM('重大事件', '重要道具', '重要伏笔') NOT NULL,
  description TEXT,
  firstAppearChapterId INTEGER,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (projectId) REFERENCES Projects(id) ON DELETE CASCADE,
  FOREIGN KEY (firstAppearChapterId) REFERENCES Chapters(id) ON DELETE SET NULL
);
