# 数据库修复指南

这个文件夹包含了用于修复数据库缺少表的脚本。错误信息显示数据库中缺少 `reader_reviews` 表，这导致了章节管理页面无法正常显示读者评价。

## 快速修复

执行以下命令来运行自动修复脚本：

```bash
cd server
node fix-database.js
```

这个脚本会尝试：
1. 安装必要的依赖
2. 运行 Sequelize 迁移来创建缺少的表
3. 如果迁移失败，则尝试直接执行 SQL 脚本

## 手动修复

如果自动修复失败，你可以尝试以下手动步骤：

### 方法 1: 使用 Sequelize 迁移

```bash
cd server
node install-deps.js
node run-migration.js
```

### 方法 2: 直接执行 SQL

```bash
cd server
node run-sql.js
```

### 方法 3: 使用 SQLite 命令行

如果上述方法都失败，你可以直接使用 SQLite 命令行工具：

```bash
cd server
sqlite3 database.sqlite < create-tables.sql
```

## 创建的表

这些脚本将创建以下表（如果它们不存在）：

1. `chapter_versions` - 存储章节的不同版本
2. `editor_comments` - 存储编辑对章节的评论
3. `reader_reviews` - 存储读者对章节的评价

## 故障排除

如果修复后仍然遇到问题：

1. 检查数据库文件路径是否正确（默认为 `server/database.sqlite`）
2. 确保 Chapters 表已经存在，因为这些新表都引用了它
3. 检查服务器日志以获取更详细的错误信息
4. 尝试备份并重新创建数据库
