const { execSync } = require('child_process');
const path = require('path');

console.log('开始修复数据库...');

// 步骤 1: 安装依赖
console.log('\n步骤 1: 安装必要的依赖');
try {
  execSync('node install-deps.js', { stdio: 'inherit' });
} catch (error) {
  console.error('安装依赖失败，但将继续尝试其他方法');
}

// 步骤 2: 尝试使用 Sequelize 迁移
console.log('\n步骤 2: 尝试使用 Sequelize 迁移');
try {
  execSync('node run-migration.js', { stdio: 'inherit' });
  console.log('Sequelize 迁移成功完成');
} catch (error) {
  console.error('Sequelize 迁移失败，将尝试直接 SQL 方法');
  
  // 步骤 3: 如果迁移失败，尝试直接 SQL
  console.log('\n步骤 3: 尝试使用直接 SQL 方法');
  try {
    execSync('node run-sql.js', { stdio: 'inherit' });
    console.log('SQL 脚本执行成功');
  } catch (sqlError) {
    console.error('SQL 脚本执行失败:', sqlError.message);
    console.log('\n所有自动修复方法都失败了。请手动检查数据库结构。');
    process.exit(1);
  }
}

console.log('\n数据库修复完成！');
console.log('现在可以重启服务器并尝试访问章节管理页面。');
