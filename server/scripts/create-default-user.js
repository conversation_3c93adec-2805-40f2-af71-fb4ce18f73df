const { sequelize, User } = require('../models');
const bcrypt = require('bcrypt');

// 创建默认用户
async function createDefaultUser() {
  try {
    console.log('开始创建默认用户...');
    
    // 检查是否已存在默认用户
    const existingUser = await User.findOne({ where: { username: 'admin' } });
    
    if (existingUser) {
      console.log('默认用户已存在，无需创建');
      return;
    }
    
    // 创建默认用户
    const defaultUser = await User.create({
      username: 'admin',
      password: 'Yinhai23', // 密码会在模型中自动哈希
      email: '<EMAIL>',
      role: 'admin'
    });
    
    console.log('默认用户创建成功:', {
      id: defaultUser.id,
      username: defaultUser.username,
      role: defaultUser.role
    });
  } catch (error) {
    console.error('创建默认用户失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行创建默认用户的函数
createDefaultUser();
