/**
 * 检查章节数据
 */

const { Chapter } = require('../models');

async function checkChapters() {
  try {
    console.log('检查章节数据...');
    
    const chapters = await Chapter.findAll({
      limit: 3,
      attributes: ['id', 'title', 'content', 'order', 'status']
    });

    console.log(`找到 ${chapters.length} 个章节:`);
    chapters.forEach(chapter => {
      console.log(`ID: ${chapter.id}, 标题: ${chapter.title}, 序号: ${chapter.order}, 状态: ${chapter.status}`);
      console.log(`内容长度: ${chapter.content ? chapter.content.length : 0} 字符`);
      console.log('---');
    });
    
  } catch (error) {
    console.error('检查章节数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkChapters()
    .then(() => {
      console.log('检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('检查失败:', error);
      process.exit(1);
    });
}

module.exports = checkChapters;
