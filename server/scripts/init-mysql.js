/**
 * MySQL数据库初始化脚本
 * 
 * 此脚本用于：
 * 1. 测试MySQL连接
 * 2. 使用Sequelize同步数据库模型
 */

const { Sequelize } = require('sequelize');
const config = require('../config/database');
const { sequelize } = require('../models');

// 获取当前环境的配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 初始化MySQL数据库
async function initMySQLDatabase() {
  try {
    console.log('开始初始化MySQL数据库...');
    
    // 检查数据库配置
    if (dbConfig.dialect !== 'mysql') {
      console.error('当前配置不是MySQL，请设置环境变量 DB_DIALECT=mysql');
      process.exit(1);
    }
    
    console.log('MySQL配置:', {
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      username: dbConfig.username
    });

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('MySQL数据库连接成功');

    // 同步数据库模型
    console.log('开始同步数据库模型...');
    await sequelize.sync({ alter: true });
    console.log('数据库模型同步成功');

    console.log('MySQL数据库初始化完成');
  } catch (error) {
    console.error('MySQL数据库初始化失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行初始化
initMySQLDatabase();
