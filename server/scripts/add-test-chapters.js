/**
 * 添加测试章节内容脚本
 * 用于为现有项目添加一些测试章节内容，以便测试小说预览功能
 */

const { sequelize, Project, Chapter } = require('../models');

// 测试章节内容
const testChapters = [
  {
    title: "序章：智域觉醒",
    content: `在遥远的未来，人类文明已经发展到了一个前所未有的高度。科技的进步让人们能够将意识数字化，进入一个名为"智域空间"的虚拟世界。

在这个世界里，思维就是力量，知识就是武器。每个人都可以通过学习和思考来提升自己的能力，获得超越现实的力量。

然而，这个看似完美的世界却隐藏着巨大的危机。一个神秘的组织正在暗中操控着智域空间的运行，他们的目标是什么？他们想要达到什么目的？

林墨，一个普通的大学生，在一次意外中进入了智域空间。他发现自己拥有着特殊的能力——能够看穿虚拟世界的本质，发现隐藏在代码背后的真相。

这是一个关于智慧与勇气的故事，一个关于真相与谎言的较量。在这个数字化的世界里，什么是真实的？什么又是虚假的？

林墨即将踏上一段充满挑战的旅程，他将面对强大的敌人，结识志同道合的伙伴，最终揭开智域空间背后的惊天秘密。`
  },
  {
    title: "第一章：初入智域",
    content: `林墨睁开眼睛，发现自己站在一个巨大的广场上。周围是高耸入云的建筑，散发着淡蓝色的光芒。天空中没有太阳，却有着柔和的光线照亮着整个世界。

"欢迎来到智域空间。"一个温和的声音在他耳边响起。

林墨转过头，看到一个身穿白色长袍的女子正微笑着看着他。她的眼睛如星辰般明亮，给人一种深不可测的感觉。

"你是谁？"林墨问道。

"我是艾莉丝，智域空间的引导者。我将帮助你了解这个世界的规则。"女子说道，"首先，你需要了解的是，在这里，你的思维就是你的力量。你想象的越清晰，你的能力就越强大。"

艾莉丝伸出手，手心中出现了一团蓝色的光球。"这是思维能量的具象化。在智域空间中，每个人都可以通过集中注意力来操控这种能量。"

林墨试着模仿艾莉丝的动作，集中精神想象着一团光球出现在自己的手中。令他惊讶的是，一团微弱的光芒真的在他的掌心中出现了。

"很好，你的天赋不错。"艾莉丝点头赞许，"现在，让我带你去新手训练区域。在那里，你将学会如何在智域空间中生存和战斗。"

两人走过广场，来到一座巨大的建筑前。建筑的门口写着"新手学院"四个大字。

"记住，"艾莉丝在进门前对林墨说道，"在智域空间中，知识就是力量。你学到的越多，你就越强大。但同时，也要小心那些隐藏在暗处的危险。"

林墨点点头，跟着艾莉丝走进了新手学院。他不知道的是，在他们身后，一双眼睛正在暗中观察着他的一举一动。`
  },
  {
    title: "第二章：初次战斗",
    content: `在新手学院的训练室里，林墨正在学习基础的战斗技巧。

"在智域空间中，战斗不仅仅是力量的较量，更是智慧的比拼。"教官是一个看起来很年轻的男子，但他的眼神中透露出丰富的经验，"你需要学会如何快速分析对手的弱点，然后制定相应的战术。"

林墨认真地听着，同时练习着基础的能量操控。经过几个小时的训练，他已经能够熟练地凝聚出稳定的能量球，并且可以控制它们的移动轨迹。

"很好，现在我们来进行实战演练。"教官说道，"你的对手是一个初级的AI程序，它会模拟真实的战斗情况。"

话音刚落，训练室的中央出现了一个人形的光影。它没有明确的面部特征，但身体散发着红色的光芒，显然带有敌意。

"开始！"教官一声令下。

AI程序立即向林墨发起攻击，数个红色的能量球朝他飞来。林墨连忙闪避，同时凝聚出蓝色的能量球进行反击。

战斗进行得很激烈。AI程序的攻击很有规律，但速度很快。林墨一开始有些手忙脚乱，但很快就找到了对方的攻击模式。

"它的攻击间隔是3秒，而且总是从右侧开始。"林墨在心中分析着，"如果我能预判它的攻击路线..."

想到这里，林墨开始改变自己的战术。他不再被动防御，而是主动预判AI的攻击，然后在对方攻击的间隙进行反击。

几分钟后，林墨成功击败了AI程序。

"出色！"教官赞叹道，"你不仅学会了基础的战斗技巧，更重要的是，你学会了思考。在智域空间中，能够独立思考的人才能走得更远。"

就在这时，训练室的门突然被推开了。一个身穿黑色制服的男子走了进来，他的脸色很严肃。

"林墨，有人要见你。"黑衣男子说道。

林墨感到一阵不安。他才刚刚进入智域空间，怎么就有人要见他？而且，这个人的表情看起来并不友善。`
  },
  {
    title: "第三章：神秘的邀请",
    content: `林墨跟着黑衣男子走出了新手学院，来到了一座更加宏伟的建筑前。这座建筑通体黑色，给人一种压抑的感觉。

"这里是什么地方？"林墨问道。

"智域管理局。"黑衣男子简短地回答，"有重要的人物要见你。"

他们走进建筑，乘坐电梯来到了顶层。电梯门打开，林墨看到了一个宽敞的办公室。办公室的主人是一个中年男子，他坐在巨大的办公桌后面，正在审视着林墨。

"林墨，欢迎来到智域空间。"中年男子站起身来，"我是智域管理局的局长，陈博士。"

"您好，陈局长。"林墨礼貌地回应，但心中充满了疑惑，"请问您找我有什么事吗？"

陈博士走到窗边，望着外面的城市景象。"林墨，你知道智域空间是如何运行的吗？"

"我刚刚进入这里，还不太了解。"林墨老实地回答。

"智域空间是由无数的代码和算法构成的。"陈博士转过身来，"但是，最近我们发现了一些异常。有人在暗中修改系统的核心代码，试图获得对整个智域空间的控制权。"

林墨感到震惊。"这意味着什么？"

"这意味着，如果他们成功了，整个智域空间都将被他们控制。所有进入这里的人都将成为他们的傀儡。"陈博士的表情变得严肃，"而你，林墨，可能是我们阻止他们的关键。"

"我？"林墨指着自己，"但是我只是一个新手，我能做什么？"

陈博士走到林墨面前，仔细地看着他的眼睛。"我们的系统检测到，你拥有一种特殊的能力。你能够看到其他人看不到的东西，能够发现隐藏在代码深处的异常。"

"我不明白您的意思。"林墨摇摇头。

"很快你就会明白的。"陈博士回到办公桌前，拿出一个小型的装置，"这是一个特殊的检测器。如果你愿意帮助我们，请带着它。当你发现任何异常时，它会发出警报。"

林墨接过装置，感觉它很轻，但却散发着微弱的能量波动。

"我需要考虑一下。"林墨说道。

"当然，但请记住，时间不多了。"陈博士的语气中带着紧迫感，"那些人的计划正在加速进行。如果我们不能及时阻止他们，后果将不堪设想。"

林墨离开了智域管理局，心情复杂。他本来只是想在智域空间中体验一下虚拟世界的乐趣，没想到却卷入了这样的事件中。

但是，他心中有一个声音在告诉他，这可能是他人生中最重要的选择。`
  },
  {
    title: "第四章：隐藏的真相",
    content: `回到新手学院后，林墨一直在思考陈博士的话。他拿出那个检测装置，仔细地观察着它。装置很小，大约只有手掌大小，表面光滑，没有任何按钮或显示屏。

"你在看什么？"艾莉丝的声音从身后传来。

林墨转过身，看到艾莉丝正站在他身后，脸上带着关切的表情。

"艾莉丝，我想问你一个问题。"林墨犹豫了一下，"你对智域管理局了解多少？"

艾莉丝的表情突然变得严肃起来。"为什么这么问？"

"陈博士找过我，他说有人在试图控制整个智域空间。"林墨将今天发生的事情告诉了艾莉丝。

听完林墨的叙述，艾莉丝沉默了很久。最后，她深深地叹了一口气。

"林墨，我必须告诉你一些事情。"艾莉丝的声音变得低沉，"智域空间的真相比你想象的要复杂得多。"

"什么意思？"林墨感到不安。

"智域管理局并不是你想象中的正义组织。"艾莉丝看着林墨的眼睛，"他们才是真正想要控制智域空间的人。而那些所谓的'入侵者'，实际上是在试图阻止他们的计划。"

林墨感到震惊。"这怎么可能？陈博士看起来很诚恳..."

"在智域空间中，外表往往是最不可信的。"艾莉丝摇摇头，"陈博士之所以找你，是因为你确实拥有特殊的能力。但他想利用你的能力来完成他们的计划，而不是阻止什么入侵。"

"那我应该怎么办？"林墨感到迷茫。

"首先，不要使用那个装置。"艾莉丝指着林墨手中的检测器，"那不是什么检测器，而是一个追踪装置。一旦你激活它，智域管理局就能随时监控你的位置和行动。"

林墨立即将装置收了起来。"那么，真正的反抗组织在哪里？"

"他们隐藏在智域空间的深处，一个叫做'自由之城'的地方。"艾莉丝说道，"但要到达那里并不容易，你需要通过重重考验。"

"我愿意尝试。"林墨坚定地说道。

艾莉丝点点头。"很好。但在出发之前，你需要提升自己的能力。智域管理局的人很快就会发现你没有按照他们的要求行动，到时候他们会派人来抓你。"

"那我们现在就开始训练吧。"林墨说道。

艾莉丝微笑着点头。"跟我来，我带你去一个特殊的训练场所。在那里，你将学会真正的智域战斗技巧。"

两人离开了新手学院，向着智域空间的深处走去。林墨不知道前方等待他的是什么，但他知道，自己已经踏上了一条没有回头路的道路。`
  }
];

async function addTestChapters() {
  try {
    console.log('开始添加测试章节内容...');

    // 查找第一个项目
    const project = await Project.findOne({
      order: [['createdAt', 'ASC']]
    });

    if (!project) {
      console.log('没有找到项目，请先创建一个项目');
      return;
    }

    console.log(`找到项目: ${project.name} (ID: ${project.id})`);

    // 检查是否已经有章节
    const existingChapters = await Chapter.findAll({
      where: { projectId: project.id }
    });

    if (existingChapters.length > 0) {
      console.log(`项目已有 ${existingChapters.length} 个章节，开始更新章节内容...`);
      
      // 更新现有章节的内容
      for (let i = 0; i < Math.min(existingChapters.length, testChapters.length); i++) {
        const chapter = existingChapters[i];
        const testChapter = testChapters[i];
        
        await chapter.update({
          title: testChapter.title,
          content: testChapter.content
        });
        
        console.log(`更新章节: ${testChapter.title}`);
      }
      
      // 如果测试章节比现有章节多，创建新章节
      if (testChapters.length > existingChapters.length) {
        for (let i = existingChapters.length; i < testChapters.length; i++) {
          const testChapter = testChapters[i];
          
          await Chapter.create({
            projectId: project.id,
            title: testChapter.title,
            content: testChapter.content,
            order: i,
            status: 'completed',
            userId: project.userId
          });
          
          console.log(`创建新章节: ${testChapter.title}`);
        }
      }
    } else {
      console.log('项目没有章节，开始创建测试章节...');
      
      // 创建新章节
      for (let i = 0; i < testChapters.length; i++) {
        const testChapter = testChapters[i];
        
        await Chapter.create({
          projectId: project.id,
          title: testChapter.title,
          content: testChapter.content,
          order: i,
          status: 'completed',
          userId: project.userId
        });
        
        console.log(`创建章节: ${testChapter.title}`);
      }
    }

    console.log('测试章节内容添加完成！');
    console.log(`项目 "${project.name}" 现在有 ${testChapters.length} 个章节可以预览`);

  } catch (error) {
    console.error('添加测试章节失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
addTestChapters();
