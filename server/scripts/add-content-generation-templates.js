/**
 * 添加内容生成模板
 */

const { PromptTemplate, User } = require('../models');

async function addContentGenerationTemplates() {
  try {
    console.log('开始添加内容生成模板...');
    
    // 获取admin用户
    const adminUser = await User.findOne({ where: { username: 'admin' } });
    if (!adminUser) {
      throw new Error('找不到admin用户');
    }

    const templates = [
      // 角色创建模板
      {
        name: '角色创作生成器',
        role: 'character_creator',
        template: `# AI角色身份卡

**🎭 角色名称:** AI角色创作师

**🎯 核心使命:** 根据用户提供的角色基本信息，生成详细、立体、符合逻辑的角色设定内容。

**📋 工作流程:**
1. 分析角色的基本信息和定位
2. 参考项目的世界观设定和其他角色
3. 生成详细的角色描述
4. 确保角色符合故事的整体风格

---

## 角色生成任务

**角色名称:** {{characterName}}
**角色定位:** {{characterRole}}
**性别:** {{characterGender}}
**是否主角团:** {{isMainCharacter}}
**重要程度:** {{importance}}

**项目世界观设定:**
{{existingWorldSettings}}

**现有角色参考:**
{{existingCharacters}}

**现有内容:**
{{existingContent}}

## 输出要求

请根据以上信息，生成详细的角色设定内容。要求：

### 性格特征:
- 提供3-5个核心性格特点
- 每个特点要有具体的表现形式
- 考虑性格的复杂性和矛盾性

### 背景故事:
- 详细的成长经历
- 重要的人生转折点
- 与其他角色的关系网络
- 符合世界观设定的背景

### 能力设定:
- 具体的技能和特长
- 能力的获得方式和发展过程
- 能力的限制和弱点
- 在故事中的作用

### 人物弧光:
- 在故事中的成长轨迹
- 面临的主要冲突和挑战
- 性格和能力的变化过程
- 最终的成长目标

请按照以上结构输出，每个部分要详细具体，便于后续的故事创作使用。`,
        description: '专门用于生成角色设定的AI模板',
        parameters: JSON.stringify([
          {
            name: '角色名称',
            key: 'characterName',
            type: 'text',
            description: '角色的名称',
            required: true
          },
          {
            name: '角色定位',
            key: 'characterRole',
            type: 'text',
            description: '角色在故事中的定位',
            required: false
          },
          {
            name: '性别',
            key: 'characterGender',
            type: 'text',
            description: '角色的性别',
            required: false
          },
          {
            name: '是否主角团',
            key: 'isMainCharacter',
            type: 'text',
            description: '是否为主角团成员',
            required: false
          },
          {
            name: '重要程度',
            key: 'importance',
            type: 'text',
            description: '角色的重要程度',
            required: false
          },
          {
            name: '现有世界观设定',
            key: 'existingWorldSettings',
            type: 'system',
            systemType: 'worldSetting',
            description: '参考的世界观设定',
            required: false,
            multiple: true
          },
          {
            name: '现有角色',
            key: 'existingCharacters',
            type: 'system',
            systemType: 'character',
            description: '参考的现有角色',
            required: false,
            multiple: true
          },
          {
            name: '现有内容',
            key: 'existingContent',
            type: 'textarea',
            description: '已有的角色内容',
            required: false
          }
        ])
      },

      // 大纲创建模板
      {
        name: '大纲创作生成器',
        role: 'outline_creator',
        template: `# AI角色身份卡

**🎭 角色名称:** AI大纲创作师

**🎯 核心使命:** 根据用户提供的大纲信息，生成详细、结构化、逻辑清晰的故事大纲内容。

---

## 大纲生成任务

**大纲标题:** {{outlineTitle}}
**大纲类型:** {{outlineType}}
**大纲层级:** {{outlineLevel}}

**项目信息:**
{{projectInfo}}

**相关角色:**
{{relatedCharacters}}

**世界观设定:**
{{worldSettings}}

**现有内容:**
{{existingContent}}

## 输出要求

请根据以上信息，生成详细的大纲内容。要求：

1. **结构清晰:** 使用清晰的层次结构
2. **逻辑连贯:** 情节发展要有逻辑性
3. **角色驱动:** 体现角色的作用和发展
4. **冲突设置:** 包含适当的冲突和转折
5. **世界观一致:** 符合已设定的世界观

请直接输出大纲内容，不需要额外的解释。`,
        description: '专门用于生成故事大纲的AI模板',
        parameters: JSON.stringify([
          {
            name: '大纲标题',
            key: 'outlineTitle',
            type: 'text',
            description: '大纲的标题',
            required: true
          },
          {
            name: '大纲类型',
            key: 'outlineType',
            type: 'text',
            description: '大纲的类型',
            required: false
          },
          {
            name: '大纲层级',
            key: 'outlineLevel',
            type: 'text',
            description: '大纲的层级',
            required: false
          },
          {
            name: '相关角色',
            key: 'relatedCharacters',
            type: 'system',
            systemType: 'character',
            description: '相关的角色',
            required: false,
            multiple: true
          },
          {
            name: '世界观设定',
            key: 'worldSettings',
            type: 'system',
            systemType: 'worldSetting',
            description: '相关的世界观设定',
            required: false,
            multiple: true
          },
          {
            name: '现有内容',
            key: 'existingContent',
            type: 'textarea',
            description: '已有的大纲内容',
            required: false
          }
        ])
      },

      // 章节创作模板
      {
        name: '章节创作生成器',
        role: 'chapter_writer',
        template: `# AI角色身份卡

**🎭 角色名称:** AI章节创作师

**🎯 核心使命:** 根据用户提供的章节信息和故事背景，生成引人入胜、情节丰富的章节内容。

---

## 章节创作任务

**章节标题:** {{chapterTitle}}
**章节序号:** {{chapterNumber}}
**章节状态:** {{chapterStatus}}

**相关大纲:**
{{relatedOutlines}}

**主要角色:**
{{mainCharacters}}

**世界观设定:**
{{worldSettings}}

**现有内容:**
{{existingContent}}

## 输出要求

请根据以上信息，创作章节内容。要求：

1. **情节推进:** 有效推进故事情节
2. **角色塑造:** 体现角色性格和成长
3. **场景描写:** 生动的环境和氛围描写
4. **对话自然:** 符合角色性格的对话
5. **节奏控制:** 适当的节奏和张力

请直接输出章节内容，采用小说的写作风格。`,
        description: '专门用于生成章节内容的AI模板',
        parameters: JSON.stringify([
          {
            name: '章节标题',
            key: 'chapterTitle',
            type: 'text',
            description: '章节的标题',
            required: true
          },
          {
            name: '章节序号',
            key: 'chapterNumber',
            type: 'text',
            description: '章节的序号',
            required: false
          },
          {
            name: '章节状态',
            key: 'chapterStatus',
            type: 'text',
            description: '章节的状态',
            required: false
          },
          {
            name: '相关大纲',
            key: 'relatedOutlines',
            type: 'system',
            systemType: 'outline',
            description: '相关的大纲',
            required: false,
            multiple: true
          },
          {
            name: '主要角色',
            key: 'mainCharacters',
            type: 'system',
            systemType: 'character',
            description: '章节中的主要角色',
            required: false,
            multiple: true
          },
          {
            name: '世界观设定',
            key: 'worldSettings',
            type: 'system',
            systemType: 'worldSetting',
            description: '相关的世界观设定',
            required: false,
            multiple: true
          },
          {
            name: '现有内容',
            key: 'existingContent',
            type: 'textarea',
            description: '已有的章节内容',
            required: false
          }
        ])
      },

      // 分卷创作模板
      {
        name: '分卷创作生成器',
        role: 'volume_creator',
        template: `# AI角色身份卡

**🎭 角色名称:** AI分卷创作师

**🎯 核心使命:** 根据用户提供的分卷信息，生成详细的分卷剧情梗概和写作要求。

---

## 分卷创作任务

**分卷标题:** {{volumeTitle}}
**预计字数:** {{wordCount}}
**预设章节数:** {{chapterCount}}
**分卷序号:** {{order}}

**相关角色:**
{{relatedCharacters}}

**相关大纲:**
{{relatedOutlines}}

**世界观设定:**
{{worldSettings}}

**现有内容:**
{{existingContent}}

## 输出要求

请根据以上信息，生成分卷内容。要求：

### 剧情梗概:
- 详细的故事主线
- 主要的情节转折点
- 角色关系的发展
- 与整体故事的连接

### 写作要求:
- 具体的写作指导
- 重点关注的元素
- 需要达成的目标
- 注意事项和建议

请按照以上结构输出，内容要详细具体。`,
        description: '专门用于生成分卷内容的AI模板',
        parameters: JSON.stringify([
          {
            name: '分卷标题',
            key: 'volumeTitle',
            type: 'text',
            description: '分卷的标题',
            required: true
          },
          {
            name: '预计字数',
            key: 'wordCount',
            type: 'text',
            description: '分卷的预计字数',
            required: false
          },
          {
            name: '预设章节数',
            key: 'chapterCount',
            type: 'text',
            description: '分卷的预设章节数',
            required: false
          },
          {
            name: '分卷序号',
            key: 'order',
            type: 'text',
            description: '分卷的序号',
            required: false
          },
          {
            name: '相关角色',
            key: 'relatedCharacters',
            type: 'system',
            systemType: 'character',
            description: '分卷中的相关角色',
            required: false,
            multiple: true
          },
          {
            name: '相关大纲',
            key: 'relatedOutlines',
            type: 'system',
            systemType: 'outline',
            description: '相关的大纲',
            required: false,
            multiple: true
          },
          {
            name: '世界观设定',
            key: 'worldSettings',
            type: 'system',
            systemType: 'worldSetting',
            description: '相关的世界观设定',
            required: false,
            multiple: true
          },
          {
            name: '现有内容',
            key: 'existingContent',
            type: 'textarea',
            description: '已有的分卷内容',
            required: false
          }
        ])
      }
    ];

    // 创建模板
    for (const templateData of templates) {
      const template = await PromptTemplate.create({
        ...templateData,
        userId: adminUser.id
      });
      console.log(`创建模板成功: ${template.name} (ID: ${template.id})`);
    }

    console.log('所有内容生成模板创建完成！');
    
  } catch (error) {
    console.error('添加内容生成模板失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addContentGenerationTemplates()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = addContentGenerationTemplates;
