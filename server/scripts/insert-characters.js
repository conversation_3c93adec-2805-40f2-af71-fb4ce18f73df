const { sequelize, Character, Project } = require('../models');

async function insertCharacters() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目
    const project = await Project.findOne({
      where: { name: '奕者游戏' }
    });

    if (!project) {
      console.error('未找到"奕者游戏"项目');
      return;
    }

    console.log(`找到项目: ${project.name}, ID: ${project.id}`);

    // 检查Characters表结构
    const [results] = await sequelize.query("DESCRIBE Characters");
    console.log('Characters表结构:');
    console.table(results);

    // 角色数据
    const charactersData = [
      {
        name: '林夜',
        role: '"心智枷锁"副手，代号"迷宫守护者"',
        gender: '男',
        personality: '阴沉内向，完全服从羲和，被其彻底操控。内心深处保留着一丝人性的挣扎',
        background: '原本是一名心理治疗师，在治疗过程中被羲和的理论"征服"，逐渐失去自我意识',
        abilities: '"记忆迷宫"（明慧境下品）',
        characterArc: '从助人者沦为害人者，在第三卷中制造信息混乱，最终可能在主角团的影响下重新找回人性',
        isMainCharacter: false,
        importance: 75,
        projectId: project.id
      },
      {
        name: '陈默',
        role: '"心智枷锁"执行者，代号"感染源"',
        gender: '男',
        personality: '曾经温和善良，被羲和操控后变得冷酷无情，但偶尔会流露出痛苦的表情',
        background: '心理学家，在研究群体心理时遇到羲和，被其理论彻底"征服"',
        abilities: '"情绪感染"（明慧境下品）',
        characterArc: '在团队合作中制造内部冲突，最终可能成为羲和理论破灭的关键证据',
        isMainCharacter: false,
        importance: 72,
        projectId: project.id
      },
      {
        name: '苏瑾',
        role: '"心智枷锁"观察者，代号"微眼"',
        gender: '女',
        personality: '原本自信独立，被击败后变得胆小谨慎，既恐惧又崇拜羲和',
        background: '原本是想要挑战羲和的弈者，结果被彻底击败后成为其工具',
        abilities: '"微观洞察"（启蒙境上品）',
        characterArc: '从挑战者沦为工具，负责收集情报，最终可能重新燃起反抗的勇气',
        isMainCharacter: false,
        importance: 70,
        projectId: project.id
      }
    ];

    // 检查是否已存在这些角色
    for (const characterData of charactersData) {
      const existingCharacter = await Character.findOne({
        where: { 
          name: characterData.name,
          projectId: project.id
        }
      });

      if (existingCharacter) {
        console.log(`角色 "${characterData.name}" 已存在，跳过插入`);
        continue;
      }

      // 插入角色
      const character = await Character.create(characterData);
      console.log(`成功插入角色: ${character.name} (ID: ${character.id})`);
    }

    console.log('角色数据插入完成');

  } catch (error) {
    console.error('插入角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
insertCharacters();
