/**
 * 创作流程配置功能数据库迁移脚本
 * 
 * 此脚本用于：
 * 1. 创建创作流程相关的数据库表
 * 2. 插入默认分类数据
 * 3. 验证表结构
 */

const fs = require('fs');
const path = require('path');
const { sequelize } = require('../models');

async function migrateWorkflowTables() {
  try {
    console.log('🚀 开始创作流程配置功能数据库迁移...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 获取数据库配置信息
    const dialect = sequelize.getDialect();
    console.log(`📊 数据库类型: ${dialect}`);

    if (dialect === 'mysql') {
      // MySQL数据库：使用Sequelize同步模型
      console.log('正在同步MySQL数据库模型...');

      // 使用 alter: true 来更新表结构，但保留数据
      await sequelize.sync({ alter: true });
      console.log('✅ 数据库模型同步成功');

      // 插入默认分类数据
      const { WorkflowCategory } = require('../models');
      const defaultCategories = [
        { name: '小说创作', description: '完整的小说创作流程', icon: 'book', sortOrder: 1 },
        { name: '角色设计', description: '角色创建和发展流程', icon: 'user', sortOrder: 2 },
        { name: '世界构建', description: '世界观和设定构建流程', icon: 'globe', sortOrder: 3 },
        { name: '情节规划', description: '故事情节和大纲规划流程', icon: 'timeline', sortOrder: 4 },
        { name: '内容生成', description: '章节和内容生成流程', icon: 'edit', sortOrder: 5 },
        { name: '审核编辑', description: '内容审核和编辑流程', icon: 'check', sortOrder: 6 },
        { name: '自定义', description: '用户自定义流程', icon: 'settings', sortOrder: 99 }
      ];

      for (const category of defaultCategories) {
        try {
          const [instance, created] = await WorkflowCategory.findOrCreate({
            where: { name: category.name },
            defaults: category
          });
          if (created) {
            console.log(`✅ 分类创建成功: ${category.name}`);
          } else {
            console.log(`⚠️  分类已存在: ${category.name}`);
          }
        } catch (error) {
          console.log(`❌ 分类创建失败: ${category.name}`, error.message);
        }
      }
    } else {
      // SQLite数据库：使用Sequelize同步
      console.log('正在同步SQLite数据库模型...');
      await sequelize.sync({ alter: true });
      
      // 插入默认分类数据
      const { WorkflowCategory } = require('../models');
      const defaultCategories = [
        { name: '小说创作', description: '完整的小说创作流程', icon: 'book', sortOrder: 1 },
        { name: '角色设计', description: '角色创建和发展流程', icon: 'user', sortOrder: 2 },
        { name: '世界构建', description: '世界观和设定构建流程', icon: 'globe', sortOrder: 3 },
        { name: '情节规划', description: '故事情节和大纲规划流程', icon: 'timeline', sortOrder: 4 },
        { name: '内容生成', description: '章节和内容生成流程', icon: 'edit', sortOrder: 5 },
        { name: '审核编辑', description: '内容审核和编辑流程', icon: 'check', sortOrder: 6 },
        { name: '自定义', description: '用户自定义流程', icon: 'settings', sortOrder: 99 }
      ];
      
      for (const category of defaultCategories) {
        try {
          await WorkflowCategory.findOrCreate({
            where: { name: category.name },
            defaults: category
          });
          console.log(`✅ 分类创建成功: ${category.name}`);
        } catch (error) {
          console.log(`⚠️  分类已存在: ${category.name}`);
        }
      }
    }

    console.log('正在验证表结构...');
    
    // 验证表是否创建成功
    const queryInterface = sequelize.getQueryInterface();
    const tablesToCheck = [
      'workflow_categories',
      'workflow_templates',
      'workflow_nodes',
      'workflow_connections',
      'workflow_executions',
      'workflow_node_executions',
      'workflow_versions'
    ];
    
    for (const tableName of tablesToCheck) {
      try {
        await queryInterface.describeTable(tableName);
        console.log(`✅ 表 ${tableName} 存在`);
      } catch (error) {
        console.log(`❌ 表 ${tableName} 不存在或有问题:`, error.message);
      }
    }

    console.log('\n🎉 创作流程配置功能数据库迁移完成！');
    console.log('已创建的表包括：');
    console.log('- workflow_categories (流程分类)');
    console.log('- workflow_templates (流程模板)');
    console.log('- workflow_nodes (流程节点)');
    console.log('- workflow_connections (流程连接)');
    console.log('- workflow_executions (流程执行记录)');
    console.log('- workflow_node_executions (节点执行记录)');
    console.log('- workflow_versions (流程版本)');
    console.log('\n现在可以开始使用创作流程配置功能了！');

  } catch (error) {
    console.error('❌ 数据库迁移失败:', error.message);
    
    if (error.name === 'SequelizeConnectionError') {
      console.error('数据库连接失败，请检查：');
      console.error('1. 数据库服务器是否正在运行');
      console.error('2. 连接参数是否正确');
      console.error('3. 数据库是否存在');
    } else if (error.name === 'SequelizeAccessDeniedError') {
      console.error('数据库访问被拒绝，请检查用户名和密码');
    } else {
      console.error('详细错误信息:', error);
    }
    
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateWorkflowTables();
}

module.exports = migrateWorkflowTables;
