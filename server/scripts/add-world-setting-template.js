/**
 * 添加世界观设定生成模板
 */

const { PromptTemplate, User } = require('../models');

async function addWorldSettingTemplate() {
  try {
    console.log('开始添加世界观设定模板...');
    
    // 获取admin用户
    const adminUser = await User.findOne({ where: { username: 'admin' } });
    if (!adminUser) {
      throw new Error('找不到admin用户');
    }

    // 创建世界观设定生成模板
    const template = await PromptTemplate.create({
      name: '世界观设定生成器',
      role: 'world_builder',
      template: `# AI角色身份卡

**🎭 角色名称:** AI世界观设定生成师

**🎯 核心使命:** 根据用户提供的要求和现有设定，生成详细、丰富、符合逻辑的世界观设定内容。

**📋 工作流程:**
1. 分析用户的设定要求和类别
2. 参考现有的世界观设定保持一致性
3. 生成详细、具体的设定内容
4. 确保设定符合小说的整体风格和逻辑

---

## 生成任务

**设定类别:** {{category}}
**设定标题:** {{title}}
**生成要求:** {{requirements}}

**现有相关设定:**
{{existingWorldSettings}}

**项目信息:**
- 项目名称: {{projectName}}
- 项目类型: {{projectType}}
- 目标受众: {{targetAudience}}
- 写作风格: {{style}}

**现有内容:**
{{existingContent}}

## 输出要求

请根据以上信息，生成详细的世界观设定内容。要求：

1. **内容丰富:** 提供具体、详细的设定描述
2. **逻辑一致:** 与现有设定保持一致，避免矛盾
3. **风格统一:** 符合项目的整体风格和基调
4. **结构清晰:** 使用清晰的段落和层次结构
5. **实用性强:** 便于后续的故事创作使用

请直接输出设定内容，不需要额外的解释或说明。`,
      description: '专门用于生成世界观设定的AI模板，支持多种参数配置',
      parameters: JSON.stringify([
        {
          name: '设定类别',
          key: 'category',
          type: 'text',
          description: '世界观设定的类别',
          required: true
        },
        {
          name: '设定标题',
          key: 'title',
          type: 'text',
          description: '世界观设定的标题',
          required: true
        },
        {
          name: '生成要求',
          key: 'requirements',
          type: 'textarea',
          description: '具体的生成要求和说明',
          required: false
        },
        {
          name: '现有世界观设定',
          key: 'existingWorldSettings',
          type: 'system',
          systemType: 'worldSetting',
          description: '参考的现有世界观设定',
          required: false,
          multiple: true
        },
        {
          name: '项目名称',
          key: 'projectName',
          type: 'text',
          description: '小说项目名称',
          required: false
        },
        {
          name: '项目类型',
          key: 'projectType',
          type: 'text',
          description: '小说项目类型',
          required: false
        },
        {
          name: '目标受众',
          key: 'targetAudience',
          type: 'text',
          description: '目标读者群体',
          required: false
        },
        {
          name: '写作风格',
          key: 'style',
          type: 'text',
          description: '写作风格',
          required: false
        },
        {
          name: '现有内容',
          key: 'existingContent',
          type: 'textarea',
          description: '已有的设定内容',
          required: false
        }
      ]),
      userId: adminUser.id
    });

    console.log('世界观设定模板创建成功:', template.name);
    console.log('模板ID:', template.id);
    
  } catch (error) {
    console.error('添加世界观设定模板失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addWorldSettingTemplate()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = addWorldSettingTemplate;
