/**
 * 测试编辑意见功能
 */

const { Chapter, EditorComment } = require('../models');

async function testEditorComments() {
  try {
    console.log('测试编辑意见功能...');
    
    // 获取第一个章节
    const chapter = await Chapter.findOne();
    if (!chapter) {
      console.log('没有找到章节数据');
      return;
    }

    console.log(`找到章节: ${chapter.title} (ID: ${chapter.id})`);

    // 检查是否已有编辑意见
    const existingComments = await EditorComment.findAll({
      where: { chapterId: chapter.id }
    });

    console.log(`现有编辑意见数量: ${existingComments.length}`);

    // 如果没有编辑意见，创建一些测试数据
    if (existingComments.length === 0) {
      console.log('创建测试编辑意见...');
      
      const testComments = [
        {
          chapterId: chapter.id,
          editorName: '主编',
          content: '这一章的开头很吸引人，但中间部分的节奏稍显缓慢。建议增加一些紧张感，让读者更投入。'
        },
        {
          chapterId: chapter.id,
          editorName: '文字编辑',
          content: '人物对话很自然，但有几处描述过于冗长。建议精简一些环境描写，突出重点情节。'
        },
        {
          chapterId: chapter.id,
          editorName: '内容编辑',
          content: '整体逻辑清晰，但主角的动机需要更明确的表达。读者可能会对他的某些行为感到困惑。'
        }
      ];

      for (const commentData of testComments) {
        await EditorComment.create(commentData);
        console.log(`创建编辑意见: ${commentData.content.substring(0, 20)}...`);
      }
    }

    // 重新获取编辑意见
    const comments = await EditorComment.findAll({
      where: { chapterId: chapter.id },
      order: [['createdAt', 'DESC']]
    });

    console.log('\n当前编辑意见列表:');
    comments.forEach((comment, index) => {
      console.log(`${index + 1}. [${comment.editorName}] ${comment.content}`);
      console.log(`   创建时间: ${comment.createdAt}`);
      console.log('');
    });

    console.log('编辑意见功能测试完成！');
    
  } catch (error) {
    console.error('测试编辑意见功能失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testEditorComments()
    .then(() => {
      console.log('测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = testEditorComments;
