/**
 * 直接修改数据库配置文件，切换到SQLite
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const configPath = path.resolve(__dirname, '../config/database.js');

// 读取配置文件
console.log('读取配置文件...');
let configContent = fs.readFileSync(configPath, 'utf8');

// 修改默认数据库类型
console.log('修改默认数据库类型为SQLite...');
configContent = configContent.replace(
  "const dbDialect = process.env.DB_DIALECT || 'mysql';",
  "const dbDialect = process.env.DB_DIALECT || 'sqlite';"
);

// 写入配置文件
console.log('保存配置文件...');
fs.writeFileSync(configPath, configContent);

console.log('配置文件已更新，默认数据库类型已设置为SQLite');
console.log('请重启应用以使用新的配置');
