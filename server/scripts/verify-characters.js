const { sequelize, Character, Project } = require('../models');

async function verifyCharacters() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目的所有角色
    const characters = await Character.findAll({
      where: { projectId: 1 },
      order: [['importance', 'DESC']]
    });

    console.log(`\n"奕者游戏"项目共有 ${characters.length} 个角色：\n`);

    characters.forEach((character, index) => {
      console.log(`${index + 1}. ${character.name} (重要程度：${character.importance})`);
      console.log(`   性别：${character.gender}`);
      console.log(`   角色：${character.role}`);
      console.log(`   性格：${character.personality}`);
      console.log(`   背景：${character.background}`);
      console.log(`   天赋：${character.abilities}`);
      console.log(`   人物弧光：${character.characterArc}`);
      console.log(`   是否主角：${character.isMainCharacter ? '是' : '否'}`);
      console.log(`   创建时间：${character.createdAt}`);
      console.log('---');
    });

  } catch (error) {
    console.error('查询角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
verifyCharacters();
