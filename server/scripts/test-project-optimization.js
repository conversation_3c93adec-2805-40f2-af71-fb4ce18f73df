/**
 * 测试项目管理接口优化
 */

const axios = require('axios');

async function testProjectOptimization() {
  try {
    console.log('开始测试项目管理接口优化...');
    
    // 首先登录获取token
    console.log('正在登录...');
    const loginResponse = await axios.post('http://localhost:5001/api/auth/login', {
      username: 'admin',
      password: 'Yinhai23'
    });
    
    const token = loginResponse.data.token;
    console.log('登录成功，获取到token');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 测试1: 获取项目基本信息（快速加载）
    console.log('\n=== 测试1: 获取项目基本信息 ===');
    const startTime1 = Date.now();
    const basicResponse = await axios.get('http://localhost:5001/api/projects-basic', { headers });
    const endTime1 = Date.now();
    
    console.log(`✅ 基本信息加载时间: ${endTime1 - startTime1}ms`);
    console.log(`项目数量: ${basicResponse.data.length}`);
    if (basicResponse.data.length > 0) {
      console.log('第一个项目:', {
        id: basicResponse.data[0].id,
        name: basicResponse.data[0].name,
        type: basicResponse.data[0].type
      });
    }

    // 测试2: 获取单个项目统计信息
    if (basicResponse.data.length > 0) {
      const projectId = basicResponse.data[0].id;
      console.log(`\n=== 测试2: 获取项目${projectId}的统计信息 ===`);
      const startTime2 = Date.now();
      const statsResponse = await axios.get(`http://localhost:5001/api/projects/${projectId}/stats`, { headers });
      const endTime2 = Date.now();
      
      console.log(`✅ 统计信息加载时间: ${endTime2 - startTime2}ms`);
      console.log('统计信息:', statsResponse.data);
    }

    // 测试3: 批量获取统计信息
    if (basicResponse.data.length > 0) {
      const projectIds = basicResponse.data.slice(0, 3).map(p => p.id); // 取前3个项目
      console.log(`\n=== 测试3: 批量获取统计信息 ===`);
      console.log(`项目IDs: [${projectIds.join(', ')}]`);
      const startTime3 = Date.now();
      const batchStatsResponse = await axios.post('http://localhost:5001/api/projects/batch-stats', 
        { projectIds }, 
        { headers }
      );
      const endTime3 = Date.now();
      
      console.log(`✅ 批量统计信息加载时间: ${endTime3 - startTime3}ms`);
      console.log('批量统计结果:', Object.keys(batchStatsResponse.data));
      
      // 显示每个项目的统计信息
      Object.entries(batchStatsResponse.data).forEach(([projectId, stats]) => {
        console.log(`项目${projectId}:`, {
          totalChapters: stats.totalChapters,
          progress: stats.progress,
          actualWordCount: stats.actualWordCount
        });
      });
    }

    // 测试4: 对比原接口性能
    console.log(`\n=== 测试4: 对比原接口性能 ===`);
    const startTime4 = Date.now();
    const originalResponse = await axios.get('http://localhost:5001/api/projects', { headers });
    const endTime4 = Date.now();
    
    console.log(`⚠️  原接口加载时间: ${endTime4 - startTime4}ms`);
    console.log(`项目数量: ${originalResponse.data.length}`);

    // 性能对比总结
    console.log(`\n=== 性能对比总结 ===`);
    console.log(`基本信息加载: ${endTime1 - startTime1}ms`);
    console.log(`原接口加载: ${endTime4 - startTime4}ms`);
    console.log(`性能提升: ${Math.round(((endTime4 - startTime4) - (endTime1 - startTime1)) / (endTime4 - startTime4) * 100)}%`);

    console.log('\n✅ 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testProjectOptimization()
    .then(() => {
      console.log('\n测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testProjectOptimization;
