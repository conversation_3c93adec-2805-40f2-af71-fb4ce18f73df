/**
 * 添加用户权限系统
 * 
 * 此脚本用于：
 * 1. 更新数据库表结构，为所有业务数据表添加userId字段
 * 2. 创建新的普通用户账号
 * 3. 将现有数据关联到admin用户
 */

const { sequelize, User, Project, WorldSetting, Character, Outline, Chapter, Volume, VolumeChapterGroup, Clue, PromptTemplate, GeneratedContent } = require('../models');

async function addUserPermissions() {
  try {
    console.log('开始添加用户权限系统...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 先确保用户表存在
    console.log('正在同步用户表...');
    await User.sync({ alter: true });
    console.log('✅ 用户表同步成功');

    // 查找admin用户
    let adminUser = await User.findOne({ where: { username: 'admin' } });

    if (!adminUser) {
      console.log('创建admin用户...');
      adminUser = await User.create({
        username: 'admin',
        password: 'Yinhai23',
        email: '<EMAIL>',
        role: 'admin'
      });
      console.log('✅ admin用户创建成功');
    } else {
      console.log('✅ admin用户已存在');
    }

    // 创建普通用户
    let normalUser = await User.findOne({ where: { username: 'liurenhao' } });

    if (!normalUser) {
      console.log('创建普通用户 liurenhao...');
      normalUser = await User.create({
        username: 'liurenhao',
        password: 'Yinhai123',
        email: '<EMAIL>',
        role: 'user'
      });
      console.log('✅ 普通用户 liurenhao 创建成功');
    } else {
      console.log('✅ 普通用户 liurenhao 已存在');
    }

    // 手动添加userId字段到各个表
    console.log('正在手动添加userId字段...');
    const queryInterface = sequelize.getQueryInterface();

    // 检查并添加userId字段到Projects表
    try {
      const projectsColumns = await queryInterface.describeTable('Projects');
      if (!projectsColumns.userId) {
        console.log('为Projects表添加userId字段...');
        await queryInterface.addColumn('Projects', 'userId', {
          type: sequelize.Sequelize.INTEGER,
          allowNull: true, // 先设为可空
          references: {
            model: 'Users',
            key: 'id'
          }
        });
        // 将现有数据的userId设为admin用户ID
        await sequelize.query(`UPDATE Projects SET userId = ${adminUser.id} WHERE userId IS NULL`);
        // 然后设为非空
        await queryInterface.changeColumn('Projects', 'userId', {
          type: sequelize.Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'Users',
            key: 'id'
          }
        });
        console.log('✅ Projects表userId字段添加成功');
      }
    } catch (error) {
      console.log('Projects表userId字段可能已存在');
    }

    // 为其他表添加userId字段
    const tables = [
      { name: 'WorldSettings', model: 'WorldSetting' },
      { name: 'Characters', model: 'Character' },
      { name: 'Outlines', model: 'Outline' },
      { name: 'Chapters', model: 'Chapter' },
      { name: 'Volumes', model: 'Volume' },
      { name: 'VolumeChapterGroups', model: 'VolumeChapterGroup' },
      { name: 'clues', model: 'Clue' },
      { name: 'PromptTemplates', model: 'PromptTemplate' },
      { name: 'GeneratedContents', model: 'GeneratedContent' }
    ];

    for (const table of tables) {
      try {
        const columns = await queryInterface.describeTable(table.name);
        if (!columns.userId) {
          console.log(`为${table.name}表添加userId字段...`);
          await queryInterface.addColumn(table.name, 'userId', {
            type: sequelize.Sequelize.INTEGER,
            allowNull: true,
            references: {
              model: 'Users',
              key: 'id'
            }
          });
          // 将现有数据的userId设为admin用户ID
          await sequelize.query(`UPDATE ${table.name} SET userId = ${adminUser.id} WHERE userId IS NULL`);
          // 然后设为非空
          await queryInterface.changeColumn(table.name, 'userId', {
            type: sequelize.Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'Users',
              key: 'id'
            }
          });
          console.log(`✅ ${table.name}表userId字段添加成功`);
        }
      } catch (error) {
        console.log(`${table.name}表userId字段可能已存在或表不存在`);
      }
    }

    console.log('\n🎉 用户权限系统添加完成！');
    console.log('用户账号信息：');
    console.log('1. 管理员账号：');
    console.log('   用户名：admin');
    console.log('   密码：Yinhai23');
    console.log('   权限：可以查看所有数据');
    console.log('');
    console.log('2. 普通用户账号：');
    console.log('   用户名：liurenhao');
    console.log('   密码：Yinhai123');
    console.log('   权限：只能查看自己创建的数据');

  } catch (error) {
    console.error('❌ 添加用户权限系统失败:', error.message);
    
    if (error.name === 'SequelizeConnectionError') {
      console.error('数据库连接失败，请检查：');
      console.error('1. 数据库服务器是否正在运行');
      console.error('2. 连接参数是否正确');
      console.error('3. 数据库是否存在');
    } else if (error.name === 'SequelizeAccessDeniedError') {
      console.error('数据库访问被拒绝，请检查用户名和密码');
    } else {
      console.error('详细错误信息:', error);
    }
    
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行脚本
addUserPermissions();
