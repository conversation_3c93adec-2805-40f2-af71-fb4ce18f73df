/**
 * 添加AI配置表
 * 
 * 此脚本用于：
 * 1. 创建ai_configs表
 * 2. 添加必要的索引
 */

const { sequelize, AIConfig } = require('../models');

async function addAIConfigTable() {
  try {
    console.log('开始创建AI配置表...');
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 同步AIConfig模型（创建表）
    await AIConfig.sync({ force: false });
    console.log('AI配置表创建成功');
    
    console.log('AI配置表添加完成！');
    
  } catch (error) {
    console.error('添加AI配置表失败:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addAIConfigTable()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = addAIConfigTable;
