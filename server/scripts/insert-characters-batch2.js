const { sequelize, Character, Project } = require('../models');

async function insertCharactersBatch2() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目
    const project = await Project.findOne({
      where: { name: '奕者游戏' }
    });

    if (!project) {
      console.error('未找到"奕者游戏"项目');
      return;
    }

    console.log(`找到项目: ${project.name}, ID: ${project.id}`);

    // 第二批次角色数据 - 牺牲者/反面教材
    const charactersData = [
      {
        name: '张明',
        role: '第一卷中失败的弈者代表',
        gender: '男',
        personality: '自私自利，缺乏团队精神',
        background: '成功商人，习惯于独来独往',
        abilities_detail: '"利益嗅觉"（启蒙境中品）',
        characterArc: '因为过度自私而在游戏中失败，成为主角团的反面教材',
        isMainCharacter: false,
        importance: 50,
        projectId: project.id
      },
      {
        name: '刘芳',
        role: '第二卷中精神崩溃的弈者',
        gender: '女',
        personality: '脆弱敏感，无法承受游戏压力',
        background: '艺术系学生，心理承受能力较弱',
        abilities_detail: '"美感直觉"（启蒙境下品）',
        characterArc: '展现智域空间的残酷性，让主角团意识到保护弱者的重要性',
        isMainCharacter: false,
        importance: 45,
        projectId: project.id
      }
    ];

    let insertedCount = 0;
    let skippedCount = 0;

    // 逐个插入角色
    for (const characterData of charactersData) {
      // 检查是否已存在该角色
      const existingCharacter = await Character.findOne({
        where: { 
          name: characterData.name,
          projectId: project.id
        }
      });

      if (existingCharacter) {
        console.log(`角色 "${characterData.name}" 已存在，跳过插入`);
        skippedCount++;
        continue;
      }

      // 插入角色
      const character = await Character.create(characterData);
      console.log(`✅ 成功插入角色: ${character.name} (ID: ${character.id}, 重要程度: ${character.importance})`);
      console.log(`   性别: ${character.gender}`);
      console.log(`   天赋: ${character.abilities_detail}`);
      console.log(`   人物弧光: ${character.characterArc}`);
      console.log('---');
      insertedCount++;
    }

    console.log(`\n第二批次插入完成: 新增 ${insertedCount} 个角色，跳过 ${skippedCount} 个已存在角色`);

  } catch (error) {
    console.error('插入角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
insertCharactersBatch2();
