const { spawn } = require('child_process');
const path = require('path');

async function runScript(scriptName) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 开始执行 ${scriptName}...`);
    console.log('='.repeat(50));
    
    const scriptPath = path.join(__dirname, scriptName);
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${scriptName} 执行完成`);
        console.log('='.repeat(50));
        resolve();
      } else {
        console.error(`❌ ${scriptName} 执行失败，退出码: ${code}`);
        reject(new Error(`Script ${scriptName} failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.error(`❌ 执行 ${scriptName} 时发生错误:`, error);
      reject(error);
    });
  });
}

async function runAllBatches() {
  try {
    console.log('🎯 开始批量插入角色数据到"奕者游戏"项目');
    console.log('📝 总共分为3个批次执行\n');

    // 执行第一批次 - 重要NPC和后期角色
    await runScript('insert-characters-batch1.js');
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 执行第二批次 - 牺牲者/反面教材
    await runScript('insert-characters-batch2.js');
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 执行第三批次 - 盟友团队成员
    await runScript('insert-characters-batch3.js');

    console.log('\n🎉 所有批次执行完成！');
    console.log('📊 建议运行验证脚本查看插入结果:');
    console.log('   node server/scripts/verify-characters.js');

  } catch (error) {
    console.error('\n❌ 批量插入过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行所有批次
runAllBatches();
