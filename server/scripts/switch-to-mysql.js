/**
 * 数据库切换脚本 - 从SQLite切换到MySQL
 *
 * 此脚本用于：
 * 1. 更新环境变量，将数据库类型切换为MySQL
 * 2. 创建MySQL数据库（如果不存在）
 * 3. 执行迁移脚本，创建表结构并导入数据
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2');
const config = require('../config/database');

// MySQL配置
const mysqlConfig = config.development.dialect === 'mysql'
  ? config.development
  : {
      host: process.env.DB_HOST || 'cd-cdb-8yjrt2ao.sql.tencentcdb.com',
      port: process.env.DB_PORT || 27134,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'Zjy@2025',
      database: process.env.DB_NAME || 'xiaoshuo2'
    };

// 迁移SQL文件路径
const migrationSqlPath = path.resolve(__dirname, '../database/sqlite-to-mysql.sql');

// 设置环境变量
function setEnvironmentVariable() {
  console.log('设置环境变量，切换到MySQL...');

  // 直接设置当前进程的环境变量
  process.env.DB_DIALECT = 'mysql';
  console.log('环境变量已设置: DB_DIALECT=mysql');

  // 创建或更新.env文件
  try {
    const envPath = path.resolve(__dirname, '../.env');
    let envContent = '';

    // 如果.env文件已存在，读取内容
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');

      // 替换或添加DB_DIALECT
      if (envContent.includes('DB_DIALECT=')) {
        envContent = envContent.replace(/DB_DIALECT=.*/g, 'DB_DIALECT=mysql');
      } else {
        envContent += '\nDB_DIALECT=mysql';
      }
    } else {
      envContent = 'DB_DIALECT=mysql';
    }

    // 写入.env文件
    fs.writeFileSync(envPath, envContent);
    console.log('.env文件已更新');
  } catch (error) {
    console.error('.env文件更新失败:', error.message);
    console.log('请手动设置环境变量: DB_DIALECT=mysql');
  }
}

// 创建MySQL数据库
async function createMySQLDatabase() {
  console.log('连接到MySQL服务器...');

  // 创建不指定数据库的连接
  const connection = mysql.createConnection({
    host: mysqlConfig.host,
    port: mysqlConfig.port,
    user: mysqlConfig.user || mysqlConfig.username,
    password: mysqlConfig.password
  });

  return new Promise((resolve, reject) => {
    connection.connect((err) => {
      if (err) {
        console.error('连接MySQL服务器失败:', err.message);
        return reject(err);
      }

      console.log('已连接到MySQL服务器');

      // 创建数据库
      const dbName = mysqlConfig.database;
      const createDbSql = `CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`;

      connection.query(createDbSql, (err) => {
        if (err) {
          console.error(`创建数据库 ${dbName} 失败:`, err.message);
          connection.end();
          return reject(err);
        }

        console.log(`数据库 ${dbName} 已创建或已存在`);
        connection.end();
        resolve();
      });
    });
  });
}

// 执行迁移SQL
async function executeMigrationSQL() {
  console.log('执行迁移SQL...');

  if (!fs.existsSync(migrationSqlPath)) {
    console.error(`迁移SQL文件不存在: ${migrationSqlPath}`);
    console.log('请先运行 node scripts/sqlite-to-mysql.js 生成迁移SQL文件');
    return false;
  }

  try {
    // 使用mysql命令行工具执行SQL文件
    const command = `mysql -h ${mysqlConfig.host} -P ${mysqlConfig.port} -u ${mysqlConfig.user || mysqlConfig.username} -p${mysqlConfig.password} ${mysqlConfig.database} < ${migrationSqlPath}`;

    console.log('正在导入数据...');
    execSync(command, { stdio: 'inherit' });

    console.log('迁移SQL执行成功');
    return true;
  } catch (error) {
    console.error('执行迁移SQL失败:', error.message);
    console.log('请手动执行迁移SQL文件');
    return false;
  }
}

// 主函数
async function switchToMySQL() {
  console.log('开始从SQLite切换到MySQL...');

  try {
    // 生成迁移SQL
    console.log('生成迁移SQL...');
    execSync('node scripts/sqlite-to-mysql.js', { stdio: 'inherit' });

    // 设置环境变量
    setEnvironmentVariable();

    // 创建MySQL数据库
    await createMySQLDatabase();

    // 执行迁移SQL
    const success = await executeMigrationSQL();

    if (success) {
      console.log('\n数据库已成功切换到MySQL!');
      console.log('请重启应用以使用新的数据库配置');
    } else {
      console.log('\n数据库切换未完成，请检查错误并手动完成迁移');
    }
  } catch (error) {
    console.error('切换数据库过程中出错:', error);
  }
}

// 执行切换
switchToMySQL();
