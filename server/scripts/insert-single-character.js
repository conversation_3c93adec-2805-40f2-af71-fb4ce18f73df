const { sequelize, Character, Project } = require('../models');

async function insertSingleCharacter() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目
    const project = await Project.findOne({
      where: { name: '奕者游戏' }
    });

    if (!project) {
      console.error('未找到"奕者游戏"项目');
      return;
    }

    console.log(`找到项目: ${project.name}, ID: ${project.id}`);

    // 角色数据 - 莫寒烟
    const characterData = {
      name: '莫寒烟',
      role: '"风暴眼"副队长，擅长信息战',
      gender: '女',
      personality: '冷静理智，善于分析，对主角团的林薇颇有好感',
      background: '前军方情报分析师，具有丰富的实战经验',
      abilities_detail: '"信息截获"（明慧境中品）',
      characterArc: '与林薇形成既竞争又合作的关系，最终成为重要的信息来源',
      isMainCharacter: false,
      importance: 70,
      projectId: project.id
    };

    // 检查是否已存在该角色
    const existingCharacter = await Character.findOne({
      where: {
        name: characterData.name,
        projectId: project.id
      }
    });

    if (existingCharacter) {
      console.log(`角色 "${characterData.name}" 已存在，跳过插入`);
      return;
    }

    // 插入角色
    const character = await Character.create(characterData);
    console.log(`成功插入角色: ${character.name} (ID: ${character.id})`);
    console.log(`  性别: ${character.gender}`);
    console.log(`  重要程度: ${character.importance}`);
    console.log(`  能力设定: ${character.abilities_detail}`);
    console.log(`  人物弧光: ${character.characterArc}`);

  } catch (error) {
    console.error('插入角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
insertSingleCharacter();
