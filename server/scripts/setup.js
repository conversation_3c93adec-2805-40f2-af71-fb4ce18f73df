const { spawn } = require('child_process');
const path = require('path');

// 运行脚本
function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    console.log(`运行脚本: ${scriptPath}`);
    
    const process = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: path.dirname(scriptPath)
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`脚本 ${scriptPath} 运行成功`);
        resolve();
      } else {
        console.error(`脚本 ${scriptPath} 运行失败，退出码: ${code}`);
        reject(new Error(`脚本退出码: ${code}`));
      }
    });
    
    process.on('error', (err) => {
      console.error(`启动脚本 ${scriptPath} 时出错:`, err);
      reject(err);
    });
  });
}

// 按顺序运行脚本
async function setup() {
  try {
    console.log('开始设置...');
    
    // 初始化数据库
    await runScript(path.join(__dirname, 'init-database.js'));
    
    // 创建默认用户
    await runScript(path.join(__dirname, 'create-default-user.js'));
    
    console.log('设置完成');
  } catch (error) {
    console.error('设置失败:', error);
    process.exit(1);
  }
}

// 执行设置
setup();
