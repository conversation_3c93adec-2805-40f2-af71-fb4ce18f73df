const { sequelize, Character, Project } = require('../models');

async function insertCharactersBatch3() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目
    const project = await Project.findOne({
      where: { name: '奕者游戏' }
    });

    if (!project) {
      console.error('未找到"奕者游戏"项目');
      return;
    }

    console.log(`找到项目: ${project.name}, ID: ${project.id}`);

    // 第三批次角色数据 - 盟友团队成员
    const charactersData = [
      {
        name: '钢铁汉子·大山',
        role: '"钢铁意志"团队的副队长',
        gender: '男',
        personality: '豪爽直率，忠诚可靠',
        background: '建筑工人，拥有强大的意志力',
        abilities_detail: '"坚韧不拔"（明慧境下品）',
        characterArc: '与赵越形成友谊，在关键时刻提供支援',
        isMainCharacter: false,
        importance: 55,
        projectId: project.id
      },
      {
        name: '智者·老周',
        role: '资深弈者，经验丰富的导师型人物',
        gender: '男',
        personality: '睿智慈祥，愿意指导后辈',
        background: '退休教授，在智域空间中生存较久',
        abilities_detail: '"经验累积"（明慧境中品）',
        characterArc: '为主角团提供宝贵建议，可能在后期做出重要牺牲',
        isMainCharacter: false,
        importance: 50,
        projectId: project.id
      },
      {
        name: '快刀手·小李',
        role: '年轻的独行弈者，后来加入联盟',
        gender: '男',
        personality: '冲动易怒，但本性善良',
        background: '厨师学徒，手法灵活',
        abilities_detail: '"速度爆发"（启蒙境上品）',
        characterArc: '从独行者成长为团队的一员，学会合作的重要性',
        isMainCharacter: false,
        importance: 45,
        projectId: project.id
      },
      {
        name: '治疗师·安娜',
        role: '专门负责心理治疗的弈者',
        gender: '女',
        personality: '温柔体贴，富有同情心',
        background: '心理医生，在现实中就帮助他人',
        abilities_detail: '"心灵慰藉"（明慧境下品）',
        characterArc: '在第三卷的心理游戏中发挥重要作用，帮助受创者恢复',
        isMainCharacter: false,
        importance: 48,
        projectId: project.id
      },
      {
        name: '预言家·老神',
        role: '神秘的预言型弈者',
        gender: '男',
        personality: '疯疯癫癫，但偶尔能说出真相',
        background: '流浪汉，被认为精神异常但实际很有智慧',
        abilities_detail: '"混沌预知"（启蒙境上品，但极不稳定）',
        characterArc: '为故事增添神秘色彩，在关键时刻提供重要预言',
        isMainCharacter: false,
        importance: 42,
        projectId: project.id
      }
    ];

    let insertedCount = 0;
    let skippedCount = 0;

    // 逐个插入角色
    for (const characterData of charactersData) {
      // 检查是否已存在该角色
      const existingCharacter = await Character.findOne({
        where: { 
          name: characterData.name,
          projectId: project.id
        }
      });

      if (existingCharacter) {
        console.log(`角色 "${characterData.name}" 已存在，跳过插入`);
        skippedCount++;
        continue;
      }

      // 插入角色
      const character = await Character.create(characterData);
      console.log(`✅ 成功插入角色: ${character.name} (ID: ${character.id}, 重要程度: ${character.importance})`);
      console.log(`   性别: ${character.gender}`);
      console.log(`   天赋: ${character.abilities_detail}`);
      console.log(`   人物弧光: ${character.characterArc}`);
      console.log('---');
      insertedCount++;
    }

    console.log(`\n第三批次插入完成: 新增 ${insertedCount} 个角色，跳过 ${skippedCount} 个已存在角色`);

  } catch (error) {
    console.error('插入角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
insertCharactersBatch3();
