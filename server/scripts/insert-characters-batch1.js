const { sequelize, Character, Project } = require('../models');

async function insertCharactersBatch1() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目
    const project = await Project.findOne({
      where: { name: '奕者游戏' }
    });

    if (!project) {
      console.error('未找到"奕者游戏"项目');
      return;
    }

    console.log(`找到项目: ${project.name}, ID: ${project.id}`);

    // 第一批次角色数据 - 重要NPC和后期角色
    const charactersData = [
      {
        name: '向导·瑞雯',
        role: '智域空间的新手向导，类似NPC的存在',
        gender: '女',
        personality: '亲切友善，但透露信息有限',
        background: '可能是早期弈者的意识残留，或是系统生成的AI',
        abilities_detail: '未知',
        characterArc: '在第一卷中引导主角团了解基本规则，后续可能揭示更深层的秘密',
        isMainCharacter: false,
        importance: 60,
        projectId: project.id
      },
      {
        name: '商人·格雷',
        role: '智域空间的道具商人',
        gender: '男',
        personality: '精明圆滑，永远在寻找最大利益',
        background: '长期生存在智域空间的神秘存在',
        abilities_detail: '"价值交换"（明慧境？）',
        characterArc: '为各个团队提供道具和信息交换，可能掌握重要线索',
        isMainCharacter: false,
        importance: 55,
        projectId: project.id
      },
      {
        name: '暗影导师',
        role: '第四卷中出现的神秘导师',
        gender: '未设定',
        personality: '深不可测，既是敌人也是老师',
        background: '可能是早期达到通玄境的弈者',
        abilities_detail: '未知（通玄境）',
        characterArc: '指导主角团突破天赋境界，但代价不明',
        isMainCharacter: false,
        importance: 70,
        projectId: project.id
      },
      {
        name: '时空行者·诺亚',
        role: '第四卷"破碎石板的预言"中的关键人物',
        gender: '男',
        personality: '神秘莫测，似乎知道智域空间的终极秘密',
        background: '可能来自其他时间线或维度的弈者',
        abilities_detail: '"时间感知"（通玄境下品）',
        characterArc: '为主角团提供关于智核的关键信息，但动机不明',
        isMainCharacter: false,
        importance: 65,
        projectId: project.id
      },
      {
        name: '记录者·卡西',
        role: '智域空间的历史记录者',
        gender: '女',
        personality: '沉默寡言，专注于记录每一场游戏',
        background: '早期弈者，选择成为观察者而非参与者',
        abilities_detail: '"记忆殿堂"（明慧境上品）',
        characterArc: '在第三卷中帮助主角团理解记忆相关的游戏机制',
        isMainCharacter: false,
        importance: 60,
        projectId: project.id
      }
    ];

    let insertedCount = 0;
    let skippedCount = 0;

    // 逐个插入角色
    for (const characterData of charactersData) {
      // 检查是否已存在该角色
      const existingCharacter = await Character.findOne({
        where: { 
          name: characterData.name,
          projectId: project.id
        }
      });

      if (existingCharacter) {
        console.log(`角色 "${characterData.name}" 已存在，跳过插入`);
        skippedCount++;
        continue;
      }

      // 插入角色
      const character = await Character.create(characterData);
      console.log(`✅ 成功插入角色: ${character.name} (ID: ${character.id}, 重要程度: ${character.importance})`);
      console.log(`   性别: ${character.gender}`);
      console.log(`   天赋: ${character.abilities_detail}`);
      console.log(`   人物弧光: ${character.characterArc}`);
      console.log('---');
      insertedCount++;
    }

    console.log(`\n第一批次插入完成: 新增 ${insertedCount} 个角色，跳过 ${skippedCount} 个已存在角色`);

  } catch (error) {
    console.error('插入角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
insertCharactersBatch1();
