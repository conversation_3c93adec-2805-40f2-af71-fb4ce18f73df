/**
 * 同步MySQL数据库模型
 * 
 * 此脚本用于：
 * 1. 连接到MySQL数据库
 * 2. 使用Sequelize模型定义同步表结构
 * 3. 导入基本数据
 */

const { sequelize, Project, WorldSetting, Character, Outline, Chapter, PromptTemplate, User } = require('../models');

// 主函数
async function syncMySQLModels() {
  console.log('开始同步MySQL数据库模型...');
  
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('MySQL数据库连接成功');
    
    // 同步数据库模型
    console.log('开始同步数据库模型...');
    await sequelize.sync({ force: true });
    console.log('数据库模型同步成功');
    
    // 导入基本数据
    console.log('开始导入基本数据...');
    
    // 创建默认用户
    const defaultUser = await User.create({
      username: 'admin',
      password: 'Yinhai23',
      email: '<EMAIL>',
      role: 'admin'
    });
    console.log('默认用户创建成功');
    
    // 创建示例项目
    const project = await Project.create({
      name: '奕者游戏',
      type: 'mystery',
      wordCount: 1000000,
      targetAudience: '智斗、悬疑、玄幻、团队合作爱好者',
      style: '逻辑缜密'
    });
    console.log('示例项目创建成功');
    
    // 创建世界观设定
    await WorldSetting.bulkCreate([
      {
        projectId: project.id,
        title: '现实背景',
        content: '○故事发生在近未来，科技发展，信息爆炸，但社会结构与现代类似。人们在现实生活中也面临各种无形的"博弈"。',
        category: 'history_background'
      },
      {
        projectId: project.id,
        title: '智域空间',
        content: '○一个通过特殊精神共振或未知科技手段连接的亚空间/虚拟实境。它并非实体存在于某处，而是当"游戏"开始时，被选中的"弈者"意识会被接入。在智域空间中，感官体验高度拟真。',
        category: 'geo_culture'
      }
    ]);
    console.log('世界观设定创建成功');
    
    // 创建大纲
    await Outline.create({
      projectId: project.id,
      title: '故事总纲',
      level: 'level1',
      content: '《弈者游戏》故事梗概\n故事始于认知心理学研究生凌策，他凭借对博弈论的深厚研究与一篇关于群体决策模型的论文，意外收到了来自神秘"智域空间"的"邀请函"。这个通过特殊精神共振连接的亚空间，将他的意识接入了一个感官高度拟真的奇境。甫一进入，凌策便觉醒了名为"星轨推演"的初始天赋，能够在脑海中构建动态星图，模拟推演局势的多种可能性，尽管初时运用尚显生涩，精神力消耗巨大，仅为启蒙境下品的"初显"状态。...'
    });
    console.log('大纲创建成功');
    
    // 创建提示词模板
    await PromptTemplate.create({
      name: '人物塑造师',
      role: 'world_builder',
      template: '# AI角色身份卡\n\n**🎭 角色名称:** AI人物塑造师\n\n**🎯 核心使命:** 深度挖掘和构建核心角色的生平、性格、动机和成长弧光，确保人物形象丰满、真实、独特且具有发展性。在章节写作中，作为角色一致性的顾问。...',
      description: '职责：深度设计主要角色背景、性格、成长轨迹',
      parameters: '[{"name":"指令","key":"command","type":"text","description":"","defaultValue":"","systemType":"","required":true},{"name":"设定","key":"worldSetting","type":"system","description":"","defaultValue":"","systemType":"worldSetting","required":true,"multiple":true},{"name":"大纲","key":"outline","type":"system","description":"","defaultValue":"","systemType":"outline","required":true},{"name":"分卷概述","key":"volume","type":"system","description":"","defaultValue":"","systemType":"volume","required":false}]'
    });
    console.log('提示词模板创建成功');
    
    console.log('基本数据导入完成');
    console.log('MySQL数据库同步完成');
  } catch (error) {
    console.error('MySQL数据库同步失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行同步
syncMySQLModels();
