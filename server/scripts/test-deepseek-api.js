/**
 * 测试DeepSeek API密钥
 */

const DeepSeekService = require('../services/deepseekService');

async function testDeepSeekAPI() {
  try {
    console.log('开始测试DeepSeek API...');
    
    // 使用您提供的API密钥
    const apiKey = 'sk-e39589e09b8440d3b33bf209b89c3e43';
    const deepseekService = new DeepSeekService(apiKey);
    
    console.log('API密钥:', apiKey);
    console.log('基础URL:', deepseekService.baseUrl);
    
    // 测试简单的聊天完成
    console.log('\n正在测试聊天完成API...');
    const response = await deepseekService.chatCompletion({
      messages: [{ role: 'user', content: 'Hello, please respond with "API test successful"' }],
      max_tokens: 20,
      temperature: 0.1
    });
    
    console.log('API响应:', response);
    
    if (response.choices && response.choices.length > 0) {
      console.log('生成的内容:', response.choices[0].message.content);
      console.log('✅ API测试成功！');
    } else {
      console.log('❌ API响应格式异常');
    }
    
    // 测试验证方法
    console.log('\n正在测试API密钥验证...');
    const isValid = await deepseekService.validateApiKey();
    console.log('验证结果:', isValid ? '✅ 有效' : '❌ 无效');
    
  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    
    // 提供一些常见问题的解决建议
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查API密钥是否正确');
    console.log('2. 确认DeepSeek账户有足够的余额');
    console.log('3. 检查网络连接是否正常');
    console.log('4. 确认API密钥有相应的权限');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testDeepSeekAPI()
    .then(() => {
      console.log('\n测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testDeepSeekAPI;
