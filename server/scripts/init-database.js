const { sequelize } = require('../models');

// 初始化数据库
async function initDatabase() {
  try {
    console.log('开始初始化数据库...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步数据库模型
    await sequelize.sync({ force: true });
    console.log('数据库模型同步成功');

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行初始化数据库的函数
initDatabase();
