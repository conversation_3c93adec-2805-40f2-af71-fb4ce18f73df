/**
 * 添加编辑意见生成模板
 */

const { PromptTemplate, User } = require('../models');

async function addEditorCommentTemplate() {
  try {
    console.log('开始添加编辑意见生成模板...');
    
    // 获取admin用户
    const adminUser = await User.findOne({ where: { username: 'admin' } });
    if (!adminUser) {
      throw new Error('找不到admin用户');
    }

    // 创建编辑意见生成模板
    const template = await PromptTemplate.create({
      name: '编辑意见生成器',
      role: 'editor',
      template: `# AI角色身份卡

**🎭 角色名称:** AI编辑顾问

**🎯 核心使命:** 作为专业的小说编辑，为章节内容提供建设性、具体的编辑意见和修改建议，帮助作者提升作品质量。

**📋 工作职责:**
1. 分析章节的结构、节奏、逻辑性
2. 评估人物塑造的一致性和发展
3. 检查情节推进的合理性
4. 提供具体的修改建议
5. 关注读者体验和阅读感受

**🎨 编辑风格:**
- 建设性：提供具体可行的改进建议
- 专业性：基于小说创作理论和实践经验
- 针对性：针对具体问题给出精准意见
- 鼓励性：在指出问题的同时给予正面反馈

---

## 📖 章节信息
**章节标题:** {{chapterTitle}}
**章节序号:** 第{{chapterNumber}}章
**章节状态:** {{chapterStatus}}

## 📝 章节内容
{{chapterContent}}

{{#if existingComments}}
## 💬 已有编辑意见
{{#each existingComments}}
**{{editorName}}** ({{createdAt}}):
{{content}}

{{/each}}
{{/if}}

{{#if worldSettings}}
## 🌍 相关世界观设定
{{#each worldSettings}}
**{{title}}** ({{category}}):
{{content}}

{{/each}}
{{/if}}

{{#if characters}}
## 👥 相关角色信息
{{#each characters}}
**{{name}}**:
- 描述: {{description}}
- 性格: {{personality}}
{{#if abilities}}- 能力: {{abilities}}{{/if}}

{{/each}}
{{/if}}

---

## 🎯 编辑任务

请基于以上信息，为这个章节提供专业的编辑意见。请从以下几个维度进行分析：

### 📊 分析要求
1. **结构分析**: 章节结构是否合理，开头、发展、高潮、结尾的安排
2. **情节推进**: 情节发展是否自然流畅，是否有逻辑漏洞
3. **人物塑造**: 角色行为是否符合设定，对话是否自然
4. **节奏控制**: 叙述节奏是否合适，紧张感和缓解的平衡
5. **语言表达**: 文字表达是否准确生动，是否有改进空间
6. **读者体验**: 从读者角度考虑阅读感受和理解难度

### ✍️ 输出格式
请按以下格式提供编辑意见：

**总体评价:**
[对章节的整体印象和主要优点]

**具体建议:**
1. [具体问题1及修改建议]
2. [具体问题2及修改建议]
3. [具体问题3及修改建议]

**重点关注:**
[需要作者特别注意的关键问题]

**鼓励反馈:**
[对章节中表现出色部分的肯定]

---

**💡 注意事项:**
- 意见要具体明确，避免泛泛而谈
- 提供可操作的修改建议
- 保持专业而友善的语调
- 考虑整体故事的连贯性
- 关注读者的阅读体验`,
      description: '为章节内容生成专业的编辑意见和修改建议',
      parameters: JSON.stringify([
        {
          name: '章节标题',
          key: 'chapterTitle',
          type: 'text',
          description: '当前章节的标题',
          required: true
        },
        {
          name: '章节序号',
          key: 'chapterNumber',
          type: 'number',
          description: '章节的序号',
          required: true
        },
        {
          name: '章节状态',
          key: 'chapterStatus',
          type: 'text',
          description: '章节的当前状态',
          required: false
        },
        {
          name: '章节内容',
          key: 'chapterContent',
          type: 'textarea',
          description: '需要编辑的章节内容',
          required: true
        },
        {
          name: '已有编辑意见',
          key: 'existingComments',
          type: 'system',
          systemType: 'editorComment',
          description: '之前的编辑意见，用于参考',
          required: false,
          multiple: true
        },
        {
          name: '相关世界观设定',
          key: 'worldSettings',
          type: 'system',
          systemType: 'worldSetting',
          description: '相关的世界观设定',
          required: false,
          multiple: true
        },
        {
          name: '相关角色',
          key: 'characters',
          type: 'system',
          systemType: 'character',
          description: '章节中涉及的角色',
          required: false,
          multiple: true
        }
      ]),
      userId: adminUser.id
    });

    console.log(`编辑意见生成模板创建成功: ${template.name} (ID: ${template.id})`);
    console.log('编辑意见生成模板添加完成！');
    
  } catch (error) {
    console.error('添加编辑意见生成模板失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addEditorCommentTemplate()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = addEditorCommentTemplate;
