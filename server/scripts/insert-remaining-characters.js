const { sequelize, Character, Project } = require('../models');

async function insertRemainingCharacters() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找"奕者游戏"项目
    const project = await Project.findOne({
      where: { name: '奕者游戏' }
    });

    if (!project) {
      console.error('未找到"奕者游戏"项目');
      return;
    }

    console.log(`找到项目: ${project.name}, ID: ${project.id}`);

    // 剩余角色数据
    const charactersData = [
      {
        name: '张昊',
        role: '"钢铁意志"团队队长，以防守见长',
        gender: '男',
        personality: '坚韧不拔，注重团队合作，是主角团的潜在盟友',
        background: '退役军人，在智域空间中以稳健著称',
        abilities_detail: '"意志壁垒"（明慧境中品）',
        characterArc: '在第三卷的心理博弈中发挥关键作用，与石磊惺惺相惜',
        isMainCharacter: false,
        importance: 65,
        projectId: project.id
      },
      {
        name: '叶秋',
        role: '"幻影舞者"团队的核心，擅长欺诈和伪装',
        gender: '女',
        personality: '神秘莫测，与苏沐形成既竞争又惺惺相惜的关系',
        background: '职业魔术师，在现实中就是欺骗的艺术家',
        abilities_detail: '"幻象编织"（明慧境中品）',
        characterArc: '与苏沐的对决贯穿多个卷次，最终可能成为重要盟友',
        isMainCharacter: false,
        importance: 68,
        projectId: project.id
      },
      {
        name: '王强',
        role: '第一卷"薛定谔的房间"游戏中的参与者',
        gender: '男',
        personality: '普通上班族，代表大多数被卷入游戏的普通人',
        background: '因工作压力过大而被智域空间选中',
        abilities_detail: '"危机直感"（启蒙境下品）',
        characterArc: '在第一场游戏中展现普通人的求生意志，成为主角团理解智域空间的重要参照',
        isMainCharacter: false,
        importance: 60,
        projectId: project.id
      },
      {
        name: '李雅',
        role: '第一卷"信任的阶梯"游戏参与者',
        gender: '女',
        personality: '谨慎多疑，但内心善良',
        background: '会计师，习惯于精确计算和风险评估',
        abilities_detail: '"数值感知"（启蒙境中品）',
        characterArc: '在信任游戏中的表现让主角团意识到合作的重要性',
        isMainCharacter: false,
        importance: 58,
        projectId: project.id
      },
      {
        name: '陈华',
        role: '第二卷"沉默的拍卖行"对手团队成员',
        gender: '男',
        personality: '贪婪狡诈，但智商不低',
        background: '小商人，精于算计',
        abilities_detail: '"价值评估"（启蒙境上品）',
        characterArc: '代表纯粹利益驱动的弈者类型，与主角团形成对比',
        isMainCharacter: false,
        importance: 55,
        projectId: project.id
      }
    ];

    let insertedCount = 0;
    let skippedCount = 0;

    // 逐个插入角色
    for (const characterData of charactersData) {
      // 检查是否已存在该角色
      const existingCharacter = await Character.findOne({
        where: { 
          name: characterData.name,
          projectId: project.id
        }
      });

      if (existingCharacter) {
        console.log(`角色 "${characterData.name}" 已存在，跳过插入`);
        skippedCount++;
        continue;
      }

      // 插入角色
      const character = await Character.create(characterData);
      console.log(`✅ 成功插入角色: ${character.name} (ID: ${character.id}, 重要程度: ${character.importance})`);
      insertedCount++;
    }

    console.log(`\n插入完成: 新增 ${insertedCount} 个角色，跳过 ${skippedCount} 个已存在角色`);

  } catch (error) {
    console.error('插入角色数据失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行脚本
insertRemainingCharacters();
