/**
 * 数据库切换脚本 - 从MySQL切换回SQLite
 *
 * 此脚本用于：
 * 1. 更新环境变量，将数据库类型切换为SQLite
 * 2. 确保SQLite数据库文件存在
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('../config/database');

// SQLite数据库文件路径
const sqliteDbPath = path.resolve(__dirname, '../database/xiaoshuozhushou.sqlite');

// 设置环境变量
function setEnvironmentVariable() {
  console.log('设置环境变量，切换到SQLite...');

  // 直接设置当前进程的环境变量
  process.env.DB_DIALECT = 'sqlite';
  console.log('环境变量已设置: DB_DIALECT=sqlite');

  // 创建或更新.env文件
  try {
    const envPath = path.resolve(__dirname, '../.env');
    let envContent = '';

    // 如果.env文件已存在，读取内容
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');

      // 替换或添加DB_DIALECT
      if (envContent.includes('DB_DIALECT=')) {
        envContent = envContent.replace(/DB_DIALECT=.*/g, 'DB_DIALECT=sqlite');
      } else {
        envContent += '\nDB_DIALECT=sqlite';
      }
    } else {
      envContent = 'DB_DIALECT=sqlite';
    }

    // 写入.env文件
    fs.writeFileSync(envPath, envContent);
    console.log('.env文件已更新');
  } catch (error) {
    console.error('.env文件更新失败:', error.message);
    console.log('请手动设置环境变量: DB_DIALECT=sqlite');
  }
}

// 检查SQLite数据库文件
function checkSQLiteDatabase() {
  console.log('检查SQLite数据库文件...');

  if (!fs.existsSync(sqliteDbPath)) {
    console.error(`SQLite数据库文件不存在: ${sqliteDbPath}`);
    console.log('请确保SQLite数据库文件存在，或运行初始化脚本创建新的数据库');
    return false;
  }

  console.log(`SQLite数据库文件存在: ${sqliteDbPath}`);
  return true;
}

// 主函数
async function switchToSQLite() {
  console.log('开始从MySQL切换到SQLite...');

  try {
    // 设置环境变量
    setEnvironmentVariable();

    // 检查SQLite数据库文件
    const dbExists = checkSQLiteDatabase();

    if (dbExists) {
      console.log('\n数据库已成功切换到SQLite!');
      console.log('请重启应用以使用新的数据库配置');
    } else {
      console.log('\n数据库切换未完成，请确保SQLite数据库文件存在');
    }
  } catch (error) {
    console.error('切换数据库过程中出错:', error);
  }
}

// 执行切换
switchToSQLite();
