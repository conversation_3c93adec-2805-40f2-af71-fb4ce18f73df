/**
 * SQLite到MySQL迁移脚本
 * 
 * 此脚本用于：
 * 1. 从SQLite数据库导出表结构并转换为MySQL兼容的CREATE TABLE语句
 * 2. 从SQLite数据库导出数据并转换为MySQL兼容的INSERT语句
 * 3. 生成完整的迁移SQL脚本
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const config = require('../config/database');

// 获取当前环境的配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// SQLite数据库文件路径
const sqliteDbPath = path.resolve(__dirname, '../database/xiaoshuozhushou.sqlite');

// 输出SQL文件路径
const outputSqlPath = path.resolve(__dirname, '../database/sqlite-to-mysql.sql');

// 将callback风格的sqlite3方法转换为Promise风格
function runQuery(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) return reject(err);
      resolve(rows);
    });
  });
}

// 获取所有表名
async function getAllTables(db) {
  const query = `SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '%_backup'`;
  return await runQuery(db, query);
}

// 获取表的创建语句
async function getTableSchema(db, tableName) {
  const query = `SELECT sql FROM sqlite_master WHERE type='table' AND name=?`;
  const result = await runQuery(db, query, [tableName]);
  return result[0]?.sql;
}

// 获取表的所有数据
async function getTableData(db, tableName) {
  const query = `SELECT * FROM "${tableName}"`;
  return await runQuery(db, query);
}

// 将SQLite的CREATE TABLE语句转换为MySQL兼容的语句
function convertCreateTableToMySQL(createTableSql, tableName) {
  if (!createTableSql) return null;

  // 替换自增语法
  let mysqlSql = createTableSql.replace(/INTEGER PRIMARY KEY AUTOINCREMENT/gi, 'INTEGER PRIMARY KEY AUTO_INCREMENT');
  
  // 替换布尔类型
  mysqlSql = mysqlSql.replace(/TINYINT\(1\)/gi, 'BOOLEAN');
  
  // 替换表名引号
  mysqlSql = mysqlSql.replace(/CREATE TABLE [`"]([^`"]+)[`"]/gi, 'CREATE TABLE IF NOT EXISTS `$1`');
  
  // 替换列名引号
  mysqlSql = mysqlSql.replace(/[`"]([^`"]+)[`"]/g, '`$1`');
  
  // 添加字符集和排序规则
  mysqlSql = mysqlSql.replace(/\);$/, ') ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;');
  
  // 替换DATETIME为TIMESTAMP
  mysqlSql = mysqlSql.replace(/DATETIME/gi, 'TIMESTAMP');
  
  return mysqlSql;
}

// 生成INSERT语句
function generateInsertStatements(tableName, data) {
  if (!data || data.length === 0) return '';
  
  const insertStatements = [];
  
  for (const row of data) {
    const columns = Object.keys(row).map(col => `\`${col}\``).join(', ');
    const values = Object.values(row).map(val => {
      if (val === null) return 'NULL';
      if (typeof val === 'string') {
        // 转义特殊字符
        return `'${val.replace(/'/g, "''").replace(/\\/g, "\\\\")}'`;
      }
      return val;
    }).join(', ');
    
    insertStatements.push(`INSERT INTO \`${tableName}\` (${columns}) VALUES (${values});`);
  }
  
  return insertStatements.join('\n');
}

// 主函数
async function migrateToMySQL() {
  console.log('开始从SQLite迁移到MySQL...');
  
  // 连接SQLite数据库
  const db = new sqlite3.Database(sqliteDbPath, sqlite3.OPEN_READONLY, (err) => {
    if (err) {
      console.error('无法连接到SQLite数据库:', err.message);
      process.exit(1);
    }
    console.log('已连接到SQLite数据库');
  });
  
  try {
    // 获取所有表
    const tables = await getAllTables(db);
    console.log(`找到 ${tables.length} 个表`);
    
    // 创建输出流
    const outputStream = fs.createWriteStream(outputSqlPath);
    
    // 写入文件头
    outputStream.write('-- SQLite到MySQL迁移脚本\n');
    outputStream.write(`-- 生成时间: ${new Date().toISOString()}\n\n`);
    outputStream.write('SET FOREIGN_KEY_CHECKS=0;\n\n');
    
    // 处理每个表
    for (const table of tables) {
      const tableName = table.name;
      console.log(`处理表: ${tableName}`);
      
      // 获取表结构
      const createTableSql = await getTableSchema(db, tableName);
      const mysqlCreateTableSql = convertCreateTableToMySQL(createTableSql, tableName);
      
      if (mysqlCreateTableSql) {
        outputStream.write(`-- 表结构: ${tableName}\n`);
        outputStream.write(`DROP TABLE IF EXISTS \`${tableName}\`;\n`);
        outputStream.write(mysqlCreateTableSql + '\n\n');
        
        // 获取表数据
        const tableData = await getTableData(db, tableName);
        
        if (tableData && tableData.length > 0) {
          outputStream.write(`-- 表数据: ${tableName}\n`);
          const insertStatements = generateInsertStatements(tableName, tableData);
          outputStream.write(insertStatements + '\n\n');
        }
      }
    }
    
    // 写入文件尾
    outputStream.write('SET FOREIGN_KEY_CHECKS=1;\n');
    outputStream.end();
    
    console.log(`迁移脚本已生成: ${outputSqlPath}`);
  } catch (error) {
    console.error('迁移过程中出错:', error);
  } finally {
    // 关闭数据库连接
    db.close((err) => {
      if (err) {
        console.error('关闭数据库连接失败:', err.message);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  }
}

// 执行迁移
migrateToMySQL();
