/**
 * 导入MySQL数据脚本
 * 
 * 此脚本用于：
 * 1. 读取迁移SQL文件
 * 2. 连接到MySQL数据库
 * 3. 执行SQL语句导入数据
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const config = require('../config/database');

// MySQL配置
const mysqlConfig = {
  host: process.env.DB_HOST || 'cd-cdb-8yjrt2ao.sql.tencentcdb.com',
  port: process.env.DB_PORT || 27134,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'Zjy@2025',
  database: process.env.DB_NAME || 'xiaoshuo2',
  multipleStatements: true // 允许执行多条SQL语句
};

// 迁移SQL文件路径
const migrationSqlPath = path.resolve(__dirname, '../database/sqlite-to-mysql.sql');

// 主函数
async function importMySQLData() {
  console.log('开始导入数据到MySQL...');
  
  // 读取SQL文件
  console.log(`读取SQL文件: ${migrationSqlPath}`);
  let sqlContent;
  try {
    sqlContent = fs.readFileSync(migrationSqlPath, 'utf8');
  } catch (error) {
    console.error(`读取SQL文件失败: ${error.message}`);
    process.exit(1);
  }
  
  // 连接MySQL
  console.log('连接到MySQL数据库...');
  let connection;
  try {
    connection = await mysql.createConnection(mysqlConfig);
    console.log('连接MySQL成功');
  } catch (error) {
    console.error(`连接MySQL失败: ${error.message}`);
    process.exit(1);
  }
  
  // 执行SQL
  console.log('开始执行SQL语句...');
  try {
    // 关闭外键检查
    await connection.query('SET FOREIGN_KEY_CHECKS=0;');
    
    // 将SQL文件拆分为单独的语句
    const statements = sqlContent.split(';').filter(stmt => stmt.trim() !== '');
    
    // 逐条执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim();
      if (stmt && !stmt.startsWith('--')) {
        try {
          await connection.query(stmt + ';');
          if (i % 10 === 0) {
            process.stdout.write('.');
          }
        } catch (error) {
          console.error(`\n执行SQL语句失败: ${error.message}`);
          console.error(`问题语句: ${stmt}`);
        }
      }
    }
    
    // 开启外键检查
    await connection.query('SET FOREIGN_KEY_CHECKS=1;');
    
    console.log('\n数据导入完成');
  } catch (error) {
    console.error(`执行SQL失败: ${error.message}`);
  } finally {
    // 关闭连接
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行导入
importMySQLData();
