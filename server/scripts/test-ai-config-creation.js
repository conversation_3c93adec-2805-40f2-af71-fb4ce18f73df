/**
 * 测试AI配置创建
 */

const axios = require('axios');

async function testAIConfigCreation() {
  try {
    console.log('开始测试AI配置创建...');
    
    // 首先登录获取token
    console.log('正在登录...');
    const loginResponse = await axios.post('http://localhost:5001/api/auth/login', {
      username: 'admin',
      password: 'Yinhai23'
    });
    
    const token = loginResponse.data.token;
    console.log('登录成功，获取到token');
    
    // 创建AI配置
    console.log('正在创建AI配置...');
    const createResponse = await axios.post('http://localhost:5001/api/ai-configs', {
      provider: 'deepseek',
      apiKey: '***********************************',
      baseUrl: '', // 空字符串，应该使用默认值
      model: 'deepseek-chat',
      isDefault: true,
      maxTokens: 4000,
      temperature: 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ AI配置创建成功:', createResponse.data);
    
    // 测试连接
    const configId = createResponse.data.id;
    console.log('正在测试连接...');
    const testResponse = await axios.post(`http://localhost:5001/api/ai-configs/${configId}/test`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 连接测试成功:', testResponse.data);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAIConfigCreation()
    .then(() => {
      console.log('\n测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = testAIConfigCreation;
