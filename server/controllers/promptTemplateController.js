const { PromptTemplate, Project, Character, WorldSetting, Outline, Chapter, Volume, VolumeChapterGroup, EditorComment, Clue } = require('../models');

// 获取所有提示词模板
exports.getAllPromptTemplates = async (req, res) => {
  try {
    const promptTemplates = await PromptTemplate.findAll({
      order: [['role', 'ASC'], ['name', 'ASC']]
    });

    res.json(promptTemplates);
  } catch (error) {
    console.error('获取提示词模板列表失败:', error);
    res.status(500).json({ message: '获取提示词模板列表失败', error: error.message });
  }
};

// 根据ID获取提示词模板
exports.getPromptTemplateById = async (req, res) => {
  try {
    const promptTemplate = await PromptTemplate.findByPk(req.params.id);

    if (!promptTemplate) {
      return res.status(404).json({ message: '提示词模板不存在' });
    }

    res.json(promptTemplate);
  } catch (error) {
    console.error('获取提示词模板失败:', error);
    res.status(500).json({ message: '获取提示词模板失败', error: error.message });
  }
};

// 创建提示词模板
exports.createPromptTemplate = async (req, res) => {
  try {
    const { name, role, template, description, parameters } = req.body;

    if (!name || !role || !template) {
      return res.status(400).json({ message: '模板名称、角色类型和模板内容不能为空' });
    }

    const promptTemplate = await PromptTemplate.create({
      name,
      role,
      template,
      description,
      parameters
    });

    res.status(201).json(promptTemplate);
  } catch (error) {
    console.error('创建提示词模板失败:', error);
    res.status(500).json({ message: '创建提示词模板失败', error: error.message });
  }
};

// 更新提示词模板
exports.updatePromptTemplate = async (req, res) => {
  try {
    const { name, role, template, description, parameters } = req.body;

    const promptTemplate = await PromptTemplate.findByPk(req.params.id);
    if (!promptTemplate) {
      return res.status(404).json({ message: '提示词模板不存在' });
    }

    await promptTemplate.update({
      name,
      role,
      template,
      description,
      parameters
    });

    res.json(promptTemplate);
  } catch (error) {
    console.error('更新提示词模板失败:', error);
    res.status(500).json({ message: '更新提示词模板失败', error: error.message });
  }
};

// 删除提示词模板
exports.deletePromptTemplate = async (req, res) => {
  try {
    const promptTemplate = await PromptTemplate.findByPk(req.params.id);
    if (!promptTemplate) {
      return res.status(404).json({ message: '提示词模板不存在' });
    }

    await promptTemplate.destroy();
    res.json({ message: '提示词模板删除成功' });
  } catch (error) {
    console.error('删除提示词模板失败:', error);
    res.status(500).json({ message: '删除提示词模板失败', error: error.message });
  }
};

// 生成提示词
exports.generatePrompt = async (req, res) => {
  try {
    const { projectId, templateId, characterIds, worldSettingIds, outlineIds, chapterId, customParams } = req.body;

    if (!templateId) {
      return res.status(400).json({ message: '提示词模板ID不能为空' });
    }

    // 获取提示词模板
    const promptTemplate = await PromptTemplate.findByPk(templateId);
    if (!promptTemplate) {
      return res.status(404).json({ message: '提示词模板不存在' });
    }

    // 准备替换模板中的变量的数据
    let templateData = {};

    // 如果有项目ID，获取项目信息
    if (projectId) {
      const project = await Project.findByPk(projectId);
      if (!project) {
        return res.status(404).json({ message: '项目不存在' });
      }
      templateData.project = project.toJSON();
    }

    // 如果有角色ID，获取角色信息
    if (characterIds && characterIds.length > 0) {
      const characters = await Character.findAll({
        where: { id: characterIds }
      });
      templateData.characters = characters.map(char => char.toJSON());
    }

    // 如果有世界观设定ID，获取世界观设定信息
    if (worldSettingIds && worldSettingIds.length > 0) {
      const worldSettings = await WorldSetting.findAll({
        where: { id: worldSettingIds }
      });
      templateData.worldSettings = worldSettings.map(ws => ws.toJSON());
    }

    // 如果有大纲ID，获取大纲信息
    if (outlineIds && outlineIds.length > 0) {
      const outlines = await Outline.findAll({
        where: { id: outlineIds }
      });
      templateData.outlines = outlines.map(outline => outline.toJSON());
    }

    // 如果有章节ID，获取章节信息
    if (chapterId) {
      const chapter = await Chapter.findByPk(chapterId);
      if (!chapter) {
        return res.status(404).json({ message: '章节不存在' });
      }
      templateData.chapter = chapter.toJSON();
    }

    // 合并自定义参数
    if (customParams) {
      templateData = { ...templateData, ...customParams };
    }

    // 替换模板中的变量
    let generatedPrompt = promptTemplate.template;

    // 简单的模板变量替换逻辑
    // 实际项目中可能需要更复杂的模板引擎
    for (const key in templateData) {
      const value = templateData[key];
      if (typeof value === 'object') {
        for (const subKey in value) {
          const subValue = value[subKey];
          const placeholder = new RegExp(`\\{\\{${key}\\.${subKey}\\}\\}`, 'g');
          generatedPrompt = generatedPrompt.replace(placeholder, subValue);
        }
      } else {
        const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(placeholder, value);
      }
    }

    res.json({
      prompt: generatedPrompt,
      template: promptTemplate.name,
      role: promptTemplate.role
    });
  } catch (error) {
    console.error('生成提示词失败:', error);
    res.status(500).json({ message: '生成提示词失败', error: error.message });
  }
};

// 根据ID生成提示词
exports.generatePromptById = async (req, res) => {
  try {
    const templateId = req.params.id;
    const { params = {}, projectId, characterIds, worldSettingIds, outlineIds, chapterId, systemParamTypes = {} } = req.body;

    if (!templateId) {
      return res.status(400).json({ message: '提示词模板ID不能为空' });
    }

    // 获取提示词模板
    const promptTemplate = await PromptTemplate.findByPk(templateId);
    if (!promptTemplate) {
      return res.status(404).json({ message: '提示词模板不存在' });
    }

    // 打印调试信息
    console.log('生成提示词请求参数:', {
      templateId,
      projectId,
      params,
      systemParamTypes
    });

    // 准备替换模板中的变量的数据
    let templateData = {};

    // 如果有项目ID，获取项目信息
    if (projectId) {
      const project = await Project.findByPk(projectId);
      if (!project) {
        return res.status(404).json({ message: '项目不存在' });
      }
      const projectData = project.toJSON();
      // 移除不需要的字段
      delete projectData.id;
      delete projectData.projectId;
      delete projectData.createdAt;
      delete projectData.updatedAt;
      templateData.project = projectData;
    }

    // 如果有角色ID，获取角色信息
    if (characterIds && characterIds.length > 0) {
      const characters = await Character.findAll({
        where: { id: characterIds }
      });
      templateData.characters = characters.map(char => {
        const charData = char.toJSON();
        // 移除不需要的字段
        delete charData.id;
        delete charData.projectId;
        delete charData.createdAt;
        delete charData.updatedAt;
        return charData;
      });
    }

    // 如果有世界观设定ID，获取世界观设定信息
    if (worldSettingIds && worldSettingIds.length > 0) {
      const worldSettings = await WorldSetting.findAll({
        where: { id: worldSettingIds }
      });
      templateData.worldSettings = worldSettings.map(ws => {
        const wsData = ws.toJSON();
        // 移除不需要的字段
        delete wsData.id;
        delete wsData.projectId;
        delete wsData.createdAt;
        delete wsData.updatedAt;
        return wsData;
      });
    }

    // 如果有大纲ID，获取大纲信息
    if (outlineIds && outlineIds.length > 0) {
      const outlines = await Outline.findAll({
        where: { id: outlineIds }
      });
      templateData.outlines = outlines.map(outline => {
        const outlineData = outline.toJSON();
        // 移除不需要的字段
        delete outlineData.id;
        delete outlineData.projectId;
        delete outlineData.createdAt;
        delete outlineData.updatedAt;
        return outlineData;
      });
    }

    // 如果有章节ID，获取章节信息和编辑意见
    if (chapterId) {
      const chapter = await Chapter.findByPk(chapterId);
      if (!chapter) {
        return res.status(404).json({ message: '章节不存在' });
      }
      const chapterData = chapter.toJSON();

      // 获取章节的编辑意见
      const editorComments = await EditorComment.findAll({
        where: { chapterId },
        order: [['createdAt', 'DESC']]
      });

      // 将编辑意见添加到章节数据中
      chapterData.editorComments = editorComments.map(comment => {
        const commentData = comment.toJSON();
        // 移除不需要的字段
        delete commentData.id;
        delete commentData.chapterId;
        delete commentData.updatedAt;
        return commentData;
      });

      // 移除不需要的字段
      delete chapterData.id;
      delete chapterData.projectId;
      delete chapterData.createdAt;
      delete chapterData.updatedAt;
      templateData.chapter = chapterData;
    }

    // 处理系统参数和自定义参数
    if (params && Object.keys(params).length > 0) {
      // 获取模板的参数配置
      let templateParameters = promptTemplate.parameters || [];
      // 确保 templateParameters 是数组
      if (typeof templateParameters === 'string') {
        try {
          templateParameters = JSON.parse(templateParameters);
        } catch (error) {
          console.error('解析模板参数失败:', error);
          templateParameters = [];
        }
      }
      if (!Array.isArray(templateParameters)) {
        templateParameters = [];
      }

      // 遍历每个参数
      for (const param of templateParameters) {
        // 如果参数值未定义且不是必填项，使用空字符串
        if (params[param.key] === undefined || params[param.key] === null || params[param.key] === '') {
          if (param.required === false) {
            templateData[param.key] = '';
          }
          continue;
        }

        if (param.type === 'system' && param.systemType) {
          const paramValue = params[param.key];
          const isMultiple = param.multiple === true;

          // 根据系统参数类型获取对应的数据
          switch (param.systemType) {
            case 'editorComment':
              if (isMultiple && Array.isArray(paramValue)) {
                // 处理多选情况
                const editorComments = await EditorComment.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = editorComments.map(item => {
                  const ecData = item.toJSON();
                  // 移除不需要的字段
                  delete ecData.id;
                  delete ecData.chapterId;
                  return ecData;
                });
              } else {
                // 处理单选情况
                const editorComment = await EditorComment.findByPk(paramValue);
                if (editorComment) {
                  const ecData = editorComment.toJSON();
                  // 移除不需要的字段
                  delete ecData.id;
                  delete ecData.chapterId;
                  templateData[param.key] = ecData;
                }
              }
              break;
            case 'worldSetting':
              if (isMultiple && Array.isArray(paramValue)) {
                // 处理多选情况
                const worldSettings = await WorldSetting.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = worldSettings.map(item => {
                  const wsData = item.toJSON();
                  // 移除不需要的字段
                  delete wsData.id;
                  delete wsData.projectId;
                  delete wsData.createdAt;
                  delete wsData.updatedAt;
                  return wsData;
                });
              } else {
                // 处理单选情况
                const worldSetting = await WorldSetting.findByPk(paramValue);
                if (worldSetting) {
                  const wsData = worldSetting.toJSON();
                  // 移除不需要的字段
                  delete wsData.id;
                  delete wsData.projectId;
                  delete wsData.createdAt;
                  delete wsData.updatedAt;
                  templateData[param.key] = wsData;
                }
              }
              break;
            case 'character':
              if (isMultiple && Array.isArray(paramValue)) {
                const characters = await Character.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = characters.map(item => {
                  const charData = item.toJSON();
                  // 移除不需要的字段
                  delete charData.id;
                  delete charData.projectId;
                  delete charData.createdAt;
                  delete charData.updatedAt;
                  return charData;
                });
              } else {
                const character = await Character.findByPk(paramValue);
                if (character) {
                  const charData = character.toJSON();
                  // 移除不需要的字段
                  delete charData.id;
                  delete charData.projectId;
                  delete charData.createdAt;
                  delete charData.updatedAt;
                  templateData[param.key] = charData;
                }
              }
              break;
            case 'outline':
              if (isMultiple && Array.isArray(paramValue)) {
                const outlines = await Outline.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = outlines.map(item => {
                  const outlineData = item.toJSON();
                  // 移除不需要的字段
                  delete outlineData.id;
                  delete outlineData.projectId;
                  delete outlineData.createdAt;
                  delete outlineData.updatedAt;
                  return outlineData;
                });
              } else {
                const outline = await Outline.findByPk(paramValue);
                if (outline) {
                  const outlineData = outline.toJSON();
                  // 移除不需要的字段
                  delete outlineData.id;
                  delete outlineData.projectId;
                  delete outlineData.createdAt;
                  delete outlineData.updatedAt;
                  templateData[param.key] = outlineData;
                }
              }
              break;
            case 'chapter':
              if (isMultiple && Array.isArray(paramValue)) {
                const chapters = await Chapter.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = chapters.map(item => {
                  const chapterData = item.toJSON();
                  // 移除不需要的字段
                  delete chapterData.id;
                  delete chapterData.projectId;
                  delete chapterData.createdAt;
                  delete chapterData.updatedAt;
                  return chapterData;
                });
              } else {
                const chapter = await Chapter.findByPk(paramValue);
                if (chapter) {
                  const chapterData = chapter.toJSON();
                  // 移除不需要的字段
                  delete chapterData.id;
                  delete chapterData.projectId;
                  delete chapterData.createdAt;
                  delete chapterData.updatedAt;
                  templateData[param.key] = chapterData;
                }
              }
              break;
            case 'volume':
              if (isMultiple && Array.isArray(paramValue)) {
                const volumes = await Volume.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = volumes.map(item => {
                  const volumeData = item.toJSON();
                  // 移除不需要的字段
                  delete volumeData.id;
                  delete volumeData.projectId;
                  delete volumeData.createdAt;
                  delete volumeData.updatedAt;
                  return volumeData;
                });
              } else {
                const volume = await Volume.findByPk(paramValue);
                if (volume) {
                  const volumeData = volume.toJSON();
                  // 移除不需要的字段
                  delete volumeData.id;
                  delete volumeData.projectId;
                  delete volumeData.createdAt;
                  delete volumeData.updatedAt;
                  templateData[param.key] = volumeData;
                }
              }
              break;
            case 'chapterGroup':
              if (isMultiple && Array.isArray(paramValue)) {
                const chapterGroups = await VolumeChapterGroup.findAll({
                  where: { id: paramValue }
                });
                templateData[param.key] = chapterGroups.map(item => {
                  const groupData = item.toJSON();
                  // 移除不需要的字段
                  delete groupData.id;
                  delete groupData.volumeId;
                  delete groupData.createdAt;
                  delete groupData.updatedAt;
                  return groupData;
                });
              } else {
                const chapterGroup = await VolumeChapterGroup.findByPk(paramValue);
                if (chapterGroup) {
                  const groupData = chapterGroup.toJSON();
                  // 移除不需要的字段
                  delete groupData.id;
                  delete groupData.volumeId;
                  delete groupData.createdAt;
                  delete groupData.updatedAt;
                  templateData[param.key] = groupData;
                }
              }
              break;
            case 'clue':
              if (isMultiple && Array.isArray(paramValue)) {
                const clues = await Clue.findAll({
                  where: { id: paramValue },
                  include: [
                    {
                      model: Chapter,
                      as: 'firstAppearChapter',
                      attributes: ['title']
                    }
                  ]
                });
                templateData[param.key] = clues.map(item => {
                  const clueData = item.toJSON();
                  // 移除不需要的字段
                  delete clueData.id;
                  delete clueData.projectId;
                  delete clueData.firstAppearChapterId;
                  delete clueData.createdAt;
                  delete clueData.updatedAt;
                  return clueData;
                });
              } else {
                const clue = await Clue.findByPk(paramValue, {
                  include: [
                    {
                      model: Chapter,
                      as: 'firstAppearChapter',
                      attributes: ['title']
                    }
                  ]
                });
                if (clue) {
                  const clueData = clue.toJSON();
                  // 移除不需要的字段
                  delete clueData.id;
                  delete clueData.projectId;
                  delete clueData.firstAppearChapterId;
                  delete clueData.createdAt;
                  delete clueData.updatedAt;
                  templateData[param.key] = clueData;
                }
              }
              break;
          }
        } else {
          // 对于非系统参数，直接添加到templateData
          templateData[param.key] = params[param.key];
        }
      }

      // 处理未在模板参数中定义的参数
      for (const key in params) {
        if (Array.isArray(templateParameters) && !templateParameters.some(p => p.key === key)) {
          templateData[key] = params[key];
        }
      }
    }

    // 替换模板中的变量
    let generatedPrompt = promptTemplate.template;

    // 增强的模板变量替换逻辑
    for (const key in templateData) {
      const value = templateData[key];

      // 处理数组类型的参数（多选系统参数）
      if (Array.isArray(value)) {
        // 替换整个数组的占位符
        const arrayPlaceholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(arrayPlaceholder, JSON.stringify(value, null, 2));

        // 处理数组索引访问，如 {{paramName[0].property}}
        const arrayIndexRegex = new RegExp(`\\{\\{${key}\\[(\\d+)\\]\\.(\\w+)\\}\\}`, 'g');
        let match;

        // 创建一个临时字符串用于替换
        let tempPrompt = generatedPrompt;

        // 查找所有数组索引访问模式
        while ((match = arrayIndexRegex.exec(generatedPrompt)) !== null) {
          const fullMatch = match[0];
          const index = parseInt(match[1]);
          const property = match[2];

          if (value[index] && value[index][property] !== undefined) {
            // 替换为对应的值
            tempPrompt = tempPrompt.replace(fullMatch, String(value[index][property]));
          }
        }

        generatedPrompt = tempPrompt;

        // 处理数组索引的嵌套属性访问，如 {{paramName[0].property.nestedProperty}}
        const arrayNestedIndexRegex = new RegExp(`\\{\\{${key}\\[(\\d+)\\]\\.(\\w+)\\.(\\w+)\\}\\}`, 'g');

        tempPrompt = generatedPrompt;
        while ((match = arrayNestedIndexRegex.exec(generatedPrompt)) !== null) {
          const fullMatch = match[0];
          const index = parseInt(match[1]);
          const property = match[2];
          const nestedProperty = match[3];

          if (value[index] && value[index][property] && value[index][property][nestedProperty] !== undefined) {
            // 替换为对应的值
            tempPrompt = tempPrompt.replace(fullMatch, String(value[index][property][nestedProperty]));
          }
        }

        generatedPrompt = tempPrompt;
      }
      // 处理对象类型的参数
      else if (typeof value === 'object' && value !== null) {
        // 处理对象类型的参数，支持嵌套属性
        for (const subKey in value) {
          const subValue = value[subKey];
          if (subValue !== null && subValue !== undefined) {
            // 替换 {{key.subKey}} 格式的占位符
            const placeholder = new RegExp(`\\{\\{${key}\\.${subKey}\\}\\}`, 'g');
            generatedPrompt = generatedPrompt.replace(placeholder, String(subValue));

            // 如果子值也是对象，处理更深层次的嵌套
            if (typeof subValue === 'object' && subValue !== null) {
              for (const nestedKey in subValue) {
                const nestedValue = subValue[nestedKey];
                if (nestedValue !== null && nestedValue !== undefined) {
                  // 替换 {{key.subKey.nestedKey}} 格式的占位符
                  const nestedPlaceholder = new RegExp(`\\{\\{${key}\\.${subKey}\\.${nestedKey}\\}\\}`, 'g');
                  generatedPrompt = generatedPrompt.replace(nestedPlaceholder, String(nestedValue));
                }
              }
            }
          }
        }

        // 同时支持直接使用对象名称作为占位符，将整个对象转为JSON字符串
        const objectPlaceholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(objectPlaceholder, JSON.stringify(value, null, 2));
      } else if (value !== null && value !== undefined) {
        // 处理基本类型的参数
        const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(placeholder, String(value));
      }
    }

    // 添加调试日志
    console.log('模板数据:', JSON.stringify(templateData, null, 2));
    console.log('生成的提示词:', generatedPrompt.substring(0, 200) + '...');

    res.json({
      prompt: generatedPrompt,
      template: promptTemplate.name,
      role: promptTemplate.role,
      parameters: promptTemplate.parameters // 返回参数配置信息
    });
  } catch (error) {
    console.error('生成提示词失败:', error);
    res.status(500).json({ message: '生成提示词失败', error: error.message });
  }
};
