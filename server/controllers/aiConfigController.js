const { AIConfig } = require('../models');
const DeepSeekService = require('../services/deepseekService');
const KimiService = require('../services/kimiService');

// 获取用户的AI配置列表
exports.getUserAIConfigs = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const configs = await AIConfig.findAll({
      where: { userId },
      order: [['isDefault', 'DESC'], ['createdAt', 'ASC']]
    });

    // 隐藏API密钥的敏感信息
    const safeConfigs = configs.map(config => {
      const configData = config.toJSON();
      configData.apiKey = configData.apiKey ? '***已配置***' : '';
      return configData;
    });

    res.json(safeConfigs);
  } catch (error) {
    console.error('获取AI配置失败:', error);
    res.status(500).json({ message: '获取AI配置失败', error: error.message });
  }
};

// 创建AI配置
exports.createAIConfig = async (req, res) => {
  try {
    const userId = req.user.id;
    const { 
      provider, 
      apiKey, 
      baseUrl, 
      model, 
      isDefault = false,
      maxTokens = 4000,
      temperature = 0.7,
      config = {}
    } = req.body;

    // 验证必填字段
    if (!provider || !apiKey || !model) {
      return res.status(400).json({ message: '提供商、API密钥和模型名称不能为空' });
    }

    // 如果设置为默认配置，先将其他配置的默认状态取消
    if (isDefault) {
      await AIConfig.update(
        { isDefault: false },
        { where: { userId, provider } }
      );
    }

    // 验证API密钥
    if (provider === 'deepseek') {
      try {
        // 如果baseUrl为空，则使用默认值
        const effectiveBaseUrl = baseUrl && baseUrl.trim() ? baseUrl : undefined;
        const deepseekService = new DeepSeekService(apiKey, effectiveBaseUrl);
        const isValid = await deepseekService.validateApiKey();
        if (!isValid) {
          return res.status(400).json({ message: 'API密钥验证失败，请检查密钥是否正确' });
        }
      } catch (error) {
        return res.status(400).json({ message: `API密钥验证失败: ${error.message}` });
      }
    } else if (provider === 'kimi') {
      try {
        // 如果baseUrl为空，则使用默认值
        const effectiveBaseUrl = baseUrl && baseUrl.trim() ? baseUrl : undefined;
        const kimiService = new KimiService(apiKey, effectiveBaseUrl);
        const isValid = await kimiService.validateApiKey();
        if (!isValid) {
          return res.status(400).json({ message: 'Kimi API密钥验证失败，请检查密钥是否正确' });
        }
      } catch (error) {
        return res.status(400).json({ message: `Kimi API密钥验证失败: ${error.message}` });
      }
    }

    const aiConfig = await AIConfig.create({
      userId,
      provider,
      apiKey,
      baseUrl,
      model,
      isDefault,
      maxTokens,
      temperature,
      config
    });

    // 返回时隐藏API密钥
    const responseData = aiConfig.toJSON();
    responseData.apiKey = '***已配置***';

    res.status(201).json(responseData);
  } catch (error) {
    console.error('创建AI配置失败:', error);
    if (error.name === 'SequelizeUniqueConstraintError') {
      res.status(400).json({ message: '该提供商和模型的配置已存在' });
    } else {
      res.status(500).json({ message: '创建AI配置失败', error: error.message });
    }
  }
};

// 更新AI配置
exports.updateAIConfig = async (req, res) => {
  try {
    const userId = req.user.id;
    const configId = req.params.id;
    const { 
      provider, 
      apiKey, 
      baseUrl, 
      model, 
      isDefault,
      isActive,
      maxTokens,
      temperature,
      config
    } = req.body;

    const aiConfig = await AIConfig.findOne({
      where: { id: configId, userId }
    });

    if (!aiConfig) {
      return res.status(404).json({ message: 'AI配置不存在' });
    }

    // 如果设置为默认配置，先将其他配置的默认状态取消
    if (isDefault && !aiConfig.isDefault) {
      await AIConfig.update(
        { isDefault: false },
        { where: { userId, provider: aiConfig.provider } }
      );
    }

    // 如果更新了API密钥，需要验证
    if (apiKey && apiKey !== aiConfig.apiKey) {
      const currentProvider = provider || aiConfig.provider;
      if (currentProvider === 'deepseek') {
        try {
          // 如果baseUrl为空，则使用默认值
          const effectiveBaseUrl = (baseUrl !== undefined ? baseUrl : aiConfig.baseUrl);
          const finalBaseUrl = effectiveBaseUrl && effectiveBaseUrl.trim() ? effectiveBaseUrl : undefined;
          const deepseekService = new DeepSeekService(apiKey, finalBaseUrl);
          const isValid = await deepseekService.validateApiKey();
          if (!isValid) {
            return res.status(400).json({ message: 'API密钥验证失败，请检查密钥是否正确' });
          }
        } catch (error) {
          return res.status(400).json({ message: `API密钥验证失败: ${error.message}` });
        }
      } else if (currentProvider === 'kimi') {
        try {
          // 如果baseUrl为空，则使用默认值
          const effectiveBaseUrl = (baseUrl !== undefined ? baseUrl : aiConfig.baseUrl);
          const finalBaseUrl = effectiveBaseUrl && effectiveBaseUrl.trim() ? effectiveBaseUrl : undefined;
          const kimiService = new KimiService(apiKey, finalBaseUrl);
          const isValid = await kimiService.validateApiKey();
          if (!isValid) {
            return res.status(400).json({ message: 'Kimi API密钥验证失败，请检查密钥是否正确' });
          }
        } catch (error) {
          return res.status(400).json({ message: `Kimi API密钥验证失败: ${error.message}` });
        }
      }
    }

    await aiConfig.update({
      provider: provider || aiConfig.provider,
      apiKey: apiKey || aiConfig.apiKey,
      baseUrl: baseUrl !== undefined ? baseUrl : aiConfig.baseUrl,
      model: model || aiConfig.model,
      isDefault: isDefault !== undefined ? isDefault : aiConfig.isDefault,
      isActive: isActive !== undefined ? isActive : aiConfig.isActive,
      maxTokens: maxTokens !== undefined ? maxTokens : aiConfig.maxTokens,
      temperature: temperature !== undefined ? temperature : aiConfig.temperature,
      config: config !== undefined ? config : aiConfig.config
    });

    // 返回时隐藏API密钥
    const responseData = aiConfig.toJSON();
    responseData.apiKey = '***已配置***';

    res.json(responseData);
  } catch (error) {
    console.error('更新AI配置失败:', error);
    res.status(500).json({ message: '更新AI配置失败', error: error.message });
  }
};

// 删除AI配置
exports.deleteAIConfig = async (req, res) => {
  try {
    const userId = req.user.id;
    const configId = req.params.id;

    const aiConfig = await AIConfig.findOne({
      where: { id: configId, userId }
    });

    if (!aiConfig) {
      return res.status(404).json({ message: 'AI配置不存在' });
    }

    await aiConfig.destroy();

    res.json({ message: 'AI配置删除成功' });
  } catch (error) {
    console.error('删除AI配置失败:', error);
    res.status(500).json({ message: '删除AI配置失败', error: error.message });
  }
};

// 设置默认AI配置
exports.setDefaultAIConfig = async (req, res) => {
  try {
    const userId = req.user.id;
    const configId = req.params.id;

    const aiConfig = await AIConfig.findOne({
      where: { id: configId, userId }
    });

    if (!aiConfig) {
      return res.status(404).json({ message: 'AI配置不存在' });
    }

    // 将同一提供商的其他配置设为非默认
    await AIConfig.update(
      { isDefault: false },
      { where: { userId, provider: aiConfig.provider } }
    );

    // 设置当前配置为默认
    await aiConfig.update({ isDefault: true });

    res.json({ message: '默认配置设置成功' });
  } catch (error) {
    console.error('设置默认配置失败:', error);
    res.status(500).json({ message: '设置默认配置失败', error: error.message });
  }
};

// 测试AI配置连接
exports.testAIConfig = async (req, res) => {
  try {
    const userId = req.user.id;
    const configId = req.params.id;

    const aiConfig = await AIConfig.findOne({
      where: { id: configId, userId }
    });

    if (!aiConfig) {
      return res.status(404).json({ message: 'AI配置不存在' });
    }

    if (!aiConfig.isActive) {
      return res.status(400).json({ message: '配置已禁用，无法测试' });
    }

    // 支持DeepSeek和Kimi
    if (aiConfig.provider === 'deepseek') {
      // 如果baseUrl为空，则使用默认值
      const effectiveBaseUrl = aiConfig.baseUrl && aiConfig.baseUrl.trim() ? aiConfig.baseUrl : undefined;
      const deepseekService = new DeepSeekService(aiConfig.apiKey, effectiveBaseUrl);

      try {
        const testResponse = await deepseekService.generateText('请回复"连接测试成功"', {
          max_tokens: 20,
          temperature: 0.1
        });

        res.json({
          success: true,
          message: '连接测试成功',
          response: testResponse
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          message: `连接测试失败: ${error.message}`
        });
      }
    } else if (aiConfig.provider === 'kimi') {
      // 如果baseUrl为空，则使用默认值
      const effectiveBaseUrl = aiConfig.baseUrl && aiConfig.baseUrl.trim() ? aiConfig.baseUrl : undefined;
      const kimiService = new KimiService(aiConfig.apiKey, effectiveBaseUrl);

      try {
        const testResponse = await kimiService.generateText('请回复"连接测试成功"', {
          max_tokens: 20,
          temperature: 0.1
        });

        res.json({
          success: true,
          message: 'Kimi连接测试成功',
          response: testResponse
        });
      } catch (error) {
        res.status(400).json({
          success: false,
          message: `Kimi连接测试失败: ${error.message}`
        });
      }
    } else {
      res.status(400).json({ message: `暂不支持 ${aiConfig.provider} 提供商的测试` });
    }
  } catch (error) {
    console.error('测试AI配置失败:', error);
    res.status(500).json({ message: '测试AI配置失败', error: error.message });
  }
};
