const { GeneratedContent, Project, Chapter, PromptTemplate } = require('../models');

// 根据项目ID获取生成内容
exports.getGeneratedContentsByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    
    // 检查项目是否存在
    const project = await Project.findByPk(projectId);
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    const generatedContents = await GeneratedContent.findAll({
      where: { projectId },
      order: [['createdAt', 'DESC']],
      include: [
        { model: Chapter },
        { model: PromptTemplate }
      ]
    });
    
    res.json(generatedContents);
  } catch (error) {
    console.error('获取生成内容列表失败:', error);
    res.status(500).json({ message: '获取生成内容列表失败', error: error.message });
  }
};

// 根据ID获取生成内容
exports.getGeneratedContentById = async (req, res) => {
  try {
    const generatedContent = await GeneratedContent.findByPk(req.params.id, {
      include: [
        { model: Chapter },
        { model: PromptTemplate }
      ]
    });
    
    if (!generatedContent) {
      return res.status(404).json({ message: '生成内容不存在' });
    }
    
    res.json(generatedContent);
  } catch (error) {
    console.error('获取生成内容失败:', error);
    res.status(500).json({ message: '获取生成内容失败', error: error.message });
  }
};

// 创建生成内容
exports.createGeneratedContent = async (req, res) => {
  try {
    const { projectId, chapterId, promptId, prompt, content, type } = req.body;
    
    if (!projectId || !content || !type) {
      return res.status(400).json({ message: '项目ID、内容和类型不能为空' });
    }
    
    // 检查项目是否存在
    const project = await Project.findByPk(projectId);
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    // 如果有章节ID，检查章节是否存在
    if (chapterId) {
      const chapter = await Chapter.findByPk(chapterId);
      if (!chapter) {
        return res.status(404).json({ message: '章节不存在' });
      }
    }
    
    // 如果有提示词模板ID，检查提示词模板是否存在
    if (promptId) {
      const promptTemplate = await PromptTemplate.findByPk(promptId);
      if (!promptTemplate) {
        return res.status(404).json({ message: '提示词模板不存在' });
      }
    }
    
    const generatedContent = await GeneratedContent.create({
      projectId,
      chapterId,
      promptId,
      prompt,
      content,
      type
    });
    
    res.status(201).json(generatedContent);
  } catch (error) {
    console.error('创建生成内容失败:', error);
    res.status(500).json({ message: '创建生成内容失败', error: error.message });
  }
};

// 更新生成内容
exports.updateGeneratedContent = async (req, res) => {
  try {
    const { chapterId, promptId, prompt, content, type } = req.body;
    
    const generatedContent = await GeneratedContent.findByPk(req.params.id);
    if (!generatedContent) {
      return res.status(404).json({ message: '生成内容不存在' });
    }
    
    // 如果有章节ID，检查章节是否存在
    if (chapterId && chapterId !== generatedContent.chapterId) {
      const chapter = await Chapter.findByPk(chapterId);
      if (!chapter) {
        return res.status(404).json({ message: '章节不存在' });
      }
    }
    
    // 如果有提示词模板ID，检查提示词模板是否存在
    if (promptId && promptId !== generatedContent.promptId) {
      const promptTemplate = await PromptTemplate.findByPk(promptId);
      if (!promptTemplate) {
        return res.status(404).json({ message: '提示词模板不存在' });
      }
    }
    
    await generatedContent.update({
      chapterId,
      promptId,
      prompt,
      content,
      type
    });
    
    res.json(generatedContent);
  } catch (error) {
    console.error('更新生成内容失败:', error);
    res.status(500).json({ message: '更新生成内容失败', error: error.message });
  }
};

// 删除生成内容
exports.deleteGeneratedContent = async (req, res) => {
  try {
    const generatedContent = await GeneratedContent.findByPk(req.params.id);
    if (!generatedContent) {
      return res.status(404).json({ message: '生成内容不存在' });
    }
    
    await generatedContent.destroy();
    res.json({ message: '生成内容删除成功' });
  } catch (error) {
    console.error('删除生成内容失败:', error);
    res.status(500).json({ message: '删除生成内容失败', error: error.message });
  }
};
