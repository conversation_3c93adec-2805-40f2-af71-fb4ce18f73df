const { Character, Project } = require('../models');

// 根据项目ID获取角色
exports.getCharactersByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 检查项目是否存在且用户有权访问
    const projectWhereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };
    const project = await Project.findOne({ where: projectWhereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    // 构建角色查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的数据
    const characterWhereCondition = userRole === 'admin' ? { projectId } : { projectId, userId };

    const characters = await Character.findAll({
      where: characterWhereCondition,
      order: [['createdAt', 'ASC']]
    });

    res.json(characters);
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({ message: '获取角色列表失败', error: error.message });
  }
};

// 根据ID获取角色
exports.getCharacterById = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const character = await Character.findOne({ where: whereCondition });
    if (!character) {
      return res.status(404).json({ message: '角色不存在或无权访问' });
    }

    res.json(character);
  } catch (error) {
    console.error('获取角色失败:', error);
    res.status(500).json({ message: '获取角色失败', error: error.message });
  }
};

// 创建角色
exports.createCharacter = async (req, res) => {
  try {
    const { projectId, name, role, gender, description, background, personality, abilities, abilities_detail, characterArc, relationships, isMainCharacter, importance } = req.body;
    const userId = req.user.id; // 获取当前登录用户ID
    const userRole = req.user.role;

    if (!projectId || !name) {
      return res.status(400).json({ message: '项目ID和角色名称不能为空' });
    }

    // 检查项目是否存在且用户有权访问
    const projectWhereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };
    const project = await Project.findOne({ where: projectWhereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    const character = await Character.create({
      projectId,
      name,
      role,
      gender,
      description,
      background,
      personality,
      abilities,
      abilities_detail,
      characterArc,
      relationships,
      isMainCharacter,
      importance,
      userId // 关联当前用户
    });

    res.status(201).json(character);
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({ message: '创建角色失败', error: error.message });
  }
};

// 更新角色
exports.updateCharacter = async (req, res) => {
  try {
    console.log('🔧 更新角色请求 - ID:', req.params.id);
    console.log('🔧 请求体数据:', JSON.stringify(req.body, null, 2));

    const { name, role, gender, description, background, personality, abilities, abilities_detail, characterArc, relationships, isMainCharacter, importance } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    console.log('🔧 提取的字段:');
    console.log('  - gender:', gender);
    console.log('  - characterArc:', characterArc);

    // 构建查询条件：admin用户可以修改所有数据，普通用户只能修改自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const character = await Character.findOne({ where: whereCondition });
    if (!character) {
      return res.status(404).json({ message: '角色不存在或无权修改' });
    }

    console.log('🔧 更新前角色数据:');
    console.log('  - 原性别:', character.gender);
    console.log('  - 原人物弧光:', character.characterArc);

    await character.update({
      name,
      role,
      gender,
      description,
      background,
      personality,
      abilities,
      abilities_detail,
      characterArc,
      relationships,
      isMainCharacter,
      importance
    });

    console.log('🔧 更新后角色数据:');
    console.log('  - 新性别:', character.gender);
    console.log('  - 新人物弧光:', character.characterArc);

    res.json(character);
  } catch (error) {
    console.error('更新角色失败:', error);
    res.status(500).json({ message: '更新角色失败', error: error.message });
  }
};

// 删除角色
exports.deleteCharacter = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以删除所有数据，普通用户只能删除自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const character = await Character.findOne({ where: whereCondition });
    if (!character) {
      return res.status(404).json({ message: '角色不存在或无权删除' });
    }

    await character.destroy();
    res.json({ message: '角色删除成功' });
  } catch (error) {
    console.error('删除角色失败:', error);
    res.status(500).json({ message: '删除角色失败', error: error.message });
  }
};
