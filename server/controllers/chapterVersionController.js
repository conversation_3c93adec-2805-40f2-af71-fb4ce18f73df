const { ChapterVersion, Chapter } = require('../models');

// 获取某个章节的所有版本
exports.getChapterVersions = async (req, res) => {
  try {
    const { chapterId } = req.params;
    const chapter = await Chapter.findByPk(chapterId);
    if (!chapter) {
      return res.status(404).json({ message: 'Chapter not found' });
    }
    const versions = await ChapterVersion.findAll({
      where: { chapterId },
      order: [['versionNumber', 'DESC']], // 或者 [['createdAt', 'DESC']]
    });
    res.status(200).json(versions);
  } catch (error) {
    console.error('Error fetching chapter versions:', error);
    res.status(500).json({ message: 'Error fetching chapter versions', error: error.message });
  }
};

// 获取特定章节的特定版本内容
exports.getChapterVersion = async (req, res) => {
  try {
    const { chapterId, versionId } = req.params;
    const version = await ChapterVersion.findOne({
      where: { id: versionId, chapterId },
    });
    if (!version) {
      return res.status(404).json({ message: 'Chapter version not found' });
    }
    res.status(200).json(version);
  } catch (error) {
    console.error('Error fetching chapter version content:', error);
    res.status(500).json({ message: 'Error fetching chapter version content', error: error.message });
  }
};

// 创建新的章节版本 (通常在更新章节内容时自动创建)
exports.createChapterVersion = async (req, res) => {
  try {
    const { chapterId } = req.params;
    const { content } = req.body;

    const chapter = await Chapter.findByPk(chapterId);
    if (!chapter) {
      return res.status(404).json({ message: 'Chapter not found' });
    }

    // 获取当前最大版本号
    const latestVersion = await ChapterVersion.findOne({
      where: { chapterId },
      order: [['versionNumber', 'DESC']],
    });

    const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

    const newVersion = await ChapterVersion.create({
      chapterId,
      content,
      versionNumber: nextVersionNumber,
    });
    res.status(201).json(newVersion);
  } catch (error) {
    console.error('Error creating chapter version:', error);
    res.status(500).json({ message: 'Error creating chapter version', error: error.message });
  }
};