const { Op } = require('sequelize');
const {
  WorkflowTemplate,
  WorkflowNode,
  WorkflowConnection,
  WorkflowExecution,
  WorkflowNodeExecution,
  User,
  Project,
  AIConfig,
  PromptTemplate
} = require('../models');
const websocketService = require('../services/websocketService');

// 生成执行ID
function generateExecutionId() {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
  const random = Math.random().toString(36).substring(2, 8);
  return `exec_${timestamp}_${random}`;
}

// 启动流程执行
exports.executeWorkflow = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const workflowId = req.params.id;
    const { projectId, inputData = {} } = req.body;

    // 检查流程模板是否存在且有权限访问
    const whereCondition = { id: workflowId, isActive: true };
    if (userRole !== 'admin') {
      whereCondition[Op.or] = [
        { userId },
        { isPublic: true }
      ];
    }

    const workflow = await WorkflowTemplate.findOne({
      where: whereCondition,
      include: [
        {
          model: WorkflowNode,
          as: 'nodes'
        },
        {
          model: WorkflowConnection,
          as: 'connections'
        }
      ]
    });

    if (!workflow) {
      return res.status(404).json({ message: '流程模板不存在或无权访问' });
    }

    // 检查项目权限（如果指定了项目）
    if (projectId) {
      const projectWhereCondition = { id: projectId };
      if (userRole !== 'admin') {
        projectWhereCondition.userId = userId;
      }
      
      const project = await Project.findOne({ where: projectWhereCondition });
      if (!project) {
        return res.status(404).json({ message: '项目不存在或无权访问' });
      }
    }

    // 生成执行ID
    const executionId = generateExecutionId();

    // 找到开始节点
    const startNode = workflow.nodes?.find(node => node.nodeType === 'start');
    if (!startNode) {
      return res.status(400).json({ message: '流程中没有找到开始节点' });
    }

    // 创建执行记录
    const execution = await WorkflowExecution.create({
      workflowId,
      executionId,
      status: 'running',
      currentNodeId: startNode.nodeId,
      inputData,
      outputData: {},
      executionLog: [{
        nodeId: startNode.nodeId,
        status: 'pending',
        timestamp: new Date().toISOString(),
        message: '流程执行已启动，等待开始节点处理'
      }],
      userId,
      projectId
    });

    // 增加模板使用次数
    await workflow.increment('usageCount');

    res.status(201).json({
      executionId,
      status: 'running',
      currentNodeId: startNode.nodeId,
      message: '流程执行已启动'
    });

    // 发送WebSocket通知
    websocketService.broadcastExecutionUpdate(executionId, {
      status: 'running',
      currentNodeId: startNode.nodeId,
      message: '流程执行已启动'
    });

    // 异步开始执行流程
    setImmediate(() => {
      executeWorkflowAsync(execution, workflow).catch(error => {
        console.error('流程执行异常:', error);
      });
    });

  } catch (error) {
    console.error('启动流程执行失败:', error);
    res.status(500).json({ message: '启动流程执行失败', error: error.message });
  }
};

// 异步执行流程
async function executeWorkflowAsync(execution, workflow) {
  try {
    const nodes = workflow.nodes || [];
    const connections = workflow.connections || [];
    
    let currentNodeId = execution.currentNodeId;
    let executionData = { ...execution.inputData };
    
    while (currentNodeId) {
      const currentNode = nodes.find(node => node.nodeId === currentNodeId);
      if (!currentNode) {
        throw new Error(`节点 ${currentNodeId} 不存在`);
      }

      // 更新执行状态
      await execution.update({
        currentNodeId,
        executionLog: [
          ...execution.executionLog,
          {
            nodeId: currentNodeId,
            status: 'running',
            timestamp: new Date().toISOString(),
            message: `开始执行节点: ${currentNode.name}`
          }
        ]
      });

      // 执行节点
      const nodeResult = await executeNode(currentNode, executionData, execution);
      
      if (nodeResult.error) {
        // 节点执行失败
        await execution.update({
          status: 'failed',
          errorMessage: nodeResult.error,
          completedAt: new Date(),
          executionLog: [
            ...execution.executionLog,
            {
              nodeId: currentNodeId,
              status: 'failed',
              timestamp: new Date().toISOString(),
              message: `节点执行失败: ${nodeResult.error}`
            }
          ]
        });
        return;
      }

      // 更新执行数据
      if (nodeResult.outputData) {
        executionData = { ...executionData, ...nodeResult.outputData };
      }

      // 如果是结束节点，完成执行
      if (currentNode.nodeType === 'end') {
        await execution.update({
          status: 'completed',
          outputData: executionData,
          completedAt: new Date(),
          executionLog: [
            ...execution.executionLog,
            {
              nodeId: currentNodeId,
              status: 'completed',
              timestamp: new Date().toISOString(),
              message: '流程执行完成'
            }
          ]
        });
        return;
      }

      // 如果是用户输入节点，暂停等待用户输入
      if (currentNode.nodeType === 'user_input') {
        await execution.update({
          status: 'paused',
          executionLog: [
            ...execution.executionLog,
            {
              nodeId: currentNodeId,
              status: 'waiting',
              timestamp: new Date().toISOString(),
              message: '等待用户输入'
            }
          ]
        });
        return;
      }

      // 找到下一个节点
      const nextConnection = connections.find(conn => conn.sourceNodeId === currentNodeId);
      if (!nextConnection) {
        throw new Error(`节点 ${currentNodeId} 没有找到下一个连接`);
      }

      currentNodeId = nextConnection.targetNodeId;
    }

  } catch (error) {
    console.error('流程执行异常:', error);
    await execution.update({
      status: 'failed',
      errorMessage: error.message,
      completedAt: new Date(),
      executionLog: [
        ...execution.executionLog,
        {
          nodeId: execution.currentNodeId,
          status: 'failed',
          timestamp: new Date().toISOString(),
          message: `流程执行异常: ${error.message}`
        }
      ]
    });
  }
}

// 执行单个节点
async function executeNode(node, inputData, execution) {
  const startTime = Date.now();
  
  try {
    let outputData = {};
    
    switch (node.nodeType) {
      case 'start':
        // 开始节点：直接返回输入数据
        outputData = inputData;
        break;
        
      case 'ai_generation':
        // AI生成节点：调用AI服务
        const aiResult = await executeAIGenerationNode(node, inputData);
        outputData = aiResult;
        break;
        
      case 'condition':
        // 条件判断节点：评估条件
        outputData = await executeConditionNode(node, inputData);
        break;
        
      case 'user_input':
        // 用户输入节点：等待用户输入
        outputData = inputData;
        break;
        
      case 'end':
        // 结束节点：整理输出数据
        outputData = inputData;
        break;
        
      default:
        throw new Error(`不支持的节点类型: ${node.nodeType}`);
    }

    const executionTime = Date.now() - startTime;

    // 记录节点执行结果
    await WorkflowNodeExecution.create({
      executionId: execution.executionId,
      nodeId: node.nodeId,
      status: 'completed',
      inputData,
      outputData,
      aiConfigId: node.config?.aiConfigId || null,
      promptTemplateId: node.config?.promptTemplateId || null,
      executionTimeMs: executionTime,
      startedAt: new Date(startTime),
      completedAt: new Date()
    });

    return { outputData };
    
  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    // 记录节点执行失败
    await WorkflowNodeExecution.create({
      executionId: execution.executionId,
      nodeId: node.nodeId,
      status: 'failed',
      inputData,
      outputData: {},
      aiConfigId: node.config?.aiConfigId || null,
      promptTemplateId: node.config?.promptTemplateId || null,
      executionTimeMs: executionTime,
      errorMessage: error.message,
      startedAt: new Date(startTime),
      completedAt: new Date()
    });

    return { error: error.message };
  }
}

// 执行AI生成节点
async function executeAIGenerationNode(node, inputData) {
  const { aiConfigId, promptTemplateId, inputMapping = {}, outputVariable } = node.config || {};
  
  if (!aiConfigId || !promptTemplateId) {
    throw new Error('AI生成节点缺少必要的配置：AI配置ID或提示词模板ID');
  }

  // 获取AI配置
  const aiConfig = await AIConfig.findByPk(aiConfigId);
  if (!aiConfig || !aiConfig.isActive) {
    throw new Error('AI配置不存在或已禁用');
  }

  // 获取提示词模板
  const promptTemplate = await PromptTemplate.findByPk(promptTemplateId);
  if (!promptTemplate) {
    throw new Error('提示词模板不存在');
  }

  // 处理输入映射，替换模板中的变量
  let prompt = promptTemplate.template;
  for (const [key, value] of Object.entries(inputMapping)) {
    const regex = new RegExp(`{{${key}}}`, 'g');
    prompt = prompt.replace(regex, value || '');
  }

  // 调用AI服务生成内容
  const DeepSeekService = require('../services/deepseekService');
  const aiService = new DeepSeekService(aiConfig.apiKey, aiConfig.baseUrl);
  
  const generatedContent = await aiService.generateText(prompt, {
    model: aiConfig.model,
    max_tokens: aiConfig.maxTokens,
    temperature: aiConfig.temperature
  });

  // 返回输出数据
  const outputData = { ...inputData };
  if (outputVariable) {
    outputData[outputVariable] = generatedContent;
  } else {
    outputData.generatedContent = generatedContent;
  }

  return outputData;
}

// 执行条件判断节点
async function executeConditionNode(node, inputData) {
  // 简单的条件判断实现
  // 这里可以根据需要实现更复杂的条件逻辑
  const { conditions = [] } = node.config || {};

  // 暂时直接返回输入数据
  return inputData;
}

// 继续执行工作流
async function continueWorkflowExecution(execution) {
  try {
    // 重新加载工作流数据
    const workflow = await WorkflowTemplate.findByPk(execution.workflowId, {
      include: [
        {
          model: WorkflowNode,
          as: 'nodes'
        },
        {
          model: WorkflowConnection,
          as: 'connections'
        }
      ]
    });

    if (!workflow) {
      throw new Error('工作流模板不存在');
    }

    const nodes = workflow.nodes || [];
    const connections = workflow.connections || [];

    let currentNodeId = execution.currentNodeId;
    let executionData = { ...execution.outputData };

    while (currentNodeId) {
      const currentNode = nodes.find(node => node.nodeId === currentNodeId);
      if (!currentNode) {
        throw new Error(`节点 ${currentNodeId} 不存在`);
      }

      // 更新执行状态
      await execution.update({
        currentNodeId,
        executionLog: [
          ...execution.executionLog,
          {
            nodeId: currentNodeId,
            status: 'running',
            timestamp: new Date().toISOString(),
            message: `继续执行节点: ${currentNode.name}`
          }
        ]
      });

      // 执行节点
      const nodeResult = await executeNode(currentNode, executionData, execution);

      if (nodeResult.error) {
        // 节点执行失败
        await execution.update({
          status: 'failed',
          errorMessage: nodeResult.error,
          completedAt: new Date(),
          executionLog: [
            ...execution.executionLog,
            {
              nodeId: currentNodeId,
              status: 'failed',
              timestamp: new Date().toISOString(),
              message: `节点执行失败: ${nodeResult.error}`
            }
          ]
        });
        return;
      }

      // 更新执行数据
      if (nodeResult.outputData) {
        executionData = { ...executionData, ...nodeResult.outputData };
      }

      // 如果是结束节点，完成执行
      if (currentNode.nodeType === 'end') {
        await execution.update({
          status: 'completed',
          outputData: executionData,
          completedAt: new Date(),
          executionLog: [
            ...execution.executionLog,
            {
              nodeId: currentNodeId,
              status: 'completed',
              timestamp: new Date().toISOString(),
              message: '流程执行完成'
            }
          ]
        });
        return;
      }

      // 如果是用户输入节点，暂停等待用户输入
      if (currentNode.nodeType === 'user_input') {
        await execution.update({
          status: 'paused',
          outputData: executionData,
          executionLog: [
            ...execution.executionLog,
            {
              nodeId: currentNodeId,
              status: 'waiting',
              timestamp: new Date().toISOString(),
              message: '等待用户输入'
            }
          ]
        });
        return;
      }

      // 找到下一个节点
      const nextConnection = connections.find(conn => conn.sourceNodeId === currentNodeId);
      if (!nextConnection) {
        throw new Error(`节点 ${currentNodeId} 没有找到下一个连接`);
      }

      currentNodeId = nextConnection.targetNodeId;
    }

  } catch (error) {
    console.error('继续流程执行异常:', error);
    await execution.update({
      status: 'failed',
      errorMessage: error.message,
      completedAt: new Date(),
      executionLog: [
        ...execution.executionLog,
        {
          nodeId: execution.currentNodeId,
          status: 'failed',
          timestamp: new Date().toISOString(),
          message: `流程执行异常: ${error.message}`
        }
      ]
    });
  }
}

// 继续执行流程
exports.continueExecution = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const executionId = req.params.executionId;
    const { nodeId, inputData = {} } = req.body;

    // 构建查询条件：admin用户可以操作所有执行，普通用户只能操作自己的
    const whereCondition = { executionId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const execution = await WorkflowExecution.findOne({ where: whereCondition });
    if (!execution) {
      return res.status(404).json({ message: '执行记录不存在或无权访问' });
    }

    if (execution.status !== 'paused') {
      return res.status(400).json({ message: '只能继续已暂停的流程' });
    }

    // 更新执行状态
    await execution.update({
      status: 'running',
      outputData: { ...execution.outputData, ...inputData },
      executionLog: [
        ...execution.executionLog,
        {
          nodeId: nodeId || execution.currentNodeId,
          status: 'resumed',
          timestamp: new Date().toISOString(),
          message: '流程已继续执行'
        }
      ]
    });

    // 发送WebSocket通知
    websocketService.broadcastExecutionUpdate(executionId, {
      status: 'running',
      currentNodeId: execution.currentNodeId,
      message: '流程已继续执行'
    });

    res.json({ message: '流程已继续执行' });

    // 异步继续执行流程
    setImmediate(() => {
      continueWorkflowExecution(execution).catch(error => {
        console.error('继续流程执行异常:', error);
      });
    });

  } catch (error) {
    console.error('继续执行失败:', error);
    res.status(500).json({ message: '继续执行失败', error: error.message });
  }
};

// 暂停执行流程
exports.pauseExecution = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const executionId = req.params.executionId;

    const whereCondition = { executionId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const execution = await WorkflowExecution.findOne({ where: whereCondition });
    if (!execution) {
      return res.status(404).json({ message: '执行记录不存在或无权访问' });
    }

    if (execution.status !== 'running') {
      return res.status(400).json({ message: '只能暂停正在运行的流程' });
    }

    await execution.update({
      status: 'paused',
      executionLog: [
        ...execution.executionLog,
        {
          nodeId: execution.currentNodeId,
          status: 'paused',
          timestamp: new Date().toISOString(),
          message: '流程已暂停'
        }
      ]
    });

    res.json({ message: '流程已暂停' });
  } catch (error) {
    console.error('暂停执行失败:', error);
    res.status(500).json({ message: '暂停执行失败', error: error.message });
  }
};

// 取消执行流程
exports.cancelExecution = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const executionId = req.params.executionId;

    const whereCondition = { executionId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const execution = await WorkflowExecution.findOne({ where: whereCondition });
    if (!execution) {
      return res.status(404).json({ message: '执行记录不存在或无权访问' });
    }

    if (!['running', 'paused'].includes(execution.status)) {
      return res.status(400).json({ message: '只能取消正在运行或已暂停的流程' });
    }

    await execution.update({
      status: 'cancelled',
      completedAt: new Date(),
      executionLog: [
        ...execution.executionLog,
        {
          nodeId: execution.currentNodeId,
          status: 'cancelled',
          timestamp: new Date().toISOString(),
          message: '流程已取消'
        }
      ]
    });

    res.json({ message: '流程已取消' });
  } catch (error) {
    console.error('取消执行失败:', error);
    res.status(500).json({ message: '取消执行失败', error: error.message });
  }
};

// 获取执行状态
exports.getExecutionStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const executionId = req.params.executionId;

    // 构建查询条件：admin用户可以查看所有执行，普通用户只能查看自己的
    const whereCondition = { executionId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const execution = await WorkflowExecution.findOne({
      where: whereCondition,
      include: [
        {
          model: WorkflowTemplate,
          attributes: ['id', 'name']
        },
        {
          model: User,
          attributes: ['id', 'username']
        },
        {
          model: Project,
          attributes: ['id', 'name']
        }
      ]
    });

    if (!execution) {
      return res.status(404).json({ message: '执行记录不存在或无权访问' });
    }

    // 计算进度
    const nodeExecutions = await WorkflowNodeExecution.findAll({
      where: { executionId },
      order: [['startedAt', 'ASC']]
    });

    const totalNodes = await WorkflowNode.count({
      where: { workflowId: execution.workflowId }
    });

    const completedNodes = nodeExecutions.filter(ne => ne.status === 'completed').length;
    const progress = totalNodes > 0 ? Math.round((completedNodes / totalNodes) * 100) : 0;

    // 计算预计完成时间
    let estimatedCompletionAt = null;
    if (execution.status === 'running' && completedNodes > 0) {
      const avgTimePerNode = nodeExecutions
        .filter(ne => ne.executionTimeMs)
        .reduce((sum, ne) => sum + ne.executionTimeMs, 0) / completedNodes;

      const remainingNodes = totalNodes - completedNodes;
      const estimatedRemainingTime = remainingNodes * avgTimePerNode;
      estimatedCompletionAt = new Date(Date.now() + estimatedRemainingTime);
    }

    res.json({
      ...execution.toJSON(),
      progress,
      estimatedCompletionAt,
      nodeExecutions
    });
  } catch (error) {
    console.error('获取执行状态失败:', error);
    res.status(500).json({ message: '获取执行状态失败', error: error.message });
  }
};

// 获取执行历史
exports.getExecutionHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const {
      page = 1,
      limit = 20,
      workflowId,
      status,
      projectId
    } = req.query;

    // 构建查询条件
    const whereCondition = {};

    // 权限控制：admin用户可以查看所有执行，普通用户只能查看自己的
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    // 其他筛选条件
    if (workflowId) {
      whereCondition.workflowId = workflowId;
    }

    if (status) {
      whereCondition.status = status;
    }

    if (projectId) {
      whereCondition.projectId = projectId;
    }

    const offset = (page - 1) * limit;

    const { count, rows } = await WorkflowExecution.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: WorkflowTemplate,
          attributes: ['id', 'name', 'category']
        },
        {
          model: User,
          attributes: ['id', 'username']
        },
        {
          model: Project,
          attributes: ['id', 'name']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 计算每个执行的进度
    const executionsWithProgress = await Promise.all(rows.map(async (execution) => {
      const nodeExecutions = await WorkflowNodeExecution.findAll({
        where: { executionId: execution.executionId },
        attributes: ['status']
      });

      const totalNodes = await WorkflowNode.count({
        where: { workflowId: execution.workflowId }
      });

      const completedNodes = nodeExecutions.filter(ne => ne.status === 'completed').length;
      const progress = totalNodes > 0 ? Math.round((completedNodes / totalNodes) * 100) : 0;

      return {
        ...execution.toJSON(),
        progress
      };
    }));

    res.json({
      data: executionsWithProgress,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取执行历史失败:', error);
    res.status(500).json({ message: '获取执行历史失败', error: error.message });
  }
};
