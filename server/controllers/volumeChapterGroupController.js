const { VolumeChapterGroup, Volume, Project, VolumeChapterGroupVersion } = require('../models');

// 根据分卷ID获取章节分组
exports.getChapterGroupsByVolume = async (req, res) => {
  try {
    const volumeId = req.params.volumeId;
    const userId = req.user.id;

    // 检查分卷是否存在且用户有权访问
    const volume = await Volume.findOne({
      where: {
        id: volumeId,
        userId: userId
      }
    });
    if (!volume) {
      return res.status(404).json({ message: '分卷不存在或无权访问' });
    }

    const chapterGroups = await VolumeChapterGroup.findAll({
      where: {
        volumeId,
        userId: userId // 只获取用户自己的章节分组
      },
      order: [['order', 'ASC']]
    });

    res.json(chapterGroups);
  } catch (error) {
    console.error('获取章节分组列表失败:', error);
    res.status(500).json({ message: '获取章节分组列表失败', error: error.message });
  }
};

// 根据ID获取章节分组
exports.getChapterGroupById = async (req, res) => {
  try {
    const userId = req.user.id;

    const chapterGroup = await VolumeChapterGroup.findOne({
      where: {
        id: req.params.id,
        userId: userId // 确保用户只能访问自己的章节分组
      }
    });

    if (!chapterGroup) {
      return res.status(404).json({ message: '章节分组不存在或无权访问' });
    }

    res.json(chapterGroup);
  } catch (error) {
    console.error('获取章节分组失败:', error);
    res.status(500).json({ message: '获取章节分组失败', error: error.message });
  }
};

// 创建章节分组
exports.createChapterGroup = async (req, res) => {
  try {
    const { volumeId, title, chapterRange, summary, progressRate, order } = req.body;
    const userId = req.user.id;

    if (!volumeId || !title || !chapterRange) {
      return res.status(400).json({ message: '分卷ID、标题和章节范围不能为空' });
    }

    // 检查分卷是否存在且用户有权访问
    const volume = await Volume.findOne({
      where: {
        id: volumeId,
        userId: userId
      }
    });
    if (!volume) {
      return res.status(404).json({ message: '分卷不存在或无权访问' });
    }

    const chapterGroup = await VolumeChapterGroup.create({
      volumeId,
      title,
      chapterRange,
      summary: summary || '',
      progressRate: progressRate || 0,
      order: order || 0,
      userId: userId // 设置用户ID
    });

    res.status(201).json(chapterGroup);
  } catch (error) {
    console.error('创建章节分组失败:', error);
    res.status(500).json({ message: '创建章节分组失败', error: error.message });
  }
};

// 更新章节分组
exports.updateChapterGroup = async (req, res) => {
  try {
    const { title, chapterRange, summary, progressRate, order } = req.body;
    const userId = req.user.id;

    const chapterGroup = await VolumeChapterGroup.findOne({
      where: {
        id: req.params.id,
        userId: userId // 确保用户只能更新自己的章节分组
      }
    });
    if (!chapterGroup) {
      return res.status(404).json({ message: '章节分组不存在或无权访问' });
    }

    // 保存旧数据以便创建版本
    const oldData = {
      title: chapterGroup.title,
      chapterRange: chapterGroup.chapterRange,
      summary: chapterGroup.summary,
      progressRate: chapterGroup.progressRate
    };

    // 更新章节分组
    await chapterGroup.update({
      title,
      chapterRange,
      summary,
      progressRate,
      order
    });

    // 如果关键字段发生变化，创建新版本
    if (oldData.title !== title || oldData.chapterRange !== chapterRange ||
        oldData.summary !== summary || oldData.progressRate !== progressRate) {
      try {
        // 获取当前最大版本号
        const latestVersion = await VolumeChapterGroupVersion.findOne({
          where: { chapterGroupId: chapterGroup.id },
          order: [['versionNumber', 'DESC']],
        });

        const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

        // 创建新版本
        await VolumeChapterGroupVersion.create({
          chapterGroupId: chapterGroup.id,
          title,
          chapterRange,
          summary,
          progressRate,
          versionNumber: nextVersionNumber,
        });
      } catch (versionError) {
        console.error('创建章节分组版本失败:', versionError);
        // 不影响主流程，只记录错误
      }
    }

    res.json(chapterGroup);
  } catch (error) {
    console.error('更新章节分组失败:', error);
    res.status(500).json({ message: '更新章节分组失败', error: error.message });
  }
};

// 删除章节分组
exports.deleteChapterGroup = async (req, res) => {
  try {
    const userId = req.user.id;

    const chapterGroup = await VolumeChapterGroup.findOne({
      where: {
        id: req.params.id,
        userId: userId // 确保用户只能删除自己的章节分组
      }
    });
    if (!chapterGroup) {
      return res.status(404).json({ message: '章节分组不存在或无权访问' });
    }

    await chapterGroup.destroy();
    res.json({ message: '章节分组删除成功' });
  } catch (error) {
    console.error('删除章节分组失败:', error);
    res.status(500).json({ message: '删除章节分组失败', error: error.message });
  }
};
