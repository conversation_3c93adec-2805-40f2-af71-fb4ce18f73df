const { Project, Chapter, Volume, ChapterVersion, Character, Outline, Clue } = require('../models');
const { Sequelize } = require('sequelize');

// 获取所有项目（包含统计信息）
exports.getAllProjects = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有项目，普通用户只能看到自己创建的项目
    const whereCondition = userRole === 'admin' ? {} : { userId };

    const projects = await Project.findAll({
      where: whereCondition,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Chapter,
          attributes: ['id', 'status', 'content'],
          required: false
        },
        {
          model: Volume,
          attributes: ['id', 'wordCount', 'chapterCount'],
          required: false
        },
        {
          model: Character,
          attributes: ['id'],
          required: false
        },
        {
          model: Outline,
          attributes: ['id'],
          required: false
        },
        {
          model: Clue,
          attributes: ['id'],
          required: false
        }
      ]
    });

    // 为每个项目计算统计信息
    const projectsWithStats = projects.map(project => {
      const projectData = project.toJSON();

      // 计算章节统计
      const chapters = projectData.Chapters || [];
      const totalChapters = chapters.length;
      const completedChapters = chapters.filter(ch => ch.status === 'completed').length;
      const draftChapters = chapters.filter(ch => ch.status === 'draft').length;

      // 计算实际字数（基于章节内容）
      const actualWordCount = chapters.reduce((total, chapter) => {
        if (chapter.content) {
          // 简单的字数计算（去除HTML标签和空格）
          const textContent = chapter.content.replace(/<[^>]*>/g, '').replace(/\s+/g, '');
          return total + textContent.length;
        }
        return total;
      }, 0);

      // 计算项目进度（基于章节完成情况）
      let progress = 0;
      if (totalChapters > 0) {
        progress = Math.round((completedChapters / totalChapters) * 100);
      } else if (actualWordCount > 0 && projectData.wordCount > 0) {
        // 如果没有章节，基于字数计算进度
        progress = Math.min(100, Math.round((actualWordCount / projectData.wordCount) * 100));
      }

      // 计算分卷统计
      const volumes = projectData.Volumes || [];
      const totalVolumes = volumes.length;

      // 其他统计
      const characterCount = (projectData.Characters || []).length;
      const outlineCount = (projectData.Outlines || []).length;
      const clueCount = (projectData.Clues || []).length;

      // 返回项目数据和统计信息
      return {
        ...projectData,
        // 移除关联数据，只保留统计信息
        Chapters: undefined,
        Volumes: undefined,
        Characters: undefined,
        Outlines: undefined,
        Clues: undefined,
        // 添加统计信息
        stats: {
          totalChapters,
          completedChapters,
          draftChapters,
          actualWordCount,
          progress,
          totalVolumes,
          characterCount,
          outlineCount,
          clueCount
        }
      };
    });

    res.json(projectsWithStats);
  } catch (error) {
    console.error('获取项目列表失败:', error);
    res.status(500).json({ message: '获取项目列表失败', error: error.message });
  }
};

// 根据ID获取项目
exports.getProjectById = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有项目，普通用户只能看到自己创建的项目
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const project = await Project.findOne({ where: whereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }
    res.json(project);
  } catch (error) {
    console.error('获取项目失败:', error);
    res.status(500).json({ message: '获取项目失败', error: error.message });
  }
};

// 创建项目
exports.createProject = async (req, res) => {
  try {
    const { name, type, wordCount, targetAudience, style } = req.body;
    const userId = req.user.id; // 获取当前登录用户ID

    if (!name || !type) {
      return res.status(400).json({ message: '项目名称和类型不能为空' });
    }

    const project = await Project.create({
      name,
      type,
      wordCount,
      targetAudience,
      style,
      userId // 关联当前用户
    });

    res.status(201).json(project);
  } catch (error) {
    console.error('创建项目失败:', error);
    res.status(500).json({ message: '创建项目失败', error: error.message });
  }
};

// 更新项目
exports.updateProject = async (req, res) => {
  try {
    const { name, type, wordCount, targetAudience, style } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以修改所有项目，普通用户只能修改自己创建的项目
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const project = await Project.findOne({ where: whereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权修改' });
    }

    await project.update({
      name,
      type,
      wordCount,
      targetAudience,
      style
    });

    res.json(project);
  } catch (error) {
    console.error('更新项目失败:', error);
    res.status(500).json({ message: '更新项目失败', error: error.message });
  }
};

// 删除项目
exports.deleteProject = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以删除所有项目，普通用户只能删除自己创建的项目
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const project = await Project.findOne({ where: whereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权删除' });
    }

    await project.destroy();
    res.json({ message: '项目删除成功' });
  } catch (error) {
    console.error('删除项目失败:', error);
    res.status(500).json({ message: '删除项目失败', error: error.message });
  }
};

// 获取项目预览数据（包含所有章节内容）
exports.getProjectPreview = async (req, res) => {
  try {
    const projectId = req.params.id;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有项目，普通用户只能看到自己创建的项目
    const whereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };

    const project = await Project.findOne({ where: whereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    // 获取项目的所有章节，按顺序排列，包含版本信息
    const chapters = await Chapter.findAll({
      where: { projectId },
      order: [['order', 'ASC']],
      attributes: ['id', 'title', 'content', 'order', 'status'],
      include: [{
        model: ChapterVersion,
        as: 'versions',
        attributes: ['id', 'versionNumber', 'content', 'createdAt'],
        order: [['versionNumber', 'DESC']]
      }]
    });

    // 获取项目的分卷信息
    const volumes = await Volume.findAll({
      where: { projectId },
      order: [['order', 'ASC']],
      attributes: ['id', 'title', 'summary', 'order']
    });

    res.json({
      project: {
        id: project.id,
        name: project.name,
        type: project.type,
        targetAudience: project.targetAudience,
        style: project.style
      },
      chapters,
      volumes
    });
  } catch (error) {
    console.error('获取项目预览数据失败:', error);
    res.status(500).json({ message: '获取项目预览数据失败', error: error.message });
  }
};

// 获取项目基本信息列表（优化版本，快速加载）
exports.getProjectsBasic = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有项目，普通用户只能看到自己创建的项目
    const whereCondition = userRole === 'admin' ? {} : { userId };

    const projects = await Project.findAll({
      where: whereCondition,
      order: [['createdAt', 'DESC']],
      attributes: ['id', 'name', 'type', 'wordCount', 'targetAudience', 'style', 'userId', 'createdAt', 'updatedAt']
    });

    res.json(projects);
  } catch (error) {
    console.error('获取项目基本信息失败:', error);
    res.status(500).json({ message: '获取项目基本信息失败', error: error.message });
  }
};

// 获取单个项目的统计信息
exports.getProjectStats = async (req, res) => {
  try {
    const projectId = req.params.id;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有项目，普通用户只能看到自己创建的项目
    const whereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };

    const project = await Project.findOne({
      where: whereCondition,
      include: [
        {
          model: Chapter,
          attributes: ['id', 'status', 'content'],
          required: false
        },
        {
          model: Volume,
          attributes: ['id', 'wordCount', 'chapterCount'],
          required: false
        },
        {
          model: Character,
          attributes: ['id'],
          required: false
        },
        {
          model: Outline,
          attributes: ['id'],
          required: false
        },
        {
          model: Clue,
          attributes: ['id'],
          required: false
        }
      ]
    });

    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    const projectData = project.toJSON();

    // 计算章节统计
    const chapters = projectData.Chapters || [];
    const totalChapters = chapters.length;
    const completedChapters = chapters.filter(ch => ch.status === 'completed').length;
    const draftChapters = chapters.filter(ch => ch.status === 'draft').length;

    // 计算实际字数（基于章节内容）
    const actualWordCount = chapters.reduce((total, chapter) => {
      if (chapter.content) {
        // 简单的字数计算（去除HTML标签和空格）
        const textContent = chapter.content.replace(/<[^>]*>/g, '').replace(/\s+/g, '');
        return total + textContent.length;
      }
      return total;
    }, 0);

    // 计算项目进度（基于章节完成情况）
    let progress = 0;
    if (totalChapters > 0) {
      progress = Math.round((completedChapters / totalChapters) * 100);
    } else if (actualWordCount > 0 && projectData.wordCount > 0) {
      // 如果没有章节，基于字数计算进度
      progress = Math.min(100, Math.round((actualWordCount / projectData.wordCount) * 100));
    }

    // 计算分卷统计
    const volumes = projectData.Volumes || [];
    const totalVolumes = volumes.length;

    // 其他统计
    const characterCount = (projectData.Characters || []).length;
    const outlineCount = (projectData.Outlines || []).length;
    const clueCount = (projectData.Clues || []).length;

    // 返回统计信息
    const stats = {
      totalChapters,
      completedChapters,
      draftChapters,
      actualWordCount,
      progress,
      totalVolumes,
      characterCount,
      outlineCount,
      clueCount
    };

    res.json(stats);
  } catch (error) {
    console.error('获取项目统计信息失败:', error);
    res.status(500).json({ message: '获取项目统计信息失败', error: error.message });
  }
};

// 批量获取多个项目的统计信息
exports.getBatchProjectStats = async (req, res) => {
  try {
    const { projectIds } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    if (!projectIds || !Array.isArray(projectIds) || projectIds.length === 0) {
      return res.status(400).json({ message: '项目ID列表不能为空' });
    }

    // 构建查询条件：admin用户可以看到所有项目，普通用户只能看到自己创建的项目
    const whereCondition = userRole === 'admin' ?
      { id: projectIds } :
      { id: projectIds, userId };

    const projects = await Project.findAll({
      where: whereCondition,
      include: [
        {
          model: Chapter,
          attributes: ['id', 'status', 'content'],
          required: false
        },
        {
          model: Volume,
          attributes: ['id', 'wordCount', 'chapterCount'],
          required: false
        },
        {
          model: Character,
          attributes: ['id'],
          required: false
        },
        {
          model: Outline,
          attributes: ['id'],
          required: false
        },
        {
          model: Clue,
          attributes: ['id'],
          required: false
        }
      ]
    });

    // 计算每个项目的统计信息
    const statsMap = {};
    projects.forEach(project => {
      const projectData = project.toJSON();

      // 计算章节统计
      const chapters = projectData.Chapters || [];
      const totalChapters = chapters.length;
      const completedChapters = chapters.filter(ch => ch.status === 'completed').length;
      const draftChapters = chapters.filter(ch => ch.status === 'draft').length;

      // 计算实际字数（基于章节内容）
      const actualWordCount = chapters.reduce((total, chapter) => {
        if (chapter.content) {
          const textContent = chapter.content.replace(/<[^>]*>/g, '').replace(/\s+/g, '');
          return total + textContent.length;
        }
        return total;
      }, 0);

      // 计算项目进度
      let progress = 0;
      if (totalChapters > 0) {
        progress = Math.round((completedChapters / totalChapters) * 100);
      } else if (actualWordCount > 0 && projectData.wordCount > 0) {
        progress = Math.min(100, Math.round((actualWordCount / projectData.wordCount) * 100));
      }

      // 计算分卷统计
      const volumes = projectData.Volumes || [];
      const totalVolumes = volumes.length;

      // 其他统计
      const characterCount = (projectData.Characters || []).length;
      const outlineCount = (projectData.Outlines || []).length;
      const clueCount = (projectData.Clues || []).length;

      statsMap[project.id] = {
        totalChapters,
        completedChapters,
        draftChapters,
        actualWordCount,
        progress,
        totalVolumes,
        characterCount,
        outlineCount,
        clueCount
      };
    });

    res.json(statsMap);
  } catch (error) {
    console.error('批量获取项目统计信息失败:', error);
    res.status(500).json({ message: '批量获取项目统计信息失败', error: error.message });
  }
};
