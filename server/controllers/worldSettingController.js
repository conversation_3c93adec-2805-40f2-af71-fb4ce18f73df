const { WorldSetting, Project } = require('../models');

// 根据项目ID获取世界观设定
exports.getWorldSettingsByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 检查项目是否存在且用户有权访问
    const projectWhereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };
    const project = await Project.findOne({ where: projectWhereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    // 构建世界观设定查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的数据
    const worldSettingWhereCondition = userRole === 'admin' ? { projectId } : { projectId, userId };

    const worldSettings = await WorldSetting.findAll({
      where: worldSettingWhereCondition,
      order: [['createdAt', 'ASC']]
    });

    res.json(worldSettings);
  } catch (error) {
    console.error('获取世界观设定失败:', error);
    res.status(500).json({ message: '获取世界观设定失败', error: error.message });
  }
};

// 根据ID获取世界观设定
exports.getWorldSettingById = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const worldSetting = await WorldSetting.findOne({ where: whereCondition });
    if (!worldSetting) {
      return res.status(404).json({ message: '世界观设定不存在或无权访问' });
    }

    res.json(worldSetting);
  } catch (error) {
    console.error('获取世界观设定失败:', error);
    res.status(500).json({ message: '获取世界观设定失败', error: error.message });
  }
};

// 创建世界观设定
exports.createWorldSetting = async (req, res) => {
  try {
    const { projectId, name, description, category } = req.body; // 修改：title -> name, content -> description
    const userId = req.user.id; // 获取当前登录用户ID
    const userRole = req.user.role;

    if (!projectId || !name || !description || !category) { // 修改：title -> name, content -> description
      return res.status(400).json({ message: '项目ID、名称、描述和类别不能为空' }); // 修改：标题 -> 名称, 内容 -> 描述
    }

    // 检查项目是否存在且用户有权访问
    const projectWhereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };
    const project = await Project.findOne({ where: projectWhereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    const worldSetting = await WorldSetting.create({
      projectId,
      title: name, // 修改：映射 name 到 title
      content: description, // 修改：映射 description 到 content
      category,
      userId // 关联当前用户
    });

    res.status(201).json(worldSetting);
  } catch (error) {
    console.error('创建世界观设定失败:', error);
    res.status(500).json({ message: '创建世界观设定失败', error: error.message });
  }
};

// 更新世界观设定
exports.updateWorldSetting = async (req, res) => {
  try {
    const { name, description, category } = req.body; // 修改：title -> name, content -> description
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以修改所有数据，普通用户只能修改自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const worldSetting = await WorldSetting.findOne({ where: whereCondition });
    if (!worldSetting) {
      return res.status(404).json({ message: '世界观设定不存在或无权修改' });
    }

    await worldSetting.update({
      title: name, // 修改：映射 name 到 title
      content: description, // 修改：映射 description 到 content
      category
    });

    res.json(worldSetting);
  } catch (error) {
    console.error('更新世界观设定失败:', error);
    res.status(500).json({ message: '更新世界观设定失败', error: error.message });
  }
};

// 删除世界观设定
exports.deleteWorldSetting = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以删除所有数据，普通用户只能删除自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const worldSetting = await WorldSetting.findOne({ where: whereCondition });
    if (!worldSetting) {
      return res.status(404).json({ message: '世界观设定不存在或无权删除' });
    }

    await worldSetting.destroy();
    res.json({ message: '世界观设定删除成功' });
  } catch (error) {
    console.error('删除世界观设定失败:', error);
    res.status(500).json({ message: '删除世界观设定失败', error: error.message });
  }
};
