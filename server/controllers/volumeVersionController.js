const { VolumeVersion, Volume } = require('../models');

// 获取某个分卷的所有版本
exports.getVolumeVersions = async (req, res) => {
  try {
    const { volumeId } = req.params;
    const volume = await Volume.findByPk(volumeId);
    if (!volume) {
      return res.status(404).json({ message: '分卷不存在' });
    }
    const versions = await VolumeVersion.findAll({
      where: { volumeId },
      order: [['versionNumber', 'DESC']], // 按版本号降序排列
    });
    res.status(200).json(versions);
  } catch (error) {
    console.error('获取分卷版本列表失败:', error);
    res.status(500).json({ message: '获取分卷版本列表失败', error: error.message });
  }
};

// 获取特定分卷的特定版本内容
exports.getVolumeVersion = async (req, res) => {
  try {
    const { volumeId, versionId } = req.params;
    const version = await VolumeVersion.findOne({
      where: { id: versionId, volumeId },
    });
    if (!version) {
      return res.status(404).json({ message: '分卷版本不存在' });
    }
    res.status(200).json(version);
  } catch (error) {
    console.error('获取分卷版本内容失败:', error);
    res.status(500).json({ message: '获取分卷版本内容失败', error: error.message });
  }
};

// 创建新的分卷版本 (通常在更新分卷内容时自动创建)
exports.createVolumeVersion = async (req, res) => {
  try {
    const { volumeId } = req.params;
    const { title, summary, wordCount, chapterCount, writingRequirements } = req.body;

    const volume = await Volume.findByPk(volumeId);
    if (!volume) {
      return res.status(404).json({ message: '分卷不存在' });
    }

    // 获取当前最大版本号
    const latestVersion = await VolumeVersion.findOne({
      where: { volumeId },
      order: [['versionNumber', 'DESC']],
    });

    const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

    const newVersion = await VolumeVersion.create({
      volumeId,
      title: title || volume.title,
      summary: summary || volume.summary,
      wordCount: wordCount !== undefined ? wordCount : volume.wordCount,
      chapterCount: chapterCount !== undefined ? chapterCount : volume.chapterCount,
      writingRequirements: writingRequirements !== undefined ? writingRequirements : volume.writingRequirements,
      versionNumber: nextVersionNumber,
    });
    res.status(201).json(newVersion);
  } catch (error) {
    console.error('创建分卷版本失败:', error);
    res.status(500).json({ message: '创建分卷版本失败', error: error.message });
  }
};
