const { Op, sequelize } = require('sequelize');
const {
  WorkflowTemplate,
  WorkflowNode,
  WorkflowConnection,
  WorkflowExecution,
  WorkflowNodeExecution,
  WorkflowVersion,
  WorkflowCategory,
  User,
  Project
} = require('../models');
const { sequelize: dbInstance } = require('../models');

// 获取流程模板列表
exports.getWorkflowTemplates = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { 
      page = 1, 
      limit = 20, 
      category, 
      search, 
      isPublic,
      userId: filterUserId 
    } = req.query;

    // 构建查询条件
    const whereCondition = {};
    
    // 权限控制：admin可以查看所有数据，普通用户只能查看自己的和公开的
    if (userRole === 'admin') {
      if (filterUserId) {
        whereCondition.userId = filterUserId;
      }
      if (isPublic !== undefined) {
        whereCondition.isPublic = isPublic === 'true';
      }
    } else {
      // 普通用户只能看到自己创建的或公开的模板
      whereCondition[Op.or] = [
        { userId },
        { isPublic: true }
      ];
    }

    // 其他筛选条件
    if (category) {
      whereCondition.category = category;
    }
    
    if (search) {
      whereCondition[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } }
      ];
    }

    const offset = (page - 1) * limit;
    
    const { count, rows } = await WorkflowTemplate.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: User,
          attributes: ['id', 'username']
        }
      ],
      order: [['updatedAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      data: rows,
      total: count,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('获取流程模板列表失败:', error);
    res.status(500).json({ message: '获取流程模板列表失败', error: error.message });
  }
};

// 获取流程模板详情
exports.getWorkflowTemplateById = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const templateId = req.params.id;

    // 构建查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的或公开的
    const whereCondition = { id: templateId };
    if (userRole !== 'admin') {
      whereCondition[Op.or] = [
        { userId },
        { isPublic: true }
      ];
    }

    const template = await WorkflowTemplate.findOne({
      where: whereCondition,
      include: [
        {
          model: User,
          attributes: ['id', 'username']
        },
        {
          model: WorkflowNode,
          as: 'nodes'
        },
        {
          model: WorkflowConnection,
          as: 'connections'
        },
        {
          model: WorkflowVersion,
          as: 'versions',
          include: [
            {
              model: User,
              attributes: ['id', 'username']
            }
          ],
          order: [['createdAt', 'DESC']]
        }
      ]
    });

    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权访问' });
    }

    res.json(template);
  } catch (error) {
    console.error('获取流程模板详情失败:', error);
    res.status(500).json({ message: '获取流程模板详情失败', error: error.message });
  }
};

// 创建流程模板
exports.createWorkflowTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { 
      name, 
      description, 
      category, 
      config, 
      tags, 
      isPublic = false,
      thumbnail 
    } = req.body;

    // 验证必填字段
    if (!name) {
      return res.status(400).json({ message: '流程名称不能为空' });
    }

    // 只有admin可以设置公开模板
    const finalIsPublic = userRole === 'admin' ? isPublic : false;

    const template = await WorkflowTemplate.create({
      name,
      description,
      category,
      config: config || {},
      tags: tags || [],
      isPublic: finalIsPublic,
      thumbnail,
      userId
    });

    // 如果有节点和连接配置，创建相应的记录
    if (config && config.nodes) {
      for (const node of config.nodes) {
        await WorkflowNode.create({
          workflowId: template.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          name: node.name,
          description: node.description,
          positionX: node.positionX,
          positionY: node.positionY,
          config: node.config || {}
        });
      }
    }

    if (config && config.connections) {
      for (const connection of config.connections) {
        await WorkflowConnection.create({
          workflowId: template.id,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          conditionConfig: connection.conditionConfig || {}
        });
      }
    }

    // 创建初始版本
    await WorkflowVersion.create({
      workflowId: template.id,
      version: '1.0.0',
      config: config || {},
      changeLog: '初始版本',
      isCurrent: true,
      createdBy: userId
    });

    res.status(201).json({
      id: template.id,
      message: '流程模板创建成功'
    });
  } catch (error) {
    console.error('创建流程模板失败:', error);
    res.status(500).json({ message: '创建流程模板失败', error: error.message });
  }
};

// 更新流程模板
exports.updateWorkflowTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const templateId = req.params.id;
    const { 
      name, 
      description, 
      category, 
      config, 
      tags, 
      isPublic,
      thumbnail,
      createNewVersion = false,
      changeLog 
    } = req.body;

    // 构建查询条件：admin用户可以编辑所有数据，普通用户只能编辑自己创建的
    const whereCondition = { id: templateId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const template = await WorkflowTemplate.findOne({ where: whereCondition });
    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权编辑' });
    }

    // 更新模板
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (category !== undefined) updateData.category = category;
    if (config !== undefined) updateData.config = config;
    if (tags !== undefined) updateData.tags = tags;
    if (thumbnail !== undefined) updateData.thumbnail = thumbnail;
    
    // 只有admin可以设置公开状态
    if (userRole === 'admin' && isPublic !== undefined) {
      updateData.isPublic = isPublic;
    }

    await template.update(updateData);

    // 如果更新了配置，同步更新节点和连接记录
    if (config !== undefined) {
      // 删除现有的节点和连接记录
      await WorkflowNode.destroy({ where: { workflowId: templateId } });
      await WorkflowConnection.destroy({ where: { workflowId: templateId } });

      // 创建新的节点记录
      if (config.nodes) {
        for (const node of config.nodes) {
          await WorkflowNode.create({
            workflowId: templateId,
            nodeId: node.nodeId,
            nodeType: node.nodeType,
            name: node.name,
            description: node.description,
            positionX: node.positionX,
            positionY: node.positionY,
            config: node.config || {}
          });
        }
      }

      // 创建新的连接记录
      if (config.connections) {
        for (const connection of config.connections) {
          await WorkflowConnection.create({
            workflowId: templateId,
            sourceNodeId: connection.sourceNodeId,
            targetNodeId: connection.targetNodeId,
            sourceHandle: connection.sourceHandle,
            targetHandle: connection.targetHandle,
            conditionConfig: connection.conditionConfig || {}
          });
        }
      }
    }

    // 如果需要创建新版本
    if (createNewVersion && config) {
      // 获取当前最新版本号
      const latestVersion = await WorkflowVersion.findOne({
        where: { workflowId: templateId },
        order: [['createdAt', 'DESC']]
      });

      // 生成新版本号
      const currentVersion = latestVersion ? latestVersion.version : '1.0.0';
      const versionParts = currentVersion.split('.').map(Number);
      versionParts[2]++; // 增加修订号
      const newVersion = versionParts.join('.');

      // 将之前的版本设为非当前版本
      await WorkflowVersion.update(
        { isCurrent: false },
        { where: { workflowId: templateId } }
      );

      // 创建新版本
      await WorkflowVersion.create({
        workflowId: templateId,
        version: newVersion,
        config,
        changeLog: changeLog || '更新流程配置',
        isCurrent: true,
        createdBy: userId
      });

      res.json({
        message: '流程模板更新成功',
        version: newVersion
      });
    } else {
      res.json({ message: '流程模板更新成功' });
    }
  } catch (error) {
    console.error('更新流程模板失败:', error);
    res.status(500).json({ message: '更新流程模板失败', error: error.message });
  }
};

// 删除流程模板
exports.deleteWorkflowTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const templateId = req.params.id;

    // 构建查询条件：admin用户可以删除所有数据，普通用户只能删除自己创建的
    const whereCondition = { id: templateId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const template = await WorkflowTemplate.findOne({ where: whereCondition });
    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权删除' });
    }

    await template.destroy();

    res.json({ message: '流程模板删除成功' });
  } catch (error) {
    console.error('删除流程模板失败:', error);
    res.status(500).json({ message: '删除流程模板失败', error: error.message });
  }
};

// 获取流程版本历史
exports.getWorkflowVersions = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const workflowId = req.params.id;

    // 检查流程模板是否存在且有权限访问
    const whereCondition = { id: workflowId };
    if (userRole !== 'admin') {
      whereCondition[Op.or] = [
        { userId },
        { isPublic: true }
      ];
    }

    const template = await WorkflowTemplate.findOne({ where: whereCondition });
    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权访问' });
    }

    const versions = await WorkflowVersion.findAll({
      where: { workflowId },
      include: [
        {
          model: User,
          attributes: ['id', 'username']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json({
      data: versions
    });
  } catch (error) {
    console.error('获取版本历史失败:', error);
    res.status(500).json({ message: '获取版本历史失败', error: error.message });
  }
};

// 创建新版本
exports.createWorkflowVersion = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const workflowId = req.params.id;
    const { changeLog } = req.body;

    // 检查流程模板是否存在且有权限修改
    const whereCondition = { id: workflowId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const template = await WorkflowTemplate.findOne({ where: whereCondition });
    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权修改' });
    }

    // 获取当前最新版本号
    const latestVersion = await WorkflowVersion.findOne({
      where: { workflowId },
      order: [['createdAt', 'DESC']]
    });

    // 生成新版本号
    let newVersionNumber = '1.0.0';
    if (latestVersion) {
      const [major, minor, patch] = latestVersion.version.split('.').map(Number);
      newVersionNumber = `${major}.${minor}.${patch + 1}`;
    }

    // 将当前版本设为非当前版本
    await WorkflowVersion.update(
      { isCurrent: false },
      { where: { workflowId } }
    );

    // 创建新版本
    const newVersion = await WorkflowVersion.create({
      workflowId,
      version: newVersionNumber,
      config: template.config,
      changeLog: changeLog || '版本更新',
      isCurrent: true,
      createdBy: userId
    });

    // 更新模板版本号
    await template.update({ version: newVersionNumber });

    res.status(201).json({
      version: newVersionNumber,
      message: '新版本创建成功'
    });
  } catch (error) {
    console.error('创建版本失败:', error);
    res.status(500).json({ message: '创建版本失败', error: error.message });
  }
};

// 回滚到指定版本
exports.restoreWorkflowVersion = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const workflowId = req.params.id;
    const versionId = req.params.versionId;

    // 检查流程模板是否存在且有权限修改
    const whereCondition = { id: workflowId };
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    const template = await WorkflowTemplate.findOne({ where: whereCondition });
    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权修改' });
    }

    // 检查版本是否存在
    const version = await WorkflowVersion.findOne({
      where: { id: versionId, workflowId }
    });
    if (!version) {
      return res.status(404).json({ message: '版本不存在' });
    }

    // 将所有版本设为非当前版本
    await WorkflowVersion.update(
      { isCurrent: false },
      { where: { workflowId } }
    );

    // 设置目标版本为当前版本
    await version.update({ isCurrent: true });

    // 更新模板配置和版本号
    await template.update({
      config: version.config,
      version: version.version
    });

    // 同步更新节点和连接记录
    if (version.config) {
      // 删除现有的节点和连接记录
      await WorkflowNode.destroy({ where: { workflowId } });
      await WorkflowConnection.destroy({ where: { workflowId } });

      // 创建新的节点记录
      if (version.config.nodes) {
        for (const node of version.config.nodes) {
          await WorkflowNode.create({
            workflowId,
            nodeId: node.nodeId,
            nodeType: node.nodeType,
            name: node.name,
            description: node.description,
            positionX: node.positionX,
            positionY: node.positionY,
            config: node.config || {}
          });
        }
      }

      // 创建新的连接记录
      if (version.config.connections) {
        for (const connection of version.config.connections) {
          await WorkflowConnection.create({
            workflowId,
            sourceNodeId: connection.sourceNodeId,
            targetNodeId: connection.targetNodeId,
            sourceHandle: connection.sourceHandle,
            targetHandle: connection.targetHandle,
            conditionConfig: connection.conditionConfig || {}
          });
        }
      }
    }

    res.json({
      message: `已回滚到版本 ${version.version}`
    });
  } catch (error) {
    console.error('版本回滚失败:', error);
    res.status(500).json({ message: '版本回滚失败', error: error.message });
  }
};

// 导出流程模板
exports.exportWorkflowTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const templateId = req.params.id;

    // 检查流程模板是否存在且有权限访问
    const whereCondition = { id: templateId };
    if (userRole !== 'admin') {
      whereCondition[Op.or] = [
        { userId },
        { isPublic: true }
      ];
    }

    const template = await WorkflowTemplate.findOne({
      where: whereCondition,
      include: [
        {
          model: WorkflowNode,
          as: 'nodes'
        },
        {
          model: WorkflowConnection,
          as: 'connections'
        },
        {
          model: WorkflowVersion,
          as: 'versions',
          where: { isCurrent: true },
          required: false
        }
      ]
    });

    if (!template) {
      return res.status(404).json({ message: '流程模板不存在或无权访问' });
    }

    // 构建导出数据
    const exportData = {
      workflow: {
        name: template.name,
        description: template.description,
        category: template.category,
        version: template.version,
        config: template.config,
        tags: template.tags,
        nodes: template.nodes,
        connections: template.connections,
        versions: template.versions
      },
      exportedAt: new Date().toISOString(),
      exportVersion: '1.0',
      metadata: {
        originalId: template.id,
        originalUserId: template.userId,
        usageCount: template.usageCount,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt
      }
    };

    res.json(exportData);
  } catch (error) {
    console.error('导出流程模板失败:', error);
    res.status(500).json({ message: '导出流程模板失败', error: error.message });
  }
};

// 导入流程模板
exports.importWorkflowTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { workflow, name, makePublic = false } = req.body;

    if (!workflow) {
      return res.status(400).json({ message: '缺少流程配置数据' });
    }

    // 使用提供的名称或原始名称，并添加"导入"标识
    const templateName = name || `${workflow.name} - 导入`;

    // 创建新的流程模板
    const newTemplate = await WorkflowTemplate.create({
      name: templateName,
      description: workflow.description || '',
      category: workflow.category || '导入',
      version: '1.0.0',
      config: workflow.config || {},
      tags: [...(workflow.tags || []), '导入'],
      isPublic: makePublic,
      userId
    });

    // 创建节点记录
    if (workflow.nodes) {
      for (const node of workflow.nodes) {
        await WorkflowNode.create({
          workflowId: newTemplate.id,
          nodeId: node.nodeId,
          nodeType: node.nodeType,
          name: node.name,
          description: node.description,
          positionX: node.positionX,
          positionY: node.positionY,
          config: node.config || {}
        });
      }
    }

    // 创建连接记录
    if (workflow.connections) {
      for (const connection of workflow.connections) {
        await WorkflowConnection.create({
          workflowId: newTemplate.id,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          conditionConfig: connection.conditionConfig || {}
        });
      }
    }

    // 创建初始版本
    await WorkflowVersion.create({
      workflowId: newTemplate.id,
      version: '1.0.0',
      config: workflow.config || {},
      changeLog: '导入的初始版本',
      isCurrent: true,
      createdBy: userId
    });

    res.status(201).json({
      id: newTemplate.id,
      message: '流程导入成功'
    });
  } catch (error) {
    console.error('导入流程模板失败:', error);
    res.status(500).json({ message: '导入流程模板失败', error: error.message });
  }
};

// 获取流程统计分析
exports.getWorkflowStatistics = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;
    const { period = 'month', workflowId } = req.query;

    // 计算时间范围
    const now = new Date();
    let startDate;

    switch (period) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // 构建查询条件
    const whereCondition = {
      createdAt: {
        [Op.gte]: startDate
      }
    };

    // 权限控制
    if (userRole !== 'admin') {
      whereCondition.userId = userId;
    }

    if (workflowId) {
      whereCondition.workflowId = workflowId;
    }

    // 获取总执行次数
    const totalExecutions = await WorkflowExecution.count({
      where: whereCondition
    });

    // 获取成功率
    const successfulExecutions = await WorkflowExecution.count({
      where: {
        ...whereCondition,
        status: 'completed'
      }
    });

    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;

    // 获取平均执行时间
    const completedExecutions = await WorkflowExecution.findAll({
      where: {
        ...whereCondition,
        status: 'completed',
        completedAt: { [Op.not]: null }
      },
      attributes: ['startedAt', 'completedAt']
    });

    let averageExecutionTime = 0;
    if (completedExecutions.length > 0) {
      const totalTime = completedExecutions.reduce((sum, execution) => {
        const duration = new Date(execution.completedAt) - new Date(execution.startedAt);
        return sum + duration;
      }, 0);
      averageExecutionTime = totalTime / completedExecutions.length;
    }

    // 获取热门流程 - 使用子查询避免GROUP BY问题
    const popularWorkflows = await WorkflowTemplate.findAll({
      attributes: [
        'id', 'name', 'category', 'usageCount'
      ],
      order: [['usageCount', 'DESC']],
      limit: 10
    });

    // 为每个流程计算最近执行次数
    for (const workflow of popularWorkflows) {
      const recentCount = await WorkflowExecution.count({
        where: {
          workflowId: workflow.id,
          createdAt: {
            [Op.gte]: startDate
          },
          ...(userRole !== 'admin' ? { userId } : {})
        }
      });
      workflow.dataValues.recentExecutions = recentCount;
    }

    // 获取执行趋势数据
    const executionTrends = await WorkflowExecution.findAll({
      where: whereCondition,
      attributes: [
        [dbInstance.fn('DATE', dbInstance.col('createdAt')), 'date'],
        [dbInstance.fn('COUNT', '*'), 'count'],
        [dbInstance.fn('SUM', dbInstance.literal("CASE WHEN status = 'completed' THEN 1 ELSE 0 END")), 'successful']
      ],
      group: [dbInstance.fn('DATE', dbInstance.col('createdAt'))],
      order: [[dbInstance.fn('DATE', dbInstance.col('createdAt')), 'ASC']]
    });

    // 获取节点性能数据
    const nodePerformance = await WorkflowNodeExecution.findAll({
      where: {
        createdAt: {
          [Op.gte]: startDate
        },
        status: 'completed',
        executionTimeMs: { [Op.not]: null }
      },
      attributes: [
        'nodeId',
        [dbInstance.fn('COUNT', '*'), 'executionCount'],
        [dbInstance.fn('AVG', dbInstance.col('executionTimeMs')), 'averageTime'],
        [dbInstance.fn('MIN', dbInstance.col('executionTimeMs')), 'minTime'],
        [dbInstance.fn('MAX', dbInstance.col('executionTimeMs')), 'maxTime']
      ],
      group: ['nodeId'],
      order: [[dbInstance.literal('averageTime'), 'DESC']],
      limit: 20
    });

    res.json({
      totalExecutions,
      successRate: Math.round(successRate * 100) / 100,
      averageExecutionTime: Math.round(averageExecutionTime),
      popularWorkflows,
      executionTrends,
      nodePerformance,
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString()
      }
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({ message: '获取统计数据失败', error: error.message });
  }
};

// 获取流程分类列表
exports.getWorkflowCategories = async (req, res) => {
  try {
    const categories = await WorkflowCategory.findAll({
      where: { isActive: true },
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });

    // 统计每个分类的流程数量
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const count = await WorkflowTemplate.count({
          where: { 
            category: category.name,
            isActive: true
          }
        });
        
        return {
          ...category.toJSON(),
          workflowCount: count
        };
      })
    );

    res.json({ data: categoriesWithCount });
  } catch (error) {
    console.error('获取流程分类列表失败:', error);
    res.status(500).json({ message: '获取流程分类列表失败', error: error.message });
  }
};
