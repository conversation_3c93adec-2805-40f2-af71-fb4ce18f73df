const { Outline, Project } = require('../models');

// 根据项目ID获取大纲
exports.getOutlinesByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 检查项目是否存在且用户有权访问
    const projectWhereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };
    const project = await Project.findOne({ where: projectWhereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    // 构建大纲查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的数据
    const outlineWhereCondition = userRole === 'admin' ? { projectId } : { projectId, userId };

    const outlines = await Outline.findAll({
      where: outlineWhereCondition,
      order: [['level', 'ASC'], ['order', 'ASC']]
    });

    res.json(outlines);
  } catch (error) {
    console.error('获取大纲列表失败:', error);
    res.status(500).json({ message: '获取大纲列表失败', error: error.message });
  }
};

// 根据ID获取大纲
exports.getOutlineById = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以看到所有数据，普通用户只能看到自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const outline = await Outline.findOne({
      where: whereCondition,
      include: [
        { model: Outline, as: 'children' }
      ]
    });

    if (!outline) {
      return res.status(404).json({ message: '大纲不存在或无权访问' });
    }

    res.json(outline);
  } catch (error) {
    console.error('获取大纲失败:', error);
    res.status(500).json({ message: '获取大纲失败', error: error.message });
  }
};

// 创建大纲
exports.createOutline = async (req, res) => {
  try {
    const { projectId, title, level, parentId, content, order } = req.body;
    const userId = req.user.id; // 获取当前登录用户ID
    const userRole = req.user.role;

    if (!projectId || !title || !level) {
      return res.status(400).json({ message: '项目ID、标题和层级不能为空' });
    }

    // 检查项目是否存在且用户有权访问
    const projectWhereCondition = userRole === 'admin' ? { id: projectId } : { id: projectId, userId };
    const project = await Project.findOne({ where: projectWhereCondition });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    // 如果有父级大纲，检查父级大纲是否存在且用户有权访问
    if (parentId) {
      const parentWhereCondition = userRole === 'admin' ? { id: parentId } : { id: parentId, userId };
      const parentOutline = await Outline.findOne({ where: parentWhereCondition });
      if (!parentOutline) {
        return res.status(404).json({ message: '父级大纲不存在或无权访问' });
      }
    }

    const outline = await Outline.create({
      projectId,
      title,
      level,
      parentId,
      content,
      order: order || 0,
      userId // 关联当前用户
    });

    res.status(201).json(outline);
  } catch (error) {
    console.error('创建大纲失败:', error);
    res.status(500).json({ message: '创建大纲失败', error: error.message });
  }
};

// 更新大纲
exports.updateOutline = async (req, res) => {
  try {
    const { title, level, parentId, content, order } = req.body;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以修改所有数据，普通用户只能修改自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const outline = await Outline.findOne({ where: whereCondition });
    if (!outline) {
      return res.status(404).json({ message: '大纲不存在或无权修改' });
    }

    // 如果有父级大纲，检查父级大纲是否存在且用户有权访问
    if (parentId && parentId !== outline.parentId) {
      const parentWhereCondition = userRole === 'admin' ? { id: parentId } : { id: parentId, userId };
      const parentOutline = await Outline.findOne({ where: parentWhereCondition });
      if (!parentOutline) {
        return res.status(404).json({ message: '父级大纲不存在或无权访问' });
      }
    }

    await outline.update({
      title,
      level,
      parentId,
      content,
      order
    });

    res.json(outline);
  } catch (error) {
    console.error('更新大纲失败:', error);
    res.status(500).json({ message: '更新大纲失败', error: error.message });
  }
};

// 删除大纲
exports.deleteOutline = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // 构建查询条件：admin用户可以删除所有数据，普通用户只能删除自己创建的数据
    const whereCondition = userRole === 'admin' ? { id: req.params.id } : { id: req.params.id, userId };

    const outline = await Outline.findOne({ where: whereCondition });
    if (!outline) {
      return res.status(404).json({ message: '大纲不存在或无权删除' });
    }

    await outline.destroy();
    res.json({ message: '大纲删除成功' });
  } catch (error) {
    console.error('删除大纲失败:', error);
    res.status(500).json({ message: '删除大纲失败', error: error.message });
  }
};
