const { VolumeChapterGroupVersion, VolumeChapterGroup } = require('../models');

// 获取某个章节分组的所有版本
exports.getChapterGroupVersions = async (req, res) => {
  try {
    const { chapterGroupId } = req.params;
    const chapterGroup = await VolumeChapterGroup.findByPk(chapterGroupId);
    if (!chapterGroup) {
      return res.status(404).json({ message: '章节分组不存在' });
    }
    const versions = await VolumeChapterGroupVersion.findAll({
      where: { chapterGroupId },
      order: [['versionNumber', 'DESC']], // 按版本号降序排列
    });
    res.status(200).json(versions);
  } catch (error) {
    console.error('获取章节分组版本列表失败:', error);
    res.status(500).json({ message: '获取章节分组版本列表失败', error: error.message });
  }
};

// 获取特定章节分组的特定版本内容
exports.getChapterGroupVersion = async (req, res) => {
  try {
    const { chapterGroupId, versionId } = req.params;
    const version = await VolumeChapterGroupVersion.findOne({
      where: { id: versionId, chapterGroupId },
    });
    if (!version) {
      return res.status(404).json({ message: '章节分组版本不存在' });
    }
    res.status(200).json(version);
  } catch (error) {
    console.error('获取章节分组版本内容失败:', error);
    res.status(500).json({ message: '获取章节分组版本内容失败', error: error.message });
  }
};

// 创建新的章节分组版本 (通常在更新章节分组内容时自动创建)
exports.createChapterGroupVersion = async (req, res) => {
  try {
    const { chapterGroupId } = req.params;
    const { title, chapterRange, summary, progressRate } = req.body;

    const chapterGroup = await VolumeChapterGroup.findByPk(chapterGroupId);
    if (!chapterGroup) {
      return res.status(404).json({ message: '章节分组不存在' });
    }

    // 获取当前最大版本号
    const latestVersion = await VolumeChapterGroupVersion.findOne({
      where: { chapterGroupId },
      order: [['versionNumber', 'DESC']],
    });

    const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

    const newVersion = await VolumeChapterGroupVersion.create({
      chapterGroupId,
      title: title || chapterGroup.title,
      chapterRange: chapterRange || chapterGroup.chapterRange,
      summary: summary || chapterGroup.summary,
      progressRate: progressRate !== undefined ? progressRate : chapterGroup.progressRate,
      versionNumber: nextVersionNumber,
    });
    res.status(201).json(newVersion);
  } catch (error) {
    console.error('创建章节分组版本失败:', error);
    res.status(500).json({ message: '创建章节分组版本失败', error: error.message });
  }
};
