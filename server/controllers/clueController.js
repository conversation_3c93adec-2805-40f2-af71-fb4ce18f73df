const { Clue, Project, Chapter } = require('../models');

// 根据项目ID获取线索
exports.getCluesByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    
    // 检查项目是否存在
    const project = await Project.findByPk(projectId);
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    const clues = await Clue.findAll({
      where: { projectId },
      include: [
        { 
          model: Chapter, 
          as: 'firstAppearChapter',
          attributes: ['id', 'title'] 
        }
      ]
    });
    
    res.json(clues);
  } catch (error) {
    console.error('获取线索列表失败:', error);
    res.status(500).json({ message: '获取线索列表失败', error: error.message });
  }
};

// 根据ID获取线索
exports.getClueById = async (req, res) => {
  try {
    const clue = await Clue.findByPk(req.params.id, {
      include: [
        { 
          model: Chapter, 
          as: 'firstAppearChapter',
          attributes: ['id', 'title'] 
        }
      ]
    });
    
    if (!clue) {
      return res.status(404).json({ message: '线索不存在' });
    }
    
    res.json(clue);
  } catch (error) {
    console.error('获取线索失败:', error);
    res.status(500).json({ message: '获取线索失败', error: error.message });
  }
};

// 创建线索
exports.createClue = async (req, res) => {
  try {
    const { projectId, name, type, description, firstAppearChapterId } = req.body;
    
    if (!projectId || !name || !type) {
      return res.status(400).json({ message: '项目ID、线索名称和线索类型不能为空' });
    }
    
    // 检查项目是否存在
    const project = await Project.findByPk(projectId);
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    // 如果提供了章节ID，检查章节是否存在
    if (firstAppearChapterId) {
      const chapter = await Chapter.findByPk(firstAppearChapterId);
      if (!chapter) {
        return res.status(404).json({ message: '章节不存在' });
      }
    }
    
    const clue = await Clue.create({
      projectId,
      name,
      type,
      description,
      firstAppearChapterId
    });
    
    res.status(201).json(clue);
  } catch (error) {
    console.error('创建线索失败:', error);
    res.status(500).json({ message: '创建线索失败', error: error.message });
  }
};

// 更新线索
exports.updateClue = async (req, res) => {
  try {
    const { name, type, description, firstAppearChapterId } = req.body;
    
    const clue = await Clue.findByPk(req.params.id);
    if (!clue) {
      return res.status(404).json({ message: '线索不存在' });
    }
    
    // 如果提供了章节ID，检查章节是否存在
    if (firstAppearChapterId) {
      const chapter = await Chapter.findByPk(firstAppearChapterId);
      if (!chapter) {
        return res.status(404).json({ message: '章节不存在' });
      }
    }
    
    await clue.update({
      name,
      type,
      description,
      firstAppearChapterId
    });
    
    res.json(clue);
  } catch (error) {
    console.error('更新线索失败:', error);
    res.status(500).json({ message: '更新线索失败', error: error.message });
  }
};

// 删除线索
exports.deleteClue = async (req, res) => {
  try {
    const clue = await Clue.findByPk(req.params.id);
    if (!clue) {
      return res.status(404).json({ message: '线索不存在' });
    }
    
    await clue.destroy();
    res.json({ message: '线索删除成功' });
  } catch (error) {
    console.error('删除线索失败:', error);
    res.status(500).json({ message: '删除线索失败', error: error.message });
  }
};
