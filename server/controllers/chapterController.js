const { Chapter, Project, Outline, ChapterVersion} = require('../models');
const { getProjectRelatedData, checkDataAccess, createProjectRelatedData, updateData, deleteData } = require('../utils/permissionHelper');

// 根据项目ID获取章节
exports.getChaptersByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    const user = req.user;

    const { data: chapters } = await getProjectRelatedData(
      Chapter,
      Project,
      projectId,
      user,
      {
        order: [['order', 'ASC']],
        include: [{ model: Outline }]
      }
    );

    res.json(chapters);
  } catch (error) {
    console.error('获取章节列表失败:', error);
    if (error.message === '项目不存在或无权访问') {
      return res.status(404).json({ message: error.message });
    }
    res.status(500).json({ message: '获取章节列表失败', error: error.message });
  }
};

// 根据ID获取章节
exports.getChapterById = async (req, res) => {
  try {
    const user = req.user;

    const chapter = await checkDataAccess(Chapter, req.params.id, user, [{ model: Outline }]);

    if (!chapter) {
      return res.status(404).json({ message: '章节不存在或无权访问' });
    }

    res.json(chapter);
  } catch (error) {
    console.error('获取章节失败:', error);
    res.status(500).json({ message: '获取章节失败', error: error.message });
  }
};

// 创建章节
exports.createChapter = async (req, res) => {
  try {
    const { projectId, title, content, outlineId } = req.body;
    const user = req.user;

    if (!projectId || !title) {
      return res.status(400).json({ message: '项目ID和章节标题不能为空' });
    }

    // 如果提供了大纲ID，检查大纲是否存在且用户有权访问
    if (outlineId) {
      const outline = await checkDataAccess(Outline, outlineId, user);
      if (!outline) {
        return res.status(404).json({ message: '大纲不存在或无权访问' });
      }
    }

    const chapter = await createProjectRelatedData(Chapter, Project, {
      projectId,
      title,
      content: content || '',
      outlineId,
      order: req.body.order || 0
    }, user);

    // 创建第一个版本
    const chapterVersion = await ChapterVersion.create({
      chapterId: chapter.id,
      versionNumber: 1,
      content: chapter.content,
      createdAt: new Date()
    });

    res.status(201).json(chapter);
  } catch (error) {
    console.error('创建章节失败:', error);
    if (error.message === '项目不存在或无权访问') {
      return res.status(404).json({ message: error.message });
    }
    res.status(500).json({ message: '创建章节失败', error: error.message });
  }
};

// 更新章节
exports.updateChapter = async (req, res) => {
  try {
    const { outlineId, title, content, order, status } = req.body;
    
    const chapter = await Chapter.findByPk(req.params.id);
    if (!chapter) {
      return res.status(404).json({ message: '章节不存在' });
    }
    
    // 如果有大纲ID，检查大纲是否存在
    if (outlineId && outlineId !== chapter.outlineId) {
      const outline = await Outline.findByPk(outlineId);
      if (!outline) {
        return res.status(404).json({ message: '大纲不存在' });
      }
    }
    
    await chapter.update({
      outlineId,
      title,
      content,
      order: req.body.order !== undefined ? req.body.order : chapter.order, // 确保保存章节序号
      status
    });
    
    res.json(chapter);
  } catch (error) {
    console.error('更新章节失败:', error);
    res.status(500).json({ message: '更新章节失败', error: error.message });
  }
};

// 删除章节
exports.deleteChapter = async (req, res) => {
  try {
    const chapter = await Chapter.findByPk(req.params.id);
    if (!chapter) {
      return res.status(404).json({ message: '章节不存在' });
    }
    
    await chapter.destroy();
    res.json({ message: '章节删除成功' });
  } catch (error) {
    console.error('删除章节失败:', error);
    res.status(500).json({ message: '删除章节失败', error: error.message });
  }
};
