const { Volume, Project, VolumeChapterGroup, VolumeVersion } = require('../models');

// 根据项目ID获取分卷
exports.getVolumesByProject = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    const userId = req.user.id;

    // 检查项目是否存在且用户有权访问
    const project = await Project.findOne({
      where: {
        id: projectId,
        userId: userId
      }
    });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    const volumes = await Volume.findAll({
      where: {
        projectId,
        userId: userId // 只获取用户自己的分卷
      },
      order: [['order', 'ASC']],
      include: [{ model: VolumeChapterGroup }]
    });

    res.json(volumes);
  } catch (error) {
    console.error('获取分卷列表失败:', error);
    res.status(500).json({ message: '获取分卷列表失败', error: error.message });
  }
};

// 根据ID获取分卷
exports.getVolumeById = async (req, res) => {
  try {
    const userId = req.user.id;

    const volume = await Volume.findOne({
      where: {
        id: req.params.id,
        userId: userId // 确保用户只能访问自己的分卷
      },
      include: [{ model: VolumeChapterGroup }]
    });

    if (!volume) {
      return res.status(404).json({ message: '分卷不存在或无权访问' });
    }

    res.json(volume);
  } catch (error) {
    console.error('获取分卷失败:', error);
    res.status(500).json({ message: '获取分卷失败', error: error.message });
  }
};

// 创建分卷
exports.createVolume = async (req, res) => {
  try {
    const { projectId, title, summary, wordCount, chapterCount, order, writingRequirements } = req.body;
    const userId = req.user.id; // 获取当前登录用户ID

    if (!projectId || !title) {
      return res.status(400).json({ message: '项目ID和分卷标题不能为空' });
    }

    // 检查项目是否存在且用户有权访问
    const project = await Project.findOne({
      where: {
        id: projectId,
        userId: userId // 确保用户只能在自己的项目中创建分卷
      }
    });
    if (!project) {
      return res.status(404).json({ message: '项目不存在或无权访问' });
    }

    const volume = await Volume.create({
      projectId,
      title,
      summary,
      wordCount: wordCount || 0,
      chapterCount: chapterCount || 0,
      order: order || 0,
      writingRequirements: writingRequirements || '',
      userId: userId // 设置用户ID
    });

    res.status(201).json(volume);
  } catch (error) {
    console.error('创建分卷失败:', error);
    res.status(500).json({ message: '创建分卷失败', error: error.message });
  }
};

// 更新分卷
exports.updateVolume = async (req, res) => {
  try {
    const { title, summary, wordCount, chapterCount, order, writingRequirements } = req.body;
    const userId = req.user.id;

    const volume = await Volume.findOne({
      where: {
        id: req.params.id,
        userId: userId // 确保用户只能更新自己的分卷
      }
    });
    if (!volume) {
      return res.status(404).json({ message: '分卷不存在或无权访问' });
    }

    // 保存旧数据以便创建版本
    const oldData = {
      title: volume.title,
      summary: volume.summary,
      wordCount: volume.wordCount,
      chapterCount: volume.chapterCount,
      writingRequirements: volume.writingRequirements
    };

    // 更新分卷
    await volume.update({
      title,
      summary,
      wordCount,
      chapterCount,
      order,
      writingRequirements: writingRequirements || ''
    });

    // 如果关键字段发生变化，创建新版本
    if (oldData.title !== title || oldData.summary !== summary ||
        oldData.wordCount !== wordCount || oldData.chapterCount !== chapterCount ||
        oldData.writingRequirements !== writingRequirements) {
      try {
        // 获取当前最大版本号
        const latestVersion = await VolumeVersion.findOne({
          where: { volumeId: volume.id },
          order: [['versionNumber', 'DESC']],
        });

        const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

        // 创建新版本
        await VolumeVersion.create({
          volumeId: volume.id,
          title,
          summary,
          wordCount,
          chapterCount,
          writingRequirements,
          versionNumber: nextVersionNumber,
        });
      } catch (versionError) {
        console.error('创建分卷版本失败:', versionError);
        // 不影响主流程，只记录错误
      }
    }

    res.json(volume);
  } catch (error) {
    console.error('更新分卷失败:', error);
    res.status(500).json({ message: '更新分卷失败', error: error.message });
  }
};

// 删除分卷
exports.deleteVolume = async (req, res) => {
  try {
    const userId = req.user.id;

    const volume = await Volume.findOne({
      where: {
        id: req.params.id,
        userId: userId // 确保用户只能删除自己的分卷
      }
    });
    if (!volume) {
      return res.status(404).json({ message: '分卷不存在或无权访问' });
    }

    await volume.destroy();
    res.json({ message: '分卷删除成功' });
  } catch (error) {
    console.error('删除分卷失败:', error);
    res.status(500).json({ message: '删除分卷失败', error: error.message });
  }
};
