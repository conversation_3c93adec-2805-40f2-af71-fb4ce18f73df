const { EditorComment, Chapter } = require('../models');

// 获取某个章节的所有编辑意见
exports.getChapterEditorComments = async (req, res) => {
  try {
    const { chapterId } = req.params;
    const chapter = await Chapter.findByPk(chapterId);
    if (!chapter) {
      return res.status(404).json({ message: 'Chapter not found' });
    }
    const comments = await EditorComment.findAll({
      where: { chapterId },
      order: [['createdAt', 'DESC']],
    });
    res.status(200).json(comments);
  } catch (error) {
    console.error('Error fetching editor comments:', error);
    res.status(500).json({ message: 'Error fetching editor comments', error: error.message });
  }
};

// 为某个章节添加新的编辑意见
exports.addEditorComment = async (req, res) => {
  try {
    const { chapterId } = req.params;
    const { content, editorName } = req.body; // editorName 可以从认证用户获取，或前端传递

    const chapter = await Chapter.findByPk(chapterId);
    if (!chapter) {
      return res.status(404).json({ message: 'Chapter not found' });
    }

    if (!content || content.trim() === '') {
        return res.status(400).json({ message: 'Comment content cannot be empty' });
    }

    const newComment = await EditorComment.create({
      chapterId,
      content,
      editorName: editorName || '匿名编辑', // 默认值或从用户会话获取
    });
    res.status(201).json(newComment);
  } catch (error) {
    console.error('Error adding editor comment:', error);
    res.status(500).json({ message: 'Error adding editor comment', error: error.message });
  }
};