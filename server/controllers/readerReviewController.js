const { ReaderReview, Chapter } = require('../models');

// 获取某个章节的所有读者评价
exports.getChapterReaderReviews = async (req, res) => {
  try {
    const { chapterId } = req.params;
    const chapter = await Chapter.findByPk(chapterId);
    if (!chapter) {
      return res.status(404).json({ message: 'Chapter not found' });
    }
    const reviews = await ReaderReview.findAll({
      where: { chapterId },
      order: [['createdAt', 'DESC']],
    });
    res.status(200).json(reviews);
  } catch (error) {
    console.error('Error fetching reader reviews:', error);
    res.status(500).json({ message: 'Error fetching reader reviews', error: error.message });
  }
};

// 为某个章节添加新的读者评价
exports.addReaderReview = async (req, res) => {
  try {
    const { chapterId } = req.params;
    const { content, rating, readerName } = req.body; // readerName 可以从认证用户获取，或前端传递

    const chapter = await Chapter.findByPk(chapterId);
    if (!chapter) {
      return res.status(404).json({ message: 'Chapter not found' });
    }

    if (!content || content.trim() === '') {
        return res.status(400).json({ message: 'Review content cannot be empty' });
    }
    if (rating === undefined || rating === null || rating < 1 || rating > 5) {
        return res.status(400).json({ message: 'Rating must be between 1 and 5' });
    }

    const newReview = await ReaderReview.create({
      chapterId,
      content,
      rating,
      readerName: readerName || '匿名读者', // 默认值或从用户会话获取
    });
    res.status(201).json(newReview);
  } catch (error) {
    console.error('Error adding reader review:', error);
    res.status(500).json({ message: 'Error adding reader review', error: error.message });
  }
};