const { User } = require('../models');
const { buildWhereCondition } = require('../utils/permissionHelper');

// 获取所有用户（仅admin可访问）
exports.getAllUsers = async (req, res) => {
  try {
    // 获取所有用户，排除密码字段
    const users = await User.findAll({
      attributes: { exclude: ['password'] },
      order: [['createdAt', 'DESC']]
    });

    res.json({
      message: '获取用户列表成功',
      users
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ message: '获取用户列表失败', error: error.message });
  }
};

// 根据ID获取用户（仅admin可访问）
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    res.json({
      message: '获取用户信息成功',
      user
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({ message: '获取用户信息失败', error: error.message });
  }
};

// 创建用户（仅admin可访问）
exports.createUser = async (req, res) => {
  try {
    const { username, password, email, role = 'user' } = req.body;

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码不能为空' });
    }

    // 验证角色
    if (!['user', 'admin'].includes(role)) {
      return res.status(400).json({ message: '无效的用户角色' });
    }

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ where: { username } });
    if (existingUser) {
      return res.status(400).json({ message: '用户名已存在' });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({ message: '邮箱已被使用' });
      }
    }

    // 创建新用户
    const user = await User.create({
      username,
      password, // 密码会在模型中自动哈希
      email,
      role
    });

    // 返回用户信息（不包括密码）
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(201).json({
      message: '用户创建成功',
      user: userResponse
    });
  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({ message: '创建用户失败', error: error.message });
  }
};

// 更新用户（仅admin可访问）
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, role, password } = req.body;

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 验证角色（如果提供）
    if (role && !['user', 'admin'].includes(role)) {
      return res.status(400).json({ message: '无效的用户角色' });
    }

    // 检查用户名是否已被其他用户使用
    if (username && username !== user.username) {
      const existingUser = await User.findOne({ where: { username } });
      if (existingUser) {
        return res.status(400).json({ message: '用户名已存在' });
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({ message: '邮箱已被使用' });
      }
    }

    // 构建更新数据
    const updateData = {};
    if (username) updateData.username = username;
    if (email !== undefined) updateData.email = email; // 允许设置为null
    if (role) updateData.role = role;
    if (password) updateData.password = password; // 密码会在模型中自动哈希

    // 更新用户
    await user.update(updateData);

    // 返回更新后的用户信息（不包括密码）
    const updatedUser = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    res.json({
      message: '用户更新成功',
      user: updatedUser
    });
  } catch (error) {
    console.error('更新用户失败:', error);
    res.status(500).json({ message: '更新用户失败', error: error.message });
  }
};

// 删除用户（仅admin可访问）
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUserId = req.user.id;

    // 防止删除自己
    if (parseInt(id) === currentUserId) {
      return res.status(400).json({ message: '不能删除自己的账号' });
    }

    // 查找用户
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 删除用户
    await user.destroy();

    res.json({
      message: '用户删除成功'
    });
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({ message: '删除用户失败', error: error.message });
  }
};
