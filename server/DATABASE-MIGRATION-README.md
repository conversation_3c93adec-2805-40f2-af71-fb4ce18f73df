# 数据库迁移指南：SQLite 到 MySQL

本文档提供了将应用程序数据库从SQLite迁移到MySQL的详细步骤，以及如何在两种数据库之间切换。

## 前提条件

1. 已安装MySQL服务器
2. 已安装MySQL客户端工具
3. 已安装Node.js和npm

## 迁移到MySQL

### 自动迁移

我们提供了一个自动化脚本来处理整个迁移过程：

```bash
cd server
npm run db:to-mysql
```

这个命令会执行以下操作：
1. 从SQLite数据库导出表结构和数据
2. 将SQLite的SQL语句转换为MySQL兼容的格式
3. 创建MySQL数据库（如果不存在）
4. 导入表结构和数据到MySQL
5. 设置环境变量，将应用程序切换到使用MySQL

### 手动迁移

如果自动迁移失败，您可以按照以下步骤手动完成迁移：

1. 生成迁移SQL脚本：

```bash
cd server
node scripts/sqlite-to-mysql.js
```

2. 创建MySQL数据库：

```sql
CREATE DATABASE xiaoshuo2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 导入迁移SQL脚本：

```bash
mysql -h cd-cdb-8yjrt2ao.sql.tencentcdb.com -P 27134 -u root -pZjy@2025 xiaoshuo2 < database/sqlite-to-mysql.sql
```

4. 设置环境变量：

```bash
# Windows
set DB_DIALECT=mysql

# Linux/Mac
export DB_DIALECT=mysql
```

## 切换回SQLite

如果需要切换回SQLite数据库，可以使用以下命令：

```bash
cd server
npm run db:to-sqlite
```

这个命令会执行以下操作：
1. 设置环境变量，将应用程序切换回使用SQLite
2. 检查SQLite数据库文件是否存在

## 数据库配置

数据库配置位于 `server/config/database.js` 文件中。应用程序会根据环境变量 `DB_DIALECT` 来决定使用哪种数据库：

- 如果 `DB_DIALECT=mysql`，则使用MySQL配置
- 否则，默认使用SQLite配置

MySQL配置参数：
- 主机：cd-cdb-8yjrt2ao.sql.tencentcdb.com
- 端口：27134
- 用户名：root
- 密码：Zjy@2025
- 数据库名：xiaoshuo2

## 故障排除

### MySQL连接问题

如果无法连接到MySQL数据库，请检查：
1. MySQL服务器是否正在运行
2. 连接参数（主机、端口、用户名、密码）是否正确
3. 数据库是否存在
4. 用户是否有权限访问该数据库

### 数据迁移问题

如果数据迁移过程中出现错误：
1. 检查迁移SQL脚本是否生成成功
2. 尝试手动执行SQL脚本，查看具体错误信息
3. 确保MySQL版本兼容（推荐使用MySQL 5.7+）

### 应用程序连接问题

如果应用程序无法连接到数据库：
1. 确认环境变量 `DB_DIALECT` 设置正确
2. 重启应用程序以加载新的配置
3. 检查应用程序日志中的错误信息

## 数据类型兼容性

SQLite和MySQL之间存在一些数据类型差异，迁移脚本已处理以下转换：

- `INTEGER PRIMARY KEY AUTOINCREMENT` → `INTEGER PRIMARY KEY AUTO_INCREMENT`
- `TINYINT(1)` → `BOOLEAN`
- `DATETIME` → `TIMESTAMP`

## 注意事项

1. 迁移过程不会影响原有的SQLite数据库文件
2. 建议在迁移前备份SQLite数据库文件
3. 迁移后，对MySQL数据库的更改不会自动同步到SQLite数据库，反之亦然
