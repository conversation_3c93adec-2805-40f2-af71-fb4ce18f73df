const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
const config = require('./config/config.json');

// 获取当前环境的配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 创建 Sequelize 实例
const sequelize = new Sequelize({
  dialect: dbConfig.dialect,
  storage: dbConfig.storage, // 对于 SQLite
  logging: console.log
});

// 运行迁移
async function runMigration() {
  try {
    // 导入 Umzug 和 SequelizeStorage
    const { Umzug, SequelizeStorage } = require('umzug');

    // 创建 Umzug 实例
    const umzug = new Umzug({
      migrations: {
        path: path.join(__dirname, './migrations'),
        pattern: /\.js$/,
        params: [
          sequelize.getQueryInterface(),
          Sequelize
        ]
      },
      storage: new SequelizeStorage({ sequelize }),
      logger: console
    });

    // 运行迁移
    const migrations = await umzug.up();
    console.log('迁移完成:', migrations.map(m => m.name));
  } catch (error) {
    console.error('迁移失败:', error);
  } finally {
    await sequelize.close();
  }
}

runMigration();
