-- MySQL版本的表创建脚本

-- 创建 chapter_versions 表（如果不存在）
CREATE TABLE IF NOT EXISTS chapter_versions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  chapterId INT NOT NULL,
  versionNumber INT NOT NULL,
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 editor_comments 表（如果不存在）
CREATE TABLE IF NOT EXISTS editor_comments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  chapterId INT NOT NULL,
  editorName VARCHAR(255),
  content TEXT NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 reader_reviews 表（如果不存在）
CREATE TABLE IF NOT EXISTS reader_reviews (
  id INT AUTO_INCREMENT PRIMARY KEY,
  chapterId INT NOT NULL,
  readerName VARCHAR(255),
  content TEXT NOT NULL,
  rating INT NOT NULL CHECK (rating BETWEEN 1 AND 5),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (chapterId) REFERENCES Chapters(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 clues 表（如果不存在）
CREATE TABLE IF NOT EXISTS clues (
  id INT AUTO_INCREMENT PRIMARY KEY,
  projectId INT NOT NULL,
  name VARCHAR(255) NOT NULL,
  type ENUM('重大事件', '重要道具', '重要伏笔') NOT NULL,
  description TEXT,
  firstAppearChapterId INT,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (projectId) REFERENCES Projects(id) ON DELETE CASCADE,
  FOREIGN KEY (firstAppearChapterId) REFERENCES Chapters(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 Volumes 表（如果不存在）
CREATE TABLE IF NOT EXISTS Volumes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  projectId INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  summary TEXT NOT NULL,
  wordCount INT DEFAULT 0,
  chapterCount INT DEFAULT 0,
  `order` INT DEFAULT 0,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (projectId) REFERENCES Projects(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 VolumeChapterGroups 表（如果不存在）
CREATE TABLE IF NOT EXISTS VolumeChapterGroups (
  id INT AUTO_INCREMENT PRIMARY KEY,
  volumeId INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  chapterRange VARCHAR(255) NOT NULL,
  summary TEXT NOT NULL,
  progressRate INT DEFAULT 0,
  `order` INT DEFAULT 0,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (volumeId) REFERENCES Volumes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 VolumeChapterGroupVersions 表（如果不存在）
CREATE TABLE IF NOT EXISTS VolumeChapterGroupVersions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  volumeChapterGroupId INT NOT NULL,
  versionNumber INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  chapterRange VARCHAR(255) NOT NULL,
  summary TEXT NOT NULL,
  progressRate INT DEFAULT 0,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (volumeChapterGroupId) REFERENCES VolumeChapterGroups(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 VolumeVersions 表（如果不存在）
CREATE TABLE IF NOT EXISTS VolumeVersions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  volumeId INT NOT NULL,
  versionNumber INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  summary TEXT NOT NULL,
  wordCount INT DEFAULT 0,
  chapterCount INT DEFAULT 0,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  FOREIGN KEY (volumeId) REFERENCES Volumes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建 Users 表（如果不存在）
CREATE TABLE IF NOT EXISTS Users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  role VARCHAR(50) DEFAULT 'user',
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
