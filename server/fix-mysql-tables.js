/**
 * 修复MySQL数据库缺少的表
 * 
 * 此脚本用于：
 * 1. 连接到MySQL数据库
 * 2. 执行SQL脚本创建缺少的表
 * 3. 处理MySQL特定的语法和配置
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const config = require('./config/database');

// 获取当前环境的配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// MySQL连接配置
const mysqlConfig = {
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  multipleStatements: true // 允许执行多条SQL语句
};

// 读取MySQL表创建脚本
const sqlFilePath = path.join(__dirname, 'create-mysql-tables.sql');

async function fixMySQLTables() {
  let connection;
  
  try {
    console.log('正在连接到MySQL数据库...');
    console.log('配置:', {
      host: mysqlConfig.host,
      port: mysqlConfig.port,
      database: mysqlConfig.database,
      user: mysqlConfig.user
    });

    // 创建数据库连接
    connection = await mysql.createConnection(mysqlConfig);
    console.log('MySQL数据库连接成功');

    // 读取SQL脚本
    console.log('正在读取SQL脚本...');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // 执行SQL脚本
    console.log('正在执行SQL脚本创建缺少的表...');
    await connection.execute(sqlContent);
    
    console.log('✅ 成功创建所有缺少的表！');
    console.log('已创建的表包括：');
    console.log('- chapter_versions (章节版本)');
    console.log('- editor_comments (编辑评论)');
    console.log('- reader_reviews (读者评价)');
    console.log('- clues (线索管理)');
    console.log('- Volumes (分卷)');
    console.log('- VolumeChapterGroups (章节分组)');
    console.log('- VolumeChapterGroupVersions (章节分组版本)');
    console.log('- VolumeVersions (分卷版本)');
    console.log('- Users (用户)');

  } catch (error) {
    console.error('❌ 修复MySQL表失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('无法连接到MySQL数据库，请检查：');
      console.error('1. MySQL服务器是否正在运行');
      console.error('2. 连接参数是否正确');
      console.error('3. 网络连接是否正常');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('数据库访问被拒绝，请检查用户名和密码');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('数据库不存在，请先创建数据库:', mysqlConfig.database);
    } else {
      console.error('详细错误信息:', error);
    }
    
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 执行修复
console.log('开始修复MySQL数据库缺少的表...');
fixMySQLTables();
