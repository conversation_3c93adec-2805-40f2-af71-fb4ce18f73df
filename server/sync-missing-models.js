/**
 * 同步缺少的数据库模型
 * 
 * 此脚本用于：
 * 1. 使用Sequelize同步所有模型到数据库
 * 2. 只创建缺少的表，不会删除现有数据
 * 3. 支持MySQL和SQLite数据库
 */

const { sequelize, Clue, ChapterVersion, EditorComment, ReaderReview, Volume, VolumeChapterGroup, VolumeChapterGroupVersion, VolumeVersion, User } = require('./models');

async function syncMissingModels() {
  try {
    console.log('开始同步缺少的数据库模型...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 获取数据库配置信息
    const dialect = sequelize.getDialect();
    console.log(`📊 数据库类型: ${dialect}`);

    // 同步模型（只创建缺少的表，不删除现有数据）
    console.log('正在同步模型到数据库...');
    
    // 使用 alter: true 来更新表结构，但保留数据
    await sequelize.sync({ alter: true });
    
    console.log('✅ 数据库模型同步成功！');
    
    // 验证表是否创建成功
    console.log('正在验证表结构...');
    
    const queryInterface = sequelize.getQueryInterface();
    
    // 检查各个表是否存在
    const tablesToCheck = [
      'clues',
      'chapter_versions', 
      'editor_comments',
      'reader_reviews',
      'Volumes',
      'VolumeChapterGroups',
      'VolumeChapterGroupVersions',
      'VolumeVersions',
      'Users'
    ];
    
    for (const tableName of tablesToCheck) {
      try {
        await queryInterface.describeTable(tableName);
        console.log(`✅ 表 ${tableName} 存在`);
      } catch (error) {
        console.log(`❌ 表 ${tableName} 不存在或有问题:`, error.message);
      }
    }

    console.log('\n🎉 数据库修复完成！');
    console.log('现在可以重启服务器并使用线索管理功能了。');

  } catch (error) {
    console.error('❌ 同步数据库模型失败:', error.message);
    
    if (error.name === 'SequelizeConnectionError') {
      console.error('数据库连接失败，请检查：');
      console.error('1. 数据库服务器是否正在运行');
      console.error('2. 连接参数是否正确');
      console.error('3. 数据库是否存在');
    } else if (error.name === 'SequelizeAccessDeniedError') {
      console.error('数据库访问被拒绝，请检查用户名和密码');
    } else {
      console.error('详细错误信息:', error);
    }
    
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  }
}

// 执行同步
console.log('🔧 开始修复数据库缺少的表...');
syncMissingModels();
