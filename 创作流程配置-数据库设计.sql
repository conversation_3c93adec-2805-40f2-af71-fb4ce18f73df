-- 创作流程配置功能数据库设计

-- 1. 创作流程表
CREATE TABLE workflow_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL COMMENT '流程名称',
    description TEXT COMMENT '流程描述',
    category VARCHAR(100) COMMENT '流程分类',
    version VARCHAR(50) DEFAULT '1.0.0' COMMENT '版本号',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（admin可设置公开模板）',
    config JSON COMMENT '流程配置（节点、连接等）',
    thumbnail TEXT COMMENT '流程缩略图',
    tags JSON COMMENT '标签',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    user_id INT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) COMMENT='创作流程模板表';

-- 2. 流程节点表
CREATE TABLE workflow_nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    workflow_id INT NOT NULL COMMENT '流程ID',
    node_id VARCHAR(100) NOT NULL COMMENT '节点唯一标识',
    node_type ENUM('start', 'ai_generation', 'user_input', 'condition', 'end') NOT NULL COMMENT '节点类型',
    name VARCHAR(255) NOT NULL COMMENT '节点名称',
    description TEXT COMMENT '节点描述',
    position_x FLOAT COMMENT 'X坐标',
    position_y FLOAT COMMENT 'Y坐标',
    config JSON COMMENT '节点配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_templates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_node (workflow_id, node_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_node_type (node_type)
) COMMENT='流程节点表';

-- 3. 流程连接表
CREATE TABLE workflow_connections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    workflow_id INT NOT NULL COMMENT '流程ID',
    source_node_id VARCHAR(100) NOT NULL COMMENT '源节点ID',
    target_node_id VARCHAR(100) NOT NULL COMMENT '目标节点ID',
    source_handle VARCHAR(100) COMMENT '源连接点',
    target_handle VARCHAR(100) COMMENT '目标连接点',
    condition_config JSON COMMENT '连接条件配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_templates(id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_source_node (source_node_id),
    INDEX idx_target_node (target_node_id)
) COMMENT='流程连接表';

-- 4. 流程执行记录表
CREATE TABLE workflow_executions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    workflow_id INT NOT NULL COMMENT '流程ID',
    execution_id VARCHAR(100) NOT NULL COMMENT '执行ID',
    status ENUM('running', 'completed', 'failed', 'cancelled') DEFAULT 'running' COMMENT '执行状态',
    current_node_id VARCHAR(100) COMMENT '当前执行节点',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    execution_log JSON COMMENT '执行日志',
    error_message TEXT COMMENT '错误信息',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    user_id INT NOT NULL COMMENT '执行用户ID',
    project_id INT COMMENT '关联项目ID',
    FOREIGN KEY (workflow_id) REFERENCES workflow_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES Projects(id) ON DELETE SET NULL,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
) COMMENT='流程执行记录表';

-- 5. 节点执行记录表
CREATE TABLE workflow_node_executions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    execution_id VARCHAR(100) NOT NULL COMMENT '流程执行ID',
    node_id VARCHAR(100) NOT NULL COMMENT '节点ID',
    status ENUM('pending', 'running', 'completed', 'failed', 'skipped') DEFAULT 'pending' COMMENT '节点执行状态',
    input_data JSON COMMENT '节点输入数据',
    output_data JSON COMMENT '节点输出数据',
    ai_config_id INT COMMENT '使用的AI配置ID',
    prompt_template_id INT COMMENT '使用的提示词模板ID',
    execution_time_ms INT COMMENT '执行耗时（毫秒）',
    error_message TEXT COMMENT '错误信息',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    FOREIGN KEY (ai_config_id) REFERENCES ai_configs(id) ON DELETE SET NULL,
    FOREIGN KEY (prompt_template_id) REFERENCES PromptTemplates(id) ON DELETE SET NULL,
    INDEX idx_execution_id (execution_id),
    INDEX idx_node_id (node_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
) COMMENT='节点执行记录表';

-- 6. 流程版本表
CREATE TABLE workflow_versions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    workflow_id INT NOT NULL COMMENT '流程ID',
    version VARCHAR(50) NOT NULL COMMENT '版本号',
    config JSON COMMENT '版本配置快照',
    change_log TEXT COMMENT '变更日志',
    is_current BOOLEAN DEFAULT FALSE COMMENT '是否当前版本',
    created_by INT NOT NULL COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES Users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_version (workflow_id, version),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_is_current (is_current)
) COMMENT='流程版本表';

-- 7. 流程分类表
CREATE TABLE workflow_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_name (name)
) COMMENT='流程分类表';

-- 插入默认分类
INSERT INTO workflow_categories (name, description, icon, sort_order) VALUES
('小说创作', '完整的小说创作流程', 'book', 1),
('角色设计', '角色创建和发展流程', 'user', 2),
('世界构建', '世界观和设定构建流程', 'globe', 3),
('情节规划', '故事情节和大纲规划流程', 'timeline', 4),
('内容生成', '章节和内容生成流程', 'edit', 5),
('审核编辑', '内容审核和编辑流程', 'check', 6),
('自定义', '用户自定义流程', 'settings', 99);
