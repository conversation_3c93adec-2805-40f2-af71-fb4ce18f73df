# Kimi (月之暗面) 大模型集成说明

## 概述

已成功为AI小说创作应用集成了Kimi（月之暗面）大模型接口，现在支持DeepSeek和Kimi两个AI提供商。用户可以在大模型配置中添加Kimi配置，并在所有AI生成功能中使用Kimi模型。

## 新增功能

### 1. Kimi服务支持
- **新增服务类**: `server/services/kimiService.js`
- **API兼容**: 完全兼容OpenAI API格式
- **支持模型**: 
  - `moonshot-v1-8k` (8K上下文，默认)
  - `moonshot-v1-32k` (32K上下文)
  - `moonshot-v1-128k` (128K上下文)
- **功能特性**:
  - 文本生成
  - 流式生成
  - 系统提示词支持
  - API密钥验证

### 2. 前端配置界面增强
- **提供商选择**: 新增"Kimi (月之暗面)"选项
- **智能表单**: 根据选择的提供商自动调整默认值
- **动态占位符**: 根据提供商显示相应的提示信息
- **模型预设**: Kimi默认使用`moonshot-v1-8k`模型

### 3. 后端API扩展
- **配置管理**: 支持Kimi配置的创建、编辑、删除
- **连接测试**: 支持Kimi API连接测试
- **生成服务**: 支持Kimi的文本生成和流式生成
- **权限验证**: 完整的用户权限控制

## 使用指南

### 第一步：获取Kimi API密钥
1. 访问 [Moonshot AI 开放平台](https://platform.moonshot.cn/)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 复制API密钥备用

### 第二步：添加Kimi配置
1. 登录AI小说创作应用
2. 点击左侧菜单的"大模型配置"
3. 点击"添加配置"按钮
4. 填写配置信息：
   - **提供商**: 选择"Kimi (月之暗面)"
   - **模型名称**: `moonshot-v1-8k`（自动填充）
   - **API密钥**: 输入您的Kimi API密钥
   - **基础URL**: 留空（自动使用 https://api.moonshot.cn/v1）
   - **最大Token数**: 建议8000（根据模型调整）
   - **温度参数**: 建议0.7
   - **设为默认**: 可选择设为默认配置
5. 点击"保存"
6. 点击"测试连接"验证配置

### 第三步：使用Kimi生成内容
1. 在任何支持AI生成的页面（世界观设定、角色管理、章节分组等）
2. 点击"AI生成"按钮
3. 在AI生成对话框中：
   - **AI配置**: 选择您的Kimi配置
   - **提示词模板**: 选择合适的模板
   - **配置参数**: 填写相关参数
4. 点击"生成内容"开始生成

## 技术实现

### 1. Kimi服务类 (`kimiService.js`)
```javascript
class KimiService {
  constructor(apiKey, baseUrl = 'https://api.moonshot.cn/v1') {
    // 初始化Kimi API客户端
  }
  
  async generateText(prompt, options) {
    // 文本生成
  }
  
  async generateTextStream(prompt, options, onChunk) {
    // 流式生成
  }
  
  async validateApiKey() {
    // API密钥验证
  }
}
```

### 2. 前端配置界面
- **动态表单**: 根据提供商自动调整表单字段
- **智能提示**: 提供商特定的占位符和帮助信息
- **用户体验**: 无缝切换不同AI提供商

### 3. 后端集成
- **统一接口**: 通过统一的AI生成接口支持多个提供商
- **服务抽象**: 抽象化AI服务，便于扩展新的提供商
- **错误处理**: 完善的错误处理和用户反馈

## 支持的功能页面

所有现有的AI生成功能都已支持Kimi：

1. **世界观设定**: AI生成世界观内容
2. **角色管理**: AI生成角色描述
3. **大纲管理**: AI生成大纲内容
4. **章节管理**: AI生成章节内容
5. **分卷管理**: AI生成分卷梗概
6. **章节分组管理**: AI生成章节分组梗概
7. **线索管理**: AI生成线索描述
8. **提示词生成器**: 使用Kimi生成提示词

## 配置对比

| 特性 | DeepSeek | Kimi |
|------|----------|------|
| 默认模型 | deepseek-chat | moonshot-v1-8k |
| 默认Token数 | 4000 | 8000 |
| API地址 | api.deepseek.com | api.moonshot.cn |
| 上下文长度 | 4K | 8K/32K/128K |
| 流式生成 | ✅ | ✅ |
| 系统提示词 | ✅ | ✅ |

## 注意事项

1. **API密钥安全**: 请妥善保管您的Kimi API密钥
2. **Token限制**: 注意不同模型的Token限制和费用
3. **网络连接**: 确保服务器能够访问Moonshot AI的API
4. **模型选择**: 根据需要选择合适的上下文长度模型
5. **费用控制**: 监控API使用量，避免超出预算

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API配额是否充足

2. **生成内容失败**
   - 检查Token数设置是否合理
   - 确认提示词长度不超过限制
   - 验证模型名称是否正确

3. **流式生成中断**
   - 检查网络稳定性
   - 确认服务器资源充足
   - 验证API响应格式

### 日志查看
- 后端日志会显示详细的Kimi API调用信息
- 前端控制台会显示错误信息和调试信息

## 更新日志

### v1.2.0 (当前版本)
- ✅ 新增Kimi (月之暗面) 大模型支持
- ✅ 完整的Kimi API集成
- ✅ 前端配置界面支持Kimi
- ✅ 所有AI生成功能支持Kimi
- ✅ 流式生成支持
- ✅ API密钥验证
- ✅ 连接测试功能
- ✅ 完善的错误处理

### v1.1.0
- ✅ DeepSeek大模型集成
- ✅ 提示词模板参数化
- ✅ 系统参数支持

### v1.0.0
- ✅ 基础AI生成功能

## 扩展计划

未来可以考虑集成更多AI提供商：
- OpenAI GPT系列
- Anthropic Claude
- 百度文心一言
- 阿里通义千问
- 腾讯混元

通过统一的服务抽象层，可以轻松添加新的AI提供商支持。
