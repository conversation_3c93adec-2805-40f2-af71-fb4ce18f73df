# 小说预览功能使用说明

## 功能概述

小说预览功能为AI小说创作应用添加了一个专业的阅读器，支持仿真阅读体验，让用户可以像阅读真实书籍一样预览创作的小说内容。

## 主要特性

### 🎨 仿真阅读体验
- **多种阅读主题**：明亮、护眼、夜间、羊皮纸四种主题
- **书籍样式界面**：模拟真实书籍的阅读体验
- **翻页效果**：支持键盘快捷键和按钮翻页
- **全屏阅读**：支持全屏模式，专注阅读体验

### 📖 阅读功能
- **章节导航**：左侧目录栏，快速跳转到任意章节
- **阅读进度**：底部进度条显示当前阅读进度
- **字体调节**：支持字体大小、行间距、页面宽度调节
- **响应式设计**：适配桌面和移动设备

### ⚙️ 个性化设置
- **阅读设置面板**：右侧抽屉式设置面板
- **主题切换**：一键切换不同阅读主题
- **布局调整**：可隐藏/显示目录栏
- **快捷键支持**：键盘快捷键操作

## 使用方法

### 1. 进入预览模式
1. 在项目管理页面，找到要预览的项目
2. 点击项目操作栏中的"预览小说"按钮
3. 系统会自动跳转到小说阅读器页面

### 2. 阅读操作
- **翻页**：
  - 点击左右两侧的翻页按钮
  - 使用键盘左右箭头键
  - 拖动底部进度条
- **目录导航**：点击左侧目录中的章节标题
- **全屏模式**：点击工具栏中的全屏按钮，或按F11键

### 3. 个性化设置
1. 点击工具栏中的"设置"按钮
2. 在设置面板中调整：
   - 阅读主题（明亮/护眼/夜间/羊皮纸）
   - 字体大小（12-24px）
   - 行间距（1.2-2.5倍）
   - 页面宽度（600-1200px）
   - 目录显示/隐藏

### 4. 快捷键
- `←` / `→`：上一章/下一章
- `F11`：切换全屏模式
- `Esc`：退出全屏模式

## 技术实现

### 后端API
- **路由**：`GET /api/projects/:id/preview`
- **功能**：获取项目的完整章节内容和分卷信息
- **权限**：需要登录认证，支持用户权限控制

### 前端组件
- **组件路径**：`client/src/views/NovelReader.vue`
- **路由**：`/novel-reader/:projectId`
- **依赖**：Vue 3 + Element Plus

### 数据结构
```javascript
{
  project: {
    id: number,
    name: string,
    type: string,
    targetAudience: string,
    style: string
  },
  chapters: [
    {
      id: number,
      title: string,
      content: string,
      order: number,
      status: string
    }
  ],
  volumes: [
    {
      id: number,
      title: string,
      summary: string,
      order: number
    }
  ]
}
```

## 样式主题

### 明亮主题
- 背景：白色 (#ffffff)
- 文字：深灰色 (#333333)
- 适合：日间阅读

### 护眼主题
- 背景：淡绿色 (#f0f8e8)
- 文字：深绿色 (#2d5016)
- 适合：长时间阅读

### 夜间主题
- 背景：深灰色 (#1a1a1a)
- 文字：浅灰色 (#e0e0e0)
- 适合：夜间阅读

### 羊皮纸主题
- 背景：米黄色 (#f4f1e8)
- 文字：棕色 (#5d4e37)
- 适合：复古风格阅读

## 响应式设计

### 桌面端（>768px）
- 左侧固定目录栏
- 中央阅读区域
- 左右翻页按钮
- 完整工具栏

### 移动端（≤768px）
- 隐藏式目录栏（滑动显示）
- 全屏阅读区域
- 隐藏翻页按钮（使用滑动操作）
- 简化工具栏

## 注意事项

1. **章节内容**：确保项目中的章节有实际内容，否则会显示"暂无内容"
2. **权限控制**：只能预览用户有权限访问的项目
3. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
4. **性能优化**：大量章节时会自动优化加载性能

## 扩展功能建议

- [ ] 书签功能
- [ ] 阅读笔记
- [ ] 语音朗读
- [ ] 离线阅读
- [ ] 分享功能
- [ ] 阅读统计

## 故障排除

### 常见问题
1. **无法加载章节内容**：检查项目是否有章节数据
2. **预览按钮无响应**：检查网络连接和登录状态
3. **样式显示异常**：清除浏览器缓存并刷新页面
4. **全屏模式异常**：检查浏览器是否支持全屏API
5. **Slider错误**：已修复空项目时的进度条错误

### 错误修复记录
- **v1.1** - 修复了当项目没有章节时，进度条组件报错的问题
- **v1.1** - 改进了空项目的用户体验，显示友好的提示信息
- **v1.1** - 增强了边界条件处理，避免数组越界错误

### 调试方法
1. 打开浏览器开发者工具
2. 查看控制台错误信息
3. 检查网络请求状态
4. 验证API响应数据格式

### 空项目处理
当项目没有章节时，阅读器会：
- 隐藏进度条和翻页按钮
- 在目录中显示"暂无章节内容"
- 在阅读区域显示"暂无章节内容"的提示
- 在工具栏显示"暂无章节"状态
